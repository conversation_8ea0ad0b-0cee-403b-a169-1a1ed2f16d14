<?php

namespace Butler\Models;

use Butler\Models\Driver\DriverDevice;

/**
 * @property integer $id
 * @property integer $user_id
 * @property integer $device_id
 * @property string $home_address
 * @property float $home_lat
 * @property float $home_lng
 * @property string $updated_at
 * @property integer $home_geofence_radius
 * @property string $home_geofence_center
 *
 * @property DriverDevice $driverDevice
 * @property User $user
 */
class UserProfileHomeHistory extends BaseModel
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'user_profile_home_history';

    /**
     * @var array
     */
    protected $fillable = ['user_id', 'device_id', 'home_address', 'home_lat', 'home_lng', 'updated_at', 'home_geofence_radius', 'home_geofence_center'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function driverDevice()
    {
        return $this->belongsTo('Butler\Models\Driver\DriverDevice', 'device_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo('Butler\Models\User');
    }
}
