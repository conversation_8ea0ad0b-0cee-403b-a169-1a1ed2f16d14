<?php

namespace Butler\Models;

use Butler\Models\Dispatch\Dispatch;
use Butler\Models\Tenant\TenantAddress;
use Butler\Models\Twilio\TwilioCallLogs;
use Butler\Models\Twilio\TwilioPhoneNumbers;
use Butler\Models\Twilio\TwilioSmsLogs;
use Butler\Models\Twilio\TwilioSmsTemplates;

/**
 * @property integer $id
 * @property string $tenant_name
 * @property string $base_url
 * @property string $tenant_logo
 * @property string $created_at
 * @property string $updated_at
 * @property integer $external_id
 * @property string $source
 *
 * @property Dispatch[] $dispatches
 * @property FrontdoorRequest[] $frontdoorRequests
 * @property SystemApiKey[] $systemApiKeys
 * @property TenantAddress[] $tenantAddresses
 * @property TenantRole[] $tenantRoles

 * @property TwilioCallLogs[] $twilioCallLogs
 * @property TwilioPhoneNumbers[] $twilioPhoneNumbers
 * @property TwilioSmsLogs[] $twilioSmsLogs
 * @property TwilioSmsTemplates[] $twilioSmsTemplates
 * @property UserType[] $userTypes
 */
class Tenant extends BaseModel
{
    /**
     * @var array
     */
    protected $fillable = ['tenant_name', 'base_url', 'tenant_logo', 'created_at', 'updated_at', 'external_id', 'source'];

    /**
     * Find a tenant by ID
     *
     * @param int $id
     * @return Tenant|null
     */
    public static function findById($id)
    {
        return self::find($id);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function dispatches()
    {
        return $this->hasMany('Butler\Models\Dispatch');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function frontdoorRequests()
    {
        return $this->hasMany('Butler\Models\FrontdoorRequest');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function systemApiKeys()
    {
        return $this->hasMany('Butler\Models\SystemApiKey');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function dispatchContracts()
    {
        return $this->hasMany('Butler\Models\Dispatch\DispatchContract');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function addresses()
    {
        return $this->hasMany('Butler\Models\Tenant\TenantAddress');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function billingAddress()
    {
        return $this->hasOne('Butler\Models\Tenant\TenantAddress')->where('address_type', TenantAddress::ADDRESS_TYPE_BILLING);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function tenantRoles()
    {
        return $this->hasMany('Butler\Models\TenantRole');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function twilioCallLogs()
    {
        return $this->hasMany(TwilioCallLogs::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function twilioPhoneNumbers()
    {
        return $this->hasMany(TwilioPhoneNumbers::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function twilioSmsLogs()
    {
        return $this->hasMany(TwilioSmsLogs::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function twilioSmsTemplates()
    {
        return $this->hasMany(TwilioSmsTemplates::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function userTypes()
    {
        return $this->hasMany('Butler\Models\UserType');
    }

}
