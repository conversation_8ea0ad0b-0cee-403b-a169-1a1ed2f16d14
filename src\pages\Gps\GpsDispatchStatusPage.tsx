import React from 'react';
import { Form, Input, Button, Select, DatePicker, Card, message } from 'antd';
import { PageContainer, ProLayout } from '@ant-design/pro-components';
interface FormField {
    name: string;
    label: string;
    type: 'text' | 'email' | 'select' | 'date';
    required?: boolean;
    options?: Array<{ label: string; value: any }>;
}
interface GpsDispatchStatusPageProps {
}
const GpsDispatchStatusPage: React.FC<GpsDispatchStatusPageProps> = (props) => {
    return (
        <ProLayout>
            <PageContainer>
                <Card title="Test"></Card>
            </PageContainer>
        </ProLayout>
    );
};
export default GpsDispatchStatusPage;