<?php

namespace Butler\Controllers;

use <PERSON>\Helpers\Func;

trait BaseControllerTraits
{
    public ?string $controllerBaseUrl = null;

    public ?int $tenant_id = 0; // Current Tenant ID.
    public ?int $user_id = 0; // Current User ID.
    public mixed $data = null;  // Request data

    public function __construct()
    {
        $auth = Func::getAuthInContainer();

        $this->tenant_id = intval($auth['cur_tenant_id'] ?? 0);
        $this->user_id = intval($auth['user_id'] ?? 0);
        $this->data = json_decode(file_get_contents('php://input'), true) ?? $_REQUEST;
    }

    public function json(mixed $data = [])
    {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    public function jsonWithSuccess(mixed $data = [])
    {
        $this->json(['success' => true, 'data' => $data]);
    }


    /**
     * Return Error in JSON.
     *
     * @param \Exception $error
     * @param $errorCode
     * @param $errorMessage
     * @return void
     */
    public function jsonWithError(\Exception $error, $errorCode = 500, $errorMessage = null)
    {
        http_response_code($errorCode);
        $this->json(['success' => false, 'message' => $errorMessage ?? $error?->getMessage()]);
    }
}