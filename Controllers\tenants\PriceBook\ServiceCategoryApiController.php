<?php

namespace Butler\Controllers\tenants\PriceBook;

use Butler\Controllers\tenants\TenantBaseController;
use Butler\Helpers\SysMsg;
use Butler\Models\ButlerDB;
use Butler\Models\PriceBook\PbServiceCategory;
use Exception;

class ServiceCategoryApiController extends TenantBaseController
{
    public function __construct()
    {
        parent::__construct();
    }


    /**
     * function buildTree  (Criar árvore com os elementos)
     * @param array $elements
     * @param array $options['parent_id_column_name', 'children_key_name', 'id_column_name']
     * @param int $parentId
     * @return array
     */
    function buildTree(array $elements, $options = [
        'parent_id_column_name' => 'parent_id',
        'children_key_name' => 'children',
        'id_column_name' => 'id'], $parentId = 0)
    {
        $branch = array();
        foreach ($elements as $element) {
            if ($element[$options['parent_id_column_name']] == $parentId) {
                $children = $this->buildTree($elements, $options, $element[$options['id_column_name']]);
                if ($children) {
                    $element[$options['children_key_name']] = $children;
                }
                $branch[] = $element;
            }
        }
        return $branch;
    }


    /**
     * Getting categories tree list.
     *
     * @return void
     */
    public function index()
    {
        $action = $_GET['action'] ?? '';
        switch ($action) {
            case 'load':
                $qb = PbServiceCategory::query()
                    ->select('pb_service_category.*')
                    ->selectRaw('pb_service_category.name AS text')
                    ->where('tenant_id', $this->tenant_id)
                    ->orderBy('position')
                    ->orderBy('name')
                    ;

                if ('#' !== ($_GET['id'] ?? null)) {
                    $qb->where('parent_id', $_GET['id']);
                } else {
                    // $qb->whereNull('parent_id');
                }

                $rows = $qb->get()->toArray();
                $tree = $this->buildTree($rows);

                $this->json($tree);
                break;
            case 'create':
                $parent = $_GET['parent'] === "#" ? NULL : (int)$_GET['parent'];

                $input = [
                    'name' => $_GET['name'] ?? '',
                    'position' => intval($_GET['position'] ?? 0),
                    'tenant_id' => $this->tenant_id,
                ];
                if ($parent) {
                    $input['parent_id'] = $parent;
                }

                $row = PbServiceCategory::create($input);

                echo json_encode(['id' => $row->id]);
                break;
            case 'rename':
                $id = (int)$_GET['id'];
                $row = PbServiceCategory::findOrFail($id);
                $row->update($_GET);
                break;

            case 'delete':
                $id = (int)$_GET['id'];

                function delete_recursive($id) {
                    $ids = PbServiceCategory::query()->where('parent_id', $id)->pluck('id')->toArray();
                    foreach ($ids as $x) {
                        delete_recursive($x);
                    }
                    PbServiceCategory::query()->where('id', $id)->delete();
                }
                delete_recursive($id);
                break;

            case 'move':
                $id = (int)$_GET['id'];
                $parent = $_GET['parent'] === "#" ? NULL : (int)$_GET['parent'];
                $position = (int)$_GET['position'];

                /** @var PbServiceCategory $row */
                $row = PbServiceCategory::query()
                    ->where('id', $id)
                    ->where('tenant_id', $this->tenant_id)
                    ->firstOrFail()
                ;

                $row->update([
                    'parent_id' => $parent,
                    'position' => $position,
                ]);

                break;

            default:
                $this->json([]);
        }
    }

    public function edit()
    {
        if (!isset($_GET['id'])) {
            throw new Exception('ID is required!');
        }

        $id = intval($_GET['id']);

        if (isset($_POST['name'])) {
            $name = trim($_POST['name'] ?? '');
            if (empty($name)) {
                throw new Exception('Please fill in required fields.');
            } else {
                try {
                    $qb = PbServiceCategory::query()->where('tenant_id', $this->tenant_id);
                    $qb->where('id', $id);

                    $row = $qb->firstOrFail();
                    $row->update($_POST);
                } catch (Exception $e) {
                    SysMsg::get_instance()->error("Could not update category: " . $e->getMessage());
                }
            }
        }

        $dbRow = PbServiceCategory::findOrFail($id)?->toArray();

        $this->json($dbRow);
    }

    public function delete()
    {
        $tenant_id = $this->tenant_id;
        $db = ButlerDB::getMainInstance();

        if (!isset($_GET['id']) || !isset($_GET['h'])) {
            $this->redirect();
        }
        $id = intval($_GET['id']);
        if (md5("uid$id") != $_GET['h']) {
            $this->redirect();
        }

        $stmt = $db->prepare("
    DELETE FROM pb_service_category WHERE id=? AND tenant_id=?
");
        $stmt->execute([$id, $tenant_id]);

        SysMsg::get_instance()->success("Deleted successfully.");
        $this->redirect();
    }
}