/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("react"), require("react-dom"));
	else if(typeof define === 'function' && define.amd)
		define(["react", "react-dom"], factory);
	else if(typeof exports === 'object')
		exports["CustomForm"] = factory(require("react"), require("react-dom"));
	else
		root["CustomForm"] = factory(root["React"], root["ReactDOM"]);
})(this, (__WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_dom__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/pages/CustomForm/index.tsx":
/*!****************************************!*\
  !*** ./src/pages/CustomForm/index.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var antd_es_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! antd/es/button */ \"./node_modules/antd/es/button/index.js\");\n/* harmony import */ var antd_es_date_picker__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! antd/es/date-picker */ \"./node_modules/antd/es/date-picker/index.js\");\n/* harmony import */ var antd_es_select__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! antd/es/select */ \"./node_modules/antd/es/select/index.js\");\n/* harmony import */ var antd_es_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! antd/es/input */ \"./node_modules/antd/es/input/index.js\");\n/* harmony import */ var antd_es_message__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! antd/es/message */ \"./node_modules/antd/es/message/index.js\");\n/* harmony import */ var antd_es_form__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! antd/es/form */ \"./node_modules/antd/es/form/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst CustomForm = ({\n  title = 'Custom Form',\n  fields = [],\n  layout = 'vertical',\n  onSubmit\n}) => {\n  const [form] = antd_es_form__WEBPACK_IMPORTED_MODULE_2__[\"default\"].useForm();\n  const defaultFields = [{\n    name: 'name',\n    label: 'Name',\n    type: 'text',\n    required: true\n  }, {\n    name: 'email',\n    label: 'Email',\n    type: 'email',\n    required: true\n  }, {\n    name: 'phone',\n    label: 'Phone',\n    type: 'text'\n  }];\n  const formFields = fields.length > 0 ? fields : defaultFields;\n  const handleSubmit = () => {\n    const values = form.getFieldsValue();\n    console.log('Form Values', values);\n    if (onSubmit) {\n      onSubmit(values);\n    } else {\n      antd_es_message__WEBPACK_IMPORTED_MODULE_3__[\"default\"].success('Form submitted successfully!');\n      console.log('Form values:', values);\n    }\n  };\n  const renderField = field => {\n    const rules = field.required ? [{\n      required: true,\n      message: `Please input ${field.label}!`\n    }] : [];\n    switch (field.type) {\n      case 'email':\n        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd_es_form__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Item, {\n          name: field.name,\n          label: field.label,\n          rules: [...rules, {\n            type: 'email',\n            message: 'Please enter a valid email!'\n          }],\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd_es_input__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {})\n        }, field.name);\n      case 'select':\n        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd_es_form__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Item, {\n          name: field.name,\n          label: field.label,\n          rules: rules,\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd_es_select__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            children: field.options?.map(option => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd_es_select__WEBPACK_IMPORTED_MODULE_5__[\"default\"].Option, {\n              value: option.value,\n              children: option.label\n            }, option.value))\n          })\n        }, field.name);\n      case 'date':\n        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd_es_form__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Item, {\n          name: field.name,\n          label: field.label,\n          rules: rules,\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd_es_date_picker__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            style: {\n              width: '100%'\n            }\n          })\n        }, field.name);\n      default:\n        return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd_es_form__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Item, {\n          name: field.name,\n          label: field.label,\n          rules: rules,\n          children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd_es_input__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {})\n        }, field.name);\n    }\n  };\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(antd_es_form__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n    form: form,\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"h1\", {\n      children: \"My Form\"\n    }), formFields.map(renderField), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(antd_es_form__WEBPACK_IMPORTED_MODULE_2__[\"default\"].Item, {\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd_es_button__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        type: \"primary\",\n        style: {\n          marginLeft: 8\n        },\n        onClick: () => handleSubmit(),\n        children: \"Submit\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd_es_button__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n        style: {\n          marginLeft: 8\n        },\n        onClick: () => form.resetFields(),\n        children: \"Reset\"\n      })]\n    })]\n  });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CustomForm);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/CustomForm/index.tsx\n");

/***/ }),

/***/ "react":
/*!**************************************************************************************!*\
  !*** external {"root":"React","commonjs2":"react","commonjs":"react","amd":"react"} ***!
  \**************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

/***/ }),

/***/ "react-dom":
/*!*****************************************************************************************************!*\
  !*** external {"root":"ReactDOM","commonjs2":"react-dom","commonjs":"react-dom","amd":"react-dom"} ***!
  \*****************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			loaded: false,
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Flag the module as loaded
/******/ 		module.loaded = true;
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = __webpack_modules__;
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/chunk loaded */
/******/ 	(() => {
/******/ 		var deferred = [];
/******/ 		__webpack_require__.O = (result, chunkIds, fn, priority) => {
/******/ 			if(chunkIds) {
/******/ 				priority = priority || 0;
/******/ 				for(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];
/******/ 				deferred[i] = [chunkIds, fn, priority];
/******/ 				return;
/******/ 			}
/******/ 			var notFulfilled = Infinity;
/******/ 			for (var i = 0; i < deferred.length; i++) {
/******/ 				var [chunkIds, fn, priority] = deferred[i];
/******/ 				var fulfilled = true;
/******/ 				for (var j = 0; j < chunkIds.length; j++) {
/******/ 					if ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {
/******/ 						chunkIds.splice(j--, 1);
/******/ 					} else {
/******/ 						fulfilled = false;
/******/ 						if(priority < notFulfilled) notFulfilled = priority;
/******/ 					}
/******/ 				}
/******/ 				if(fulfilled) {
/******/ 					deferred.splice(i--, 1)
/******/ 					var r = fn();
/******/ 					if (r !== undefined) result = r;
/******/ 				}
/******/ 			}
/******/ 			return result;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/global */
/******/ 	(() => {
/******/ 		__webpack_require__.g = (function() {
/******/ 			if (typeof globalThis === 'object') return globalThis;
/******/ 			try {
/******/ 				return this || new Function('return this')();
/******/ 			} catch (e) {
/******/ 				if (typeof window === 'object') return window;
/******/ 			}
/******/ 		})();
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/harmony module decorator */
/******/ 	(() => {
/******/ 		__webpack_require__.hmd = (module) => {
/******/ 			module = Object.create(module);
/******/ 			if (!module.children) module.children = [];
/******/ 			Object.defineProperty(module, 'exports', {
/******/ 				enumerable: true,
/******/ 				set: () => {
/******/ 					throw new Error('ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: ' + module.id);
/******/ 				}
/******/ 			});
/******/ 			return module;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/jsonp chunk loading */
/******/ 	(() => {
/******/ 		// no baseURI
/******/ 		
/******/ 		// object to store loaded and loading chunks
/******/ 		// undefined = chunk not loaded, null = chunk preloaded/prefetched
/******/ 		// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded
/******/ 		var installedChunks = {
/******/ 			"CustomForm": 0
/******/ 		};
/******/ 		
/******/ 		// no chunk on demand loading
/******/ 		
/******/ 		// no prefetching
/******/ 		
/******/ 		// no preloaded
/******/ 		
/******/ 		// no HMR
/******/ 		
/******/ 		// no HMR manifest
/******/ 		
/******/ 		__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);
/******/ 		
/******/ 		// install a JSONP callback for chunk loading
/******/ 		var webpackJsonpCallback = (parentChunkLoadingFunction, data) => {
/******/ 			var [chunkIds, moreModules, runtime] = data;
/******/ 			// add "moreModules" to the modules object,
/******/ 			// then flag all "chunkIds" as loaded and fire callback
/******/ 			var moduleId, chunkId, i = 0;
/******/ 			if(chunkIds.some((id) => (installedChunks[id] !== 0))) {
/******/ 				for(moduleId in moreModules) {
/******/ 					if(__webpack_require__.o(moreModules, moduleId)) {
/******/ 						__webpack_require__.m[moduleId] = moreModules[moduleId];
/******/ 					}
/******/ 				}
/******/ 				if(runtime) var result = runtime(__webpack_require__);
/******/ 			}
/******/ 			if(parentChunkLoadingFunction) parentChunkLoadingFunction(data);
/******/ 			for(;i < chunkIds.length; i++) {
/******/ 				chunkId = chunkIds[i];
/******/ 				if(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {
/******/ 					installedChunks[chunkId][0]();
/******/ 				}
/******/ 				installedChunks[chunkId] = 0;
/******/ 			}
/******/ 			return __webpack_require__.O(result);
/******/ 		}
/******/ 		
/******/ 		var chunkLoadingGlobal = this["webpackChunkcustom_components_builder"] = this["webpackChunkcustom_components_builder"] || [];
/******/ 		chunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));
/******/ 		chunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module depends on other loaded chunks and execution need to be delayed
/******/ 	var __webpack_exports__ = __webpack_require__.O(undefined, ["vendors"], () => (__webpack_require__("./src/pages/CustomForm/index.tsx")))
/******/ 	__webpack_exports__ = __webpack_require__.O(__webpack_exports__);
/******/ 	__webpack_exports__ = __webpack_exports__["default"];
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});