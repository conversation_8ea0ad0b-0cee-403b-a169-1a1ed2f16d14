<?php

namespace Butler\Models\Dispatch;

use Butler\Helpers\Func;
use Butler\Models\BaseModel;
use Butler\Models\Customer\Customer;
use Butler\Models\Customer\CustomerProperty;
use Butler\Models\Tenant;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;


/**
 * @property integer $id
 * @property integer $tenant_id
 * @property integer $source_org_id
 * @property integer $external_id
 * @property string $dispatchType
 * @property string $trade
 * @property string $priority
 * @property string $date
 * @property string $isAuthoRequired
 * @property string $created_at
 * @property string $updated_at
 *
 * @property DispatchComment[] $dispatchComments
 * @property DispatchContract[] $dispatchContracts
 * @property DispatchCoverageNote[] $dispatchCoverageNotes
 * @property DispatchCoverage[] $dispatchCoverages
 * @property DispatchCustomer[] $dispatchCustomers
 * @property DispatchCustomerProperty[] $dispatchCustomerProperties
 * @property DispatchItemSymptom[] $dispatchItemSymptoms
 * @property DispatchItem[] $dispatchItems
 * @property DispatchLog[] $dispatchLogs
 * @property DispatchMessage[] $dispatchMessages
 * @property DispatchNcc[] $dispatchNccs
 * @property DispatchPayment[] $dispatchPayments
 * @property DispatchOrg $dispatchOrg
 * @property Tenant $tenant
 * @property DispatchUser[] $dispatchUsers
 * @property DispatchEstimate[] $estimates
 *
 *      ----- By Patryk ----
 * @property Customer $customer         // First customer entry.
 * @property CustomerProperty[] $addresses         // Addresses
 *      ----- End of Patryk ----
 */
class Dispatch extends BaseModel
{
    /**
     * @var array
     */
    protected $fillable = ['tenant_id', 'source_org_id', 'external_id', 'dispatchType', 'trade', 'priority', 'date', 'isAuthoRequired', 'job_source', 'env', 'created_at', 'updated_at'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function dispatchContracts()
    {
        return $this->hasMany(DispatchContract::class, 'dispatch_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function dispatchCoverageNotes()
    {
        return $this->hasMany(DispatchCoverageNote::class, 'dispatch_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function dispatchCoverages()
    {
        return $this->hasMany(DispatchCoverage::class, 'dispatch_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function dispatchCustomers()
    {
        return $this->hasMany(DispatchCustomer::class, 'dispatch_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function estimates()
    {
        return $this->hasMany('Butler\Models\Dispatch\DispatchEstimate');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function dispatchItemSymptoms()
    {
        return $this->hasMany(DispatchItemSymptom::class, 'dispatch_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function dispatchItems()
    {
        return $this->hasMany(DispatchItem::class, 'dispatch_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function dispatchNccs()
    {
        return $this->hasMany(DispatchNcc::class, 'dispatch_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function dispatchPayments()
    {
        return $this->hasMany(DispatchPayment::class, 'dispatch_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function dispatchOrg()
    {
        return $this->belongsTo(DispatchOrg::class, 'source_org_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function tenant()
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\hasOne
     */
    public function dispatchStatus()
    {
        return $this->hasOne(DispatchStatus::class, 'dispatch_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\hasOne
     */
    public function dispatchAppointment()
    {
        return $this->hasOne(DispatchAppointment::class, 'dispatch_id');
    }

    /**
     * Get the user associated with this dispatch.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function dispatchUser()
    {
        return $this->hasOne(DispatchUser::class, 'dispatch_id');
    }

    /**
     * Get the user associated with this dispatch.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function dispatchHightlights()
    {
        return $this->hasMany(DispatchHighlight::class, 'dispatch_id');
    }

    /**
     * Associate a user with this dispatch.
     *
     * @param int $userId
     * @return \Butler\Models\Dispatch\DispatchUser
     */
    public function associateUser($userId)
    {
        // Delete any existing association first to maintain uniqueness
        DispatchUser::where('dispatch_id', $this->id)->delete();

        // Create new association
        return DispatchUser::create([
            'dispatch_id' => $this->id,
            'user_id' => $userId
        ]);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function dispatchCustomerProperties()
    {
        return $this->hasMany(DispatchCustomerProperty::class, 'dispatch_id')
            ->whereIn('customer_id', $this->dispatchCustomers()->pluck('customer_id'));
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOneThrough
     */
    public function customer()
    {
        return $this->hasOneThrough(Customer::class, DispatchCustomer::class, 'dispatch_id', 'id', 'id', 'customer_id');
    }

    /**
     * @return Builder|BelongsToMany
     */
    public function addresses()
    {
        return $this->belongsToMany(
            CustomerProperty::class,
            DispatchCustomerProperty::class,
            'dispatch_id',
            'customer_id'
        )
            // IMPORTANT This is restricted mode to correct address mapping
            // Later we can use this mode to verify the data consistency of dispatch_customer_property table.
            /*->whereExists(function (\Illuminate\Database\Query\Builder $builder) {
                return $builder->from('dispatch_customers')
                    ->selectRaw("1")
                    ->whereColumn('dispatch_customers.customer_id', 'dispatch_customer_property.customer_id')
                    ->whereColumn('dispatch_customers.dispatch_id', 'dispatch_customer_property.dispatch_id')
                    ->whereColumn('customer_properties.id', 'dispatch_customer_property.property_id');

                return $builder;
            })*/ ;
    }

    public function dispatchMeta() {
        return $this->hasMany(DispatchMeta::class, 'dispatch_id');
    }

    public static function searchDispatches(
        $tenantId,
        $assigned_user = null,
        $date = null,
        $startDate = null,
        $endDate = null,
        $job_status = null, // Dispatch Assigned, Dispatch Accepted
        $appointment_status = null,
        $params = []
    )
    {
        $query = Dispatch::query()->whereHas('dispatchStatus', function ($q) use ($job_status) {
            $q->whereIn('job_status', $job_status);
        })->with([
            'dispatchContracts',
            'dispatchCoverageNotes',
            'dispatchCoverages',
            'dispatchHightlights',
            'dispatchCustomerProperties',
            'dispatchCustomers.customer.customerProperties.dispatchCustomerProperties',
            'dispatchCustomers.customer.customerProperties.customerPropertyGeolocation',
            'dispatchCustomers.customer.customerPhones',
            'dispatchItemSymptoms',
            'dispatchItems' => function ($builder) {
                if (Func::keyExistsInWithParam($_REQUEST, 'dispatchItems.symptoms')) {
                    $builder->with('symptoms');
                }
            },
            'dispatchNccs',
            'dispatchPayments',
            'dispatchOrg',
            'dispatchStatus',
            'dispatchAppointment',
            'dispatchUser',
            'dispatchMeta'
        ]);

        if ($tenantId) {
            $query->where('tenant_id', $tenantId);
        }

        if ($assigned_user) {
            if ($assigned_user != "all" && $assigned_user != "unassigned") {
                $query->whereHas('dispatchUser', function ($q) use ($assigned_user) {
                    $q->whereIn('user_id', $assigned_user);
                });
            }

            if ($assigned_user == "unassigned") {
                $query->whereDoesntHave('dispatchUser');
            }
        }

        if ($date) {
            // Filter by appointment date
            $query->whereHas('dispatchAppointment', function ($q) use ($date) {
                $q->where('appointment_date', $date);
            });
        }

        if ($startDate && $endDate) {
            // Filter by appointment date
            $query->whereHas('dispatchAppointment', function ($q) use ($startDate, $endDate) {
                $q->whereBetween('appointment_date', [$startDate, $endDate]);
            });
        }

        if (is_array($appointment_status) && count($appointment_status)) {
            // Filter by appointment status            
            $query->whereHas('dispatchStatus', function ($q) use ($appointment_status) {
                $q->whereIn('appointment_status', $appointment_status);
            });
        }

        if (Func::keyExistsInWithParam($params, 'customer')) {
            $query->with('customer', function($query) use (&$params) {
                if (Func::keyExistsInWithParam($params, 'customer.customerPhones')) {
                    $query->with('customerPhones');
                }
            });
        }

        if (Func::keyExistsInWithParam($params, 'addresses')) {
            $query->with('addresses', function($query) use (&$params) {
                if (Func::keyExistsInWithParam($params, 'addresses.customerPropertyGeolocation')) {
                    $query->with('customerPropertyGeolocation');
                }
            });
        }
        return $query->get();
    }


    public static function searchParkingDispatches(
        $tenantId,
        $assigned_user = null,
        $date = null,
        $startDate = null,
        $endDate = null,
        $job_status = [DispatchStatus::STATUS_JOB_ON_HOLD],
        $park_status = null
    ) {
        $query = Dispatch::query()->whereHas('dispatchStatus', function($q) use ($job_status, $park_status) {            
            $q->whereIn('job_status', $job_status);
            if ($park_status) {
                $q->whereIn('parking_status', $park_status);
            }
        })

        ->with([
            'dispatchContracts',
            'dispatchCoverageNotes',
            'dispatchCoverages',
            'dispatchHightlights',
            'dispatchCustomerProperties',
            'dispatchCustomers.customer.customerProperties.dispatchCustomerProperties',
            'dispatchCustomers.customer.customerProperties.customerPropertyGeolocation',
            'dispatchCustomers.customer.customerPhones',
            'dispatchItemSymptoms',
            'dispatchItems' => function($builder) {
                if (Func::keyExistsInWithParam($_REQUEST, 'dispatchItems.symptoms')) {
                    $builder->with('symptoms');
                }
            },
            'dispatchNccs',
            'dispatchPayments',
            'dispatchOrg',
            'dispatchStatus',
            'dispatchAppointment',
            'dispatchUser',
            'dispatchMeta'
        ]);

        if ($tenantId) {
            $query->where('tenant_id', $tenantId);
        }

        if ($assigned_user) {
            if ($assigned_user != "all" && $assigned_user != "unassigned") {
                $query->whereHas('dispatchUser', function($q) use ($assigned_user) {
                    $q->whereIn('user_id', $assigned_user);
                });
            }

            if ($assigned_user == "unassigned") {
                $query->whereDoesntHave('dispatchUser');
            }
        }

        if ($date) {
            // Filter by appointment date
            $query->whereHas('dispatchAppointment', function($q) use ($date) {
                $q->where('appointment_date', $date);
            });
        }

        if ($startDate && $endDate) {
            // Filter by appointment date
            $query->whereHas('dispatchAppointment', function($q) use ($startDate, $endDate) {
                $q->whereBetween('appointment_date', [$startDate, $endDate]);
            });
        }


        return $query->get();
    }

    /**
     * Get all dispatches that are not assigned to any user with all related data.
     *
     * @param int|null $tenantId Optional tenant ID filter
     * @param string|null $date Optional date filter (format: YYYY-MM-DD)
     * @param int|null $employeeId Optional employee ID filter
     * @param string|null $startTime Optional start time in CST (format: YYYY-MM-DD HH:MM:SS)
     * @param string|null $endTime Optional end time in CST (format: YYYY-MM-DD HH:MM:SS)
     * @param bool $withNcc Optional filter to only include dispatches with NCC records
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public static function getUnassignedWithRelations($tenantId = null, $date = null, $employeeId = null, $startTime = null, $endTime = null, $withNcc = false, $assigned = false)
    {

        if ($assigned) {
            $query = Dispatch::query()->whereHas('dispatchUser')
                ->with([
                    'dispatchContracts',
                    'dispatchCoverageNotes',
                    'dispatchCoverages',
                    'dispatchCustomers.customer.customerProperties',
                    'dispatchCustomers.customer.customerProperties.dispatchCustomerProperties',
                    'dispatchCustomers.customer.customerPhones',
                    'dispatchItemSymptoms',
                    'dispatchItems',
                    'dispatchNccs',
                    'dispatchPayments',
                    'dispatchOrg',
                    'dispatchStatus',
                    'dispatchAppointment',
                    'dispatchUser'
                ]);
        } else {
            $query = Dispatch::query()->whereDoesntHave('dispatchUser')
                ->with([
                    'dispatchContracts',
                    'dispatchCoverageNotes',
                    'dispatchCoverages',
                    'dispatchCustomers.customer.customerProperties',
                    'dispatchCustomers.customer.customerProperties.dispatchCustomerProperties',
                    'dispatchCustomers.customer.customerPhones',
                    'dispatchItemSymptoms',
                    'dispatchItems',
                    'dispatchNccs',
                    'dispatchPayments',
                    'dispatchOrg',
                    'dispatchStatus',
                    'dispatchAppointment'
                ]);
        }

        if ($tenantId) {
            $query->where('tenant_id', $tenantId);
        }

        if ($date) {
            // Deprecated, don't use this parameter
            // For ISO 8601 date strings like '2025-05-16T14:56:30Z'
            // $query->whereRaw("DATE(date) = ?", [date('Y-m-d', strtotime($date))]);
        }

        if ($startTime && $endTime) {
            // Filter by appointment date
            $query->whereHas('dispatchAppointment', function ($q) use ($startTime, $endTime) {
                $q->whereBetween('appointment_date', [$startTime, $endTime]);
            });
        }

        if ($employeeId) {
            $query->whereHas('dispatchUser', function ($q) use ($employeeId) {
                $q->where('user_id', $employeeId);
            });
        }

        if ($withNcc) {
            $query->whereHas('dispatchNccs');
        }

        return $query->get();
    }

    public function getByExternalId($externalId)
    {
        return $this->where('external_id', $externalId)->first();
    }
}