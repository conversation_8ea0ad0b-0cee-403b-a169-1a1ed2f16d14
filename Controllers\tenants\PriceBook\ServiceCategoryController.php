<?php

namespace Butler\Controllers\tenants\PriceBook;

use Butler\Controllers\tenants\TenantBaseController;
use Butler\Helpers\SysMsg;
use Butler\Models\ButlerDB;
use Butler\Models\PriceBook\PbServiceCategory;
use Exception;
use PDO;

class ServiceCategoryController extends TenantBaseController
{
    public function __construct()
    {
        parent::__construct();

        $this->controllerBaseUrl = BASE_URL . '/tenant/pricebook/service-category';
        $this->templatePath = $this->templatePath . DS . 'pricebook';
        $this->menu = 'pricebook';
        $this->title = 'Services';
    }

    public function index()
    {
        $this->title = "Service Category List";
        $tenant_id = $this->tenant_id;

        $db = ButlerDB::getMainInstance();

        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = 10; // Items per page
        $offset = ($page - 1) * $limit;

        $countStmt = $db->query("SELECT COUNT(*) FROM pb_service_category");
        $total_records = $countStmt->fetchColumn();
        $total_pages = ceil($total_records / $limit);

        $stmt = $db->prepare("
    SELECT pb_service_category.*, c2.name AS parent_name FROM pb_service_category
             LEFT JOIN pb_service_category c2 on c2.id = pb_service_category.parent_id
    WHERE pb_service_category.tenant_id=:tenant_id                                                         
    ORDER BY CONCAT(IFNULL(c2.name, pb_service_category.name), '__', IF(c2.name IS NULL, '', pb_service_category.name)) ASC
    LIMIT :limit OFFSET :offset            
");
        $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->bindValue(':tenant_id', $tenant_id, PDO::PARAM_INT);
        $stmt->execute();
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Add pagination data to be used in the view
        $pagination = [
            'current_page' => $page,
            'total_pages' => $total_pages,
            'limit' => $limit,
            'total_records' => $total_records
        ];


        if (($_GET['format'] ?? '') == 'json') {
            $this->json(compact('rows', 'pagination'));
        } else
            $this->render('service_category', compact('rows', 'pagination'));
    }

    public function add()
    {
        $this->title = "Add Service Category";
        $tenant_id = $this->tenant_id;
        $db = ButlerDB::getMainInstance();

        if (isset($_POST['name'])) {
            $name = trim($_POST['name'] ?? '');
            $parent_id = $_POST['parent_id'] ?? null;
            if (empty($name)) {
                SysMsg::get_instance()->error("Please fill in required fields.");
            } else {
                try {

                    $stmt = $db->prepare("
                    INSERT INTO pb_service_category (name, parent_id, tenant_id)
                    VALUES (:name, :parent_id, :tenant_id)
                ");
                    $stmt->bindParam(':name', $name);
                    $stmt->bindParam(':tenant_id', $tenant_id);
                    $stmt->bindParam(':parent_id', $parent_id, $parent_id ? PDO::PARAM_INT : PDO::PARAM_NULL);
                    $stmt->execute();

                    SysMsg::get_instance()->success("Added successfully.");
                    $this->redirect();
                } catch (Exception $e) {
                    SysMsg::get_instance()->error("Could not add category: " . $e->getMessage());
                }
            }
        }

        // getting parent categories list.
        $stmt = $db->prepare("
    SELECT id, name FROM pb_service_category
             WHERE parent_id is NULL
             AND tenant_id=?
    ORDER BY name
");
        $stmt->execute([$tenant_id]);
        $parents = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $stmt->closeCursor();

        $this->render('service_category_add', compact('parents'));
    }

    public function edit()
    {
        $this->title = "Update Service Category";

        $tenant_id = $this->tenant_id;
        $successes = array();

        $db = ButlerDB::getMainInstance();

        if (!isset($_GET['id'])) {
            $this->redirect();
        }

        $id = intval($_GET['id']);

        if (isset($_POST['name'])) {
            $name = trim($_POST['name'] ?? '');
            $parent_id = $_POST['parent_id'] ?? null;
            if (empty($name)) {
                SysMsg::get_instance()->error("Please fill in required fields.");
            } else {
                try {

                    $stmt = $db->prepare("
                    UPDATE pb_service_category set name=:name, parent_id=:parent_id
                    WHERE id=:id AND tenant_id=:tenant_id
                ");
                    $stmt->bindParam(':name', $name);
                    $stmt->bindParam(':parent_id', $parent_id, $parent_id ? PDO::PARAM_INT : PDO::PARAM_NULL);
                    $stmt->bindParam(':id', $id, PDO::PARAM_INT);
                    $stmt->bindParam(':tenant_id', $tenant_id, PDO::PARAM_INT);
                    $stmt->execute();

                    SysMsg::get_instance()->success("Updated successfully.");
                    $this->redirect();
                } catch (Exception $e) {
                    SysMsg::get_instance()->error("Could not update category: " . $e->getMessage());
                }
            }
        }

        // getting parent categories list.
        $stmt = $db->prepare("
    SELECT id, name FROM pb_service_category
             WHERE parent_id is NULL
    ORDER BY name
");
        $stmt->execute();
        $parents = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();


        $dbRow = PbServiceCategory::findOrFail($id)?->toArray();

        $this->render('service_category_edit', compact('dbRow', 'id', 'parents'));
    }

    public function delete()
    {
        $tenant_id = $this->tenant_id;
        $db = ButlerDB::getMainInstance();

        if (!isset($_GET['id']) || !isset($_GET['h'])) {
            $this->redirect();
        }
        $id = intval($_GET['id']);
        if (md5("uid$id") != $_GET['h']) {
            $this->redirect();
        }

        $stmt = $db->prepare("
    DELETE FROM pb_service_category WHERE id=? AND tenant_id=?
");
        $stmt->execute([$id, $tenant_id]);

        SysMsg::get_instance()->success("Deleted successfully.");
        $this->redirect();
    }
}