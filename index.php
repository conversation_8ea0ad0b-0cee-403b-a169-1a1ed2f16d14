<?php

error_reporting(E_ALL); // Error/Exception engine, always use E_ALL

ini_set('ignore_repeated_errors', TRUE); // always use TRUE
ini_set('display_errors', FALSE); // Error/Exception display, use FALSE only in production environment or real server. Use TRUE in development environment
ini_set('log_errors', TRUE); // Error/Exception file logging engine.
ini_set('error_log', __DIR__ . '/logs/errors.log'); // Logging file path

require_once __DIR__ . '/custom-autoloader.php';

// Set up CORS headers
$cors_config = require_once __DIR__ . '/Config/cors_config.php';
$origin = $_SERVER['HTTP_ORIGIN'] ?? '';

if (in_array($origin, $cors_config['allow_origin'])) {
    header("Access-Control-Allow-Origin: $origin");
    header("Access-Control-Allow-Credentials: true");
    header("Access-Control-Allow-Methods: " . $cors_config['allow_methods']);
    header("Access-Control-Allow-Headers: " . $cors_config['allow_headers']);
    header("Access-Control-Max-Age: " . $cors_config['max_age']);
}

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// --------- Global Vars ----------//
$container = new stdClass();


// --------- Database connection ----------//
$capsule = new \Illuminate\Database\Capsule\Manager;
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => $_ENV['DB_HOST'],
    'database' => $_ENV['DB_NAME'],
    'username' => $_ENV['DB_USER'],
    'password' => $_ENV['DB_PASS'],
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_general_ci',
    'prefix' => '',
    /*'options'   => [
        // NOTE: This returns mysql query result set data as string.
        \PDO::ATTR_EMULATE_PREPARES => true
    ]*/
]);

$capsule->setAsGlobal();

if ('dev' === ($_ENV['ENVIRONMENT'] ?? null)) {
    $capsule->getConnection()->enableQueryLog();
    $capsule->getConnection()->setEventDispatcher(new \Illuminate\Events\Dispatcher);
    $capsule->getConnection()->listen(function (\Illuminate\Database\Events\QueryExecuted $query) {
        $sql = $query->sql;
        $bindings = $query->bindings;
        $sql_with_bindings = preg_replace_callback('/\?/', function ($match) use ($sql, &$bindings) {
            return json_encode(array_shift($bindings));
        }, $sql);

        // $logger->debug(PHP_EOL . " [SQL] " . $sql_with_bindings);
        $logger = new \Butler\Lib\Log\Logger('db_' . date('Y_m_d') . '.log');
        $logger->debug(PHP_EOL . " [SQL] " . $sql_with_bindings);
    });
}
$capsule->bootEloquent();
// ----------------------------------------//

require_once __DIR__ . '/Lib/session_preload.php';
require_once __DIR__ . '/Routes/routes.php';

$url = isset($_GET['url']) ? $_GET['url'] : '';
$url = rtrim($url, '/');
$router->dispatch($url);


