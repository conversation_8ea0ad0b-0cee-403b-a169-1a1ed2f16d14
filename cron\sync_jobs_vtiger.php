<?php

/**

 * <PERSON><PERSON>: Sync New Jobs into jobs_Vtiger Table

 *

 * This script finds rows in the "jobs" table that do not have a matching "job_id" in "jobs_Vtiger"

 * and inserts them with the following mappings:

 * - jobs.job_id -> jobs_Vtiger.job_id

 * - jobs.complet_address -> jobs_Vtiger.billing_address

 * - jobs.cust_lat -> jobs_Vtiger.cust_lat

 * - jobs.cust_long -> jobs_Vtiger.cust_long

 * - CONCAT(jobs.customer_first_name, ' ', jobs.customer_last_name) -> jobs_Vtiger.customer_name

 * - jobs.customer_phone -> jobs_Vtiger.phone_number

 * - '2001-01-01' -> jobs_Vtiger.Appointment_set

 * - jobs.zone_name -> jobs_Vtiger.zone_name

 * - jobs.job_source -> jobs_Vtiger.job_source

 * - jobs.job_description -> jobs_Vtiger.job_notes

 * - jobs.job_title -> jobs_Vtiger.opportunity_name

 * - jobs.job_source -> jobs_Vtiger.organization_name

 */



// Set error reporting (optional; adjust as needed)

error_reporting(E_ALL);

ini_set("display_errors", 1);



// Database credentials – update these with your actual settings

$dbHost = 'localhost';

$dbName = 'pbx_serv';

$dbUser = 'pbx_serv';

$dbPass = 'JoanMom123!';



try {

    // Establish a connection to the database using PDO

    $pdo = new PDO("mysql:host={$dbHost};dbname={$dbName};charset=utf8mb4", $dbUser, $dbPass);

    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

} catch (PDOException $e) {

    error_log("Connection failed: " . $e->getMessage());

    exit("Database connection failed. Check error logs.");

}



// Build the INSERT ... SELECT query to sync new jobs

$sql = "

INSERT INTO jobs_Vtiger (

    job_id,

    billing_address,

    cust_lat,

    cust_long,

    customer_name,

    phone_number,

    Appointment_set,

    zone_name,

    job_source,

    job_notes,

    opportunity_name,

    organization_name

)

SELECT 

    j.job_id,

    j.complet_address,

    j.cust_lat,

    j.cust_long,

    CONCAT(j.customer_first_name, ' ', j.customer_last_name) AS customer_name,

    j.customer_phone,

    '2001-01-01' AS Appointment_set,

    j.zone_name,

    j.job_source,

    j.job_description,

    j.job_title,

    j.job_source AS organization_name

FROM jobs j

WHERE j.job_id IS NOT NULL

  AND NOT EXISTS (

      SELECT 1 FROM jobs_Vtiger v WHERE v.job_id = j.job_id

  )

";



// Execute the query and output a result message

try {

    $affectedRows = $pdo->exec($sql);

    echo "Sync complete. Inserted $affectedRows new row(s) into jobs_Vtiger." . PHP_EOL;

} catch (PDOException $e) {

    error_log("Cron sync error: " . $e->getMessage());

    exit("Sync error: " . $e->getMessage());

}

?>

