{"cwd": "F:\\htdocs\\development_team", "pkg": {"name": "custom-components-builder", "version": "1.0.0", "description": "Custom UmiJS with Ant Design Pro components for PHP injection", "main": "index.js", "scripts": {"build": "umi build", "build:components": "webpack --config webpack.components.js --mode production", "dev_": "umi dev", "dev": "webpack --watch --config webpack.components.js --mode development", "dev:components": "webpack serve --config webpack.components.js --mode development", "watch": "webpack --config webpack.components.js --mode development --watch", "analyze": "cross-env ANALYZE=1 umi build", "start": "npm run dev"}, "dependencies": {"@ant-design/icons": "^4.8.3", "@ant-design/pro-components": "^2.7.19", "@umijs/preset-react": "^2.1.7", "antd": "^5.21.2", "antd-style": "^3.7.0", "classnames": "^2.3.2", "copy-webpack-plugin": "^13.0.0", "lodash": "^4.17.21", "moment": "^2.29.4", "react": "^18.3.1", "react-dom": "^18.3.1", "umi": "^4.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/preset-react": "^7.18.0", "@babel/preset-typescript": "^7.18.0", "@types/lodash": "^4.14.191", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.10", "@umijs/lint": "^4.0.0", "babel-loader": "^9.1.0", "babel-plugin-import": "^1.13.8", "cross-env": "^7.0.3", "css-loader": "^6.7.0", "html-webpack-plugin": "^5.5.0", "less": "^4.1.3", "less-loader": "^11.1.0", "mini-css-extract-plugin": "^2.7.0", "style-loader": "^3.3.0", "typescript": "^4.9.4", "webpack": "^5.75.0", "webpack-bundle-analyzer": "^4.7.0", "webpack-cli": "^5.0.0", "webpack-dev-server": "^4.11.0"}, "engines": {"node": ">=14.0.0"}}, "pkgPath": "F:\\htdocs\\development_team\\package.json", "plugins": {"./node_modules/@umijs/core/dist/service/servicePlugin": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "preset", "path": "F:/htdocs/development_team/node_modules/@umijs/core/dist/service/servicePlugin.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/core/dist/service/servicePlugin", "key": "servicePlugin"}, "@umijs/preset-umi": {"config": {}, "time": {"hooks": {}, "register": 44}, "enableBy": "register", "type": "preset", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/index.js", "cwd": "F:\\htdocs\\development_team", "id": "@umijs/preset-umi", "key": "umi"}, "./node_modules/@umijs/preset-umi/dist/registerMethods": {"config": {}, "time": {"hooks": {"onStart": [2]}, "register": 84}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/registerMethods.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/registerMethods", "key": "registerMethods"}, "@umijs/did-you-know": {"config": {}, "time": {"hooks": {"onStart": [2]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/did-you-know/dist/plugin.js", "cwd": "F:\\htdocs\\development_team", "id": "@umijs/did-you-know", "key": "umijsDidYouKnow"}, "./node_modules/@umijs/preset-umi/dist/features/404/404": {"config": {}, "time": {"hooks": {"modifyRoutes": [0]}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/404/404.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/404/404", "key": "404"}, "./node_modules/@umijs/preset-umi/dist/features/appData/appData": {"config": {}, "time": {"hooks": {"modifyAppData": [11]}, "register": 61}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/appData/appData.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/appData", "key": "appData"}, "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/appData/umiInfo.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/appData/umiInfo", "key": "umiInfo"}, "./node_modules/@umijs/preset-umi/dist/features/check/check": {"config": {}, "time": {"hooks": {"onCheckConfig": [0], "onCheck": [1]}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/check/check.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/check/check", "key": "check"}, "./node_modules/@umijs/preset-umi/dist/features/check/babel722": {"config": {}, "time": {"hooks": {"onCheck": [0]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/check/babel722.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/check/babel722", "key": "babel722"}, "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/codeSplitting/codeSplitting", "key": "codeSplitting"}, "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 22}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/configPlugins/configPlugins", "key": "configPlugins"}, "virtual: config-title": {"id": "virtual: config-title", "key": "title", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styles": {"id": "virtual: config-styles", "key": "styles", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-scripts": {"id": "virtual: config-scripts", "key": "scripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routes": {"id": "virtual: config-routes", "key": "routes", "config": {"onChange": "regenerateTmpFiles"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-routeLoader": {"id": "virtual: config-routeLoader", "key": "routeLoader", "config": {"default": {"moduleType": "esm"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-reactRouter5Compat": {"id": "virtual: config-reactRouter5Compat", "key": "reactRouter5Compat", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-presets": {"id": "virtual: config-presets", "key": "presets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-plugins": {"id": "virtual: config-plugins", "key": "plugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-npmClient": {"id": "virtual: config-npmClient", "key": "npmClient", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mountElementId": {"id": "virtual: config-mountElementId", "key": "mountElementId", "config": {"default": "root"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-metas": {"id": "virtual: config-metas", "key": "metas", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-links": {"id": "virtual: config-links", "key": "links", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-historyWithQuery": {"id": "virtual: config-historyWithQuery", "key": "historyWithQuery", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-history": {"id": "virtual: config-history", "key": "history", "config": {"default": {"type": "browser"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-headScripts": {"id": "virtual: config-headScripts", "key": "headScripts", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esbuildMinifyIIFE": {"id": "virtual: config-esbuildMinifyIIFE", "key": "esbuildMinifyIIFE", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionRoutes": {"id": "virtual: config-conventionRoutes", "key": "conventionRoutes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-conventionLayout": {"id": "virtual: config-conventionLayout", "key": "conventionLayout", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-base": {"id": "virtual: config-base", "key": "base", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-analyze": {"id": "virtual: config-analyze", "key": "analyze", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-writeToDisk": {"id": "virtual: config-writeToDisk", "key": "writeToDisk", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-transformRuntime": {"id": "virtual: config-transformRuntime", "key": "transformRuntime", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-theme": {"id": "virtual: config-theme", "key": "theme", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-targets": {"id": "virtual: config-targets", "key": "targets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgr": {"id": "virtual: config-svgr", "key": "svgr", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-svgo": {"id": "virtual: config-svgo", "key": "svgo", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-stylusLoader": {"id": "virtual: config-stylusLoader", "key": "stylus<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-styleLoader": {"id": "virtual: config-style<PERSON>oader", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspilerOptions": {"id": "virtual: config-srcTranspilerOptions", "key": "srcTranspilerOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-srcTranspiler": {"id": "virtual: config-srcTranspiler", "key": "srcTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-sassLoader": {"id": "virtual: config-sassLoader", "key": "sass<PERSON><PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-runtimePublicPath": {"id": "virtual: config-runtimePublicPath", "key": "runtimePublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-purgeCSS": {"id": "virtual: config-purgeCSS", "key": "purgeCSS", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-publicPath": {"id": "virtual: config-publicPath", "key": "publicPath", "config": {"default": "/"}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-proxy": {"id": "virtual: config-proxy", "key": "proxy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-postcssLoader": {"id": "virtual: config-postcssLoader", "key": "postcss<PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-outputPath": {"id": "virtual: config-outputPath", "key": "outputPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-normalCSSLoaderModules": {"id": "virtual: config-normalCSSLoaderModules", "key": "normalCSSLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mfsu": {"id": "virtual: config-mfsu", "key": "mfsu", "config": {"default": {"strategy": "eager"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-mdx": {"id": "virtual: config-mdx", "key": "mdx", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-manifest": {"id": "virtual: config-manifest", "key": "manifest", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-lessLoader": {"id": "virtual: config-less<PERSON><PERSON>der", "key": "<PERSON><PERSON><PERSON><PERSON>", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifierOptions": {"id": "virtual: config-jsMinifierOptions", "key": "jsMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-jsMinifier": {"id": "virtual: config-jsMinifier", "key": "jsMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-inlineLimit": {"id": "virtual: config-inlineLimit", "key": "inlineLimit", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-ignoreMomentLocale": {"id": "virtual: config-ignoreMomentLocale", "key": "ignoreMomentLocale", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-https": {"id": "virtual: config-https", "key": "https", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-hash": {"id": "virtual: config-hash", "key": "hash", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-forkTSChecker": {"id": "virtual: config-fork<PERSON><PERSON><PERSON><PERSON>", "key": "forkTSChecker", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-fastRefresh": {"id": "virtual: config-fastRefresh", "key": "fastRefresh", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraPostCSSPlugins": {"id": "virtual: config-extraPostCSSPlugins", "key": "extraPostCSSPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPresets": {"id": "virtual: config-extraBabelPresets", "key": "extraBabelPresets", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelPlugins": {"id": "virtual: config-extraBabelPlugins", "key": "extraBabelPlugins", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-extraBabelIncludes": {"id": "virtual: config-extraBabelIncludes", "key": "extraBabelIncludes", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-externals": {"id": "virtual: config-externals", "key": "externals", "config": {"default": {}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-esm": {"id": "virtual: config-esm", "key": "esm", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-devtool": {"id": "virtual: config-devtool", "key": "devtool", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-depTranspiler": {"id": "virtual: config-depTranspiler", "key": "depTranspiler", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-define": {"id": "virtual: config-define", "key": "define", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-deadCode": {"id": "virtual: config-deadCode", "key": "deadCode", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssPublicPath": {"id": "virtual: config-cssPublicPath", "key": "cssPublicPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifierOptions": {"id": "virtual: config-cssMinifierOptions", "key": "cssMinifierOptions", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssMinifier": {"id": "virtual: config-cssMinifier", "key": "cssMinifier", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoaderModules": {"id": "virtual: config-cssLoaderModules", "key": "cssLoaderModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cssLoader": {"id": "virtual: config-cssLoader", "key": "cssL<PERSON>der", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-copy": {"id": "virtual: config-copy", "key": "copy", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-checkDepCssModules": {"id": "virtual: config-checkDepCssModules", "key": "checkDepCssModules", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-chainWebpack": {"id": "virtual: config-chainWebpack", "key": "chainWebpack", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-cacheDirectoryPath": {"id": "virtual: config-cacheDirectoryPath", "key": "cacheDirectoryPath", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-babelLoaderCustomize": {"id": "virtual: config-babelLoaderCustomize", "key": "babelLoaderCustomize", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoprefixer": {"id": "virtual: config-autoprefixer", "key": "autoprefixer", "config": {}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-autoCSSModules": {"id": "virtual: config-autoCSSModules", "key": "autoCSSModules", "config": {"default": true}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "virtual: config-alias": {"id": "virtual: config-alias", "key": "alias", "config": {"default": {"umi": "@@/exports", "react": "F:\\htdocs\\development_team\\node_modules\\react", "react-dom": "F:\\htdocs\\development_team\\node_modules\\react-dom", "react-router": "F:\\htdocs\\development_team\\node_modules\\react-router", "react-router-dom": "F:\\htdocs\\development_team\\node_modules\\react-router-dom"}}, "type": "plugin", "enableBy": "register", "time": {"hooks": {}, "register": 0}}, "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/crossorigin/crossorigin", "key": "crossorigin"}, "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand": {"config": {}, "time": {"hooks": {"onStart": [1]}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/depsOnDemand/depsOnDemand", "key": "deps<PERSON>n<PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/devTool/devTool.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/devTool/devTool", "key": "devTool"}, "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker": {"config": {}, "time": {"hooks": {}, "register": 214}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/esbuildHelperChecker/esbuildHelperChecker", "key": "esbuildHelperChecker"}, "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi": {"config": {}, "time": {"hooks": {}, "register": 424}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/esmi/esmi.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/esmi/esmi", "key": "esmi"}, "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic": {"config": {}, "time": {"hooks": {}, "register": 52}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/exportStatic/exportStatic", "key": "exportStatic"}, "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons": {"config": {}, "time": {"hooks": {"modifyAppData": [1]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/favicons/favicons.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/favicons/favicons", "key": "favicons"}, "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/helmet/helmet.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/helmet/helmet", "key": "helmet"}, "./node_modules/@umijs/preset-umi/dist/features/icons/icons": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/icons/icons.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/icons/icons", "key": "icons"}, "./node_modules/@umijs/preset-umi/dist/features/mock/mock": {"config": {}, "time": {"hooks": {}, "register": 94}, "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/mock/mock.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/mock/mock", "key": "mock"}, "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/mpa/mpa.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/mpa/mpa", "key": "mpa"}, "./node_modules/@umijs/preset-umi/dist/features/okam/okam": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/okam/okam.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/okam/okam", "key": "okam"}, "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/overrides/overrides.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/overrides/overrides", "key": "overrides"}, "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/phantomDependency/phantomDependency", "key": "phantomDependency"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill": {"config": {}, "time": {"hooks": {"modifyConfig": [0]}, "register": 15}, "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/polyfill", "key": "polyfill"}, "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/polyfill/publicPathPolyfill", "key": "publicPathPolyfill"}, "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/prepare/prepare.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/prepare/prepare", "key": "prepare"}, "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/routePrefetch/routePrefetch", "key": "routePrefetch"}, "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/terminal/terminal.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/terminal/terminal", "key": "terminal"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles": {"config": {}, "time": {"hooks": {}, "register": 12}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/tmpFiles", "key": "tmpFiles"}, "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader": {"config": {}, "time": {"hooks": {}, "register": 9}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/clientLoader/clientLoader", "key": "clientLoader"}, "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps": {"config": {}, "time": {"hooks": {}, "register": 1}, "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/routeProps/routeProps", "key": "routeProps"}, "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr": {"config": {}, "time": {"hooks": {}, "register": 9}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/ssr/ssr.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/ssr/ssr", "key": "ssr"}, "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes": {"config": {}, "time": {"hooks": {}, "register": 5}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/tmpFiles/configTypes", "key": "configTypes"}, "./node_modules/@umijs/preset-umi/dist/features/transform/transform": {"config": {}, "time": {"hooks": {}, "register": 7}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/transform/transform.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/transform/transform", "key": "transform"}, "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport": {"config": {}, "time": {"hooks": {}, "register": 9}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/lowImport/lowImport", "key": "lowImport"}, "./node_modules/@umijs/preset-umi/dist/features/vite/vite": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/vite/vite.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/vite/vite", "key": "vite"}, "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute": {"config": {}, "time": {"hooks": {}, "register": 31}, "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/apiRoute/apiRoute", "key": "apiRoute"}, "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect": {"config": {}, "time": {"hooks": {}, "register": 68}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/monorepo/redirect.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/monorepo/redirect", "key": "monorepoRedirect"}, "./node_modules/@umijs/preset-umi/dist/features/test/test": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/test/test.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/test/test", "key": "test"}, "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent": {"config": {}, "time": {"hooks": {}, "register": 2}, "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/clickToComponent/clickToComponent", "key": "clickToComponent"}, "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/legacy/legacy.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/legacy/legacy", "key": "legacy"}, "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/classPropertiesLoose/classPropertiesLoose", "key": "classPropertiesLoose"}, "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack": {"config": {}, "time": {"hooks": {}, "register": 3}, "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/webpack/webpack.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/webpack/webpack", "key": "preset-umi:webpack"}, "./node_modules/@umijs/preset-umi/dist/features/swc/swc": {"config": {}, "time": {"hooks": {"addOnDemandDeps": [1]}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/swc/swc.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/swc/swc", "key": "swc"}, "./node_modules/@umijs/preset-umi/dist/features/ui/ui": {"config": {}, "time": {"hooks": {}, "register": 7}, "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/ui/ui.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/ui/ui", "key": "ui"}, "./node_modules/@umijs/preset-umi/dist/features/mako/mako": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/mako/mako.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/mako/mako", "key": "mako"}, "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian": {"config": {}, "time": {"hooks": {}, "register": 6}, "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/hmrGuardian/hmrGuardian", "key": "hm<PERSON><PERSON><PERSON><PERSON>"}, "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad": {"config": {}, "time": {"hooks": {}, "register": 4}, "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/routePreloadOnLoad/routePreloadOnLoad", "key": "routePreloadOnLoad"}, "./node_modules/@umijs/preset-umi/dist/features/forget/forget": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "config", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/forget/forget.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/forget/forget", "key": "forget"}, "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/features/bundler/bundler.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/features/bundler/bundler", "key": "preset-umi:bundler"}, "./node_modules/@umijs/preset-umi/dist/commands/build": {"config": {}, "time": {"hooks": {}, "register": 39}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/build.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/build", "key": "build"}, "./node_modules/@umijs/preset-umi/dist/commands/config/config": {"config": {}, "time": {"hooks": {}, "register": 96}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/config/config.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/config/config", "key": "config"}, "./node_modules/@umijs/preset-umi/dist/commands/dev/dev": {"config": {}, "time": {"hooks": {}, "register": 236}, "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/dev/dev.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/dev/dev", "key": "dev"}, "./node_modules/@umijs/preset-umi/dist/commands/help": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/help.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/help", "key": "help"}, "./node_modules/@umijs/preset-umi/dist/commands/lint": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/lint.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/lint", "key": "lint"}, "./node_modules/@umijs/preset-umi/dist/commands/setup": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/setup.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/setup", "key": "setup"}, "./node_modules/@umijs/preset-umi/dist/commands/deadcode": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/deadcode.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/deadcode", "key": "deadcode"}, "./node_modules/@umijs/preset-umi/dist/commands/version": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/version.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/version", "key": "version"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/page": {"config": {}, "time": {"hooks": {}, "register": 4}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/generators/page.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/page", "key": "generator:page"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/generators/prettier.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/prettier", "key": "generator:prettier"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tsconfig", "key": "generator:tsconfig"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/jest": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/generators/jest.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/jest", "key": "generator:jest"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/tailwindcss", "key": "generator:tailwindcss"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/dva": {"config": {}, "time": {"hooks": {}, "register": 0}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/generators/dva.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/dva", "key": "generator:dva"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/component": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/generators/component.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/component", "key": "generator:component"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/mock": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/generators/mock.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/mock", "key": "generator:mock"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/generators/cypress.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/cypress", "key": "generator:cypress"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/api": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/generators/api.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/api", "key": "generator:api"}, "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/generators/precommit.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/generators/precommit", "key": "generator:precommit"}, "./node_modules/@umijs/preset-umi/dist/commands/plugin": {"config": {}, "time": {"hooks": {}, "register": 1}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/plugin.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/plugin", "key": "command:plugin"}, "./node_modules/@umijs/preset-umi/dist/commands/verify-commit": {"config": {}, "time": {"hooks": {}, "register": 2}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/verify-commit.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/verify-commit", "key": "verifyCommit"}, "./node_modules/@umijs/preset-umi/dist/commands/preview": {"config": {}, "time": {"hooks": {}, "register": 60}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/preview.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/preview", "key": "preview"}, "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu": {"config": {}, "time": {"hooks": {}, "register": 3}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/preset-umi/dist/commands/mfsu/mfsu", "key": "mfsu-cli"}, "@umijs/plugin-run": {"config": {}, "time": {"hooks": {}, "register": 6}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/plugin-run/dist/index.js", "cwd": "F:\\htdocs\\development_team", "id": "@umijs/plugin-run", "key": "run"}, "./node_modules/@umijs/core/dist/service/generatePlugin": {"config": {}, "time": {"hooks": {}, "register": 8}, "enableBy": "register", "type": "plugin", "path": "F:/htdocs/development_team/node_modules/@umijs/core/dist/service/generatePlugin.js", "cwd": "F:\\htdocs\\development_team", "id": "./node_modules/@umijs/core/dist/service/generatePlugin", "key": "generatePlugin"}}, "presets": [], "name": "build", "args": {"_": []}, "userConfig": {}, "mainConfigFile": null, "config": {"routeLoader": {"moduleType": "esm"}, "mountElementId": "root", "history": {"type": "browser"}, "base": "/", "svgr": {}, "publicPath": "/", "mfsu": {"strategy": "eager"}, "ignoreMomentLocale": true, "externals": {}, "autoCSSModules": true, "alias": {"umi": "@@/exports", "react": "F:\\htdocs\\development_team\\node_modules\\react", "react-dom": "F:\\htdocs\\development_team\\node_modules\\react-dom", "react-router": "F:\\htdocs\\development_team\\node_modules\\react-router", "react-router-dom": "F:\\htdocs\\development_team\\node_modules\\react-router-dom", "@": "F:/htdocs/development_team/src", "@@": "F:/htdocs/development_team/src/.umi-production", "regenerator-runtime": "F:\\htdocs\\development_team\\node_modules\\regenerator-runtime"}, "targets": {"chrome": 80}}, "routes": {}, "apiRoutes": {}, "hasSrcDir": true, "npmClient": "yarn", "umi": {"version": "4.4.11", "name": "<PERSON><PERSON>", "importSource": "umi", "cliName": "umi"}, "bundleStatus": {"done": false}, "mfsuBundleStatus": {"done": false}, "react": {"version": "18.3.1", "path": "F:\\htdocs\\development_team\\node_modules\\react"}, "react-dom": {"version": "18.3.1", "path": "F:\\htdocs\\development_team\\node_modules\\react-dom"}, "appJS": null, "locale": "en-US", "globalCSS": [], "globalJS": [], "overridesCSS": [], "bundler": "webpack", "git": {"originUrl": "**************-pa:developmentbutler/development_team.git"}, "framework": "react", "typescript": {"tsVersion": "4.9.5", "tslibVersion": "2.8.1"}, "faviconFiles": []}