<?php
namespace Butler\Lib\Auth;

use <PERSON>\Helpers\Func;
use Butler\Models\ButlerDB;
use <PERSON>\Lib\Auth\Permissions;
use <PERSON>\Config\SiteConfig;
use PDO;
use PDOException;

class AdminAuthMiddleware {
    public static function authenticate($requiredPermission = null) {        
        $headers = apache_request_headers();
        $authHeader = $headers['authorization'] ?? '';
        if ($authHeader && strpos($authHeader, 'Bearer ') === 0) {
            $auth = self::authenticateToken();
        } else {
            $auth = self::authenticateSession();
        }
        
        if ($requiredPermission !== null) {
            if (!Permissions::hasPermission($auth['role'], $requiredPermission)) {
                self::sendForbidden('Insufficient permissions');
            }
        }

        // Register auth info globally.
        Func::setInContainer('auth', $auth);

        return $auth;
    }

    private static function authenticateToken() {
        $headers = apache_request_headers();
        $authHeader = $headers['authorization'] ?? '';
        $token = trim(substr($authHeader, 7)); // Remove "Bearer " prefix
        
        if (empty($token)) {
            self::sendUnauthorized('No token provided');
        }

        try {
            $pdo = ButlerDB::getMainInstance();

            $data = null;
            if (empty($tenant_id)) {
                // Verify token and get tenant info
                $stmt = $pdo->prepare("
                    SELECT 
                        t.id as tenant_id,
                        t.db_name,
                        t.db_user,
                        t.db_pass,
                        t.tenant_number,
                        t.company_name,
                        tu.id as user_id,
                        tu.email
                    FROM tenant_users tu
                    JOIN tenants t ON tu.tenant_id = t.id
                    WHERE tu.api_token = ? AND tu.token_expires > NOW()
                    LIMIT 1
                ");
                
                $stmt->execute([$token]);
                $data = $stmt->fetch(PDO::FETCH_ASSOC);
                $data['user_type'] = 'tenant';
                $data['role'] = 'admin';
            }
            else {
                $tenantDb = ButlerDB::getTenantInstance($tenant_id);

                $stmt = $tenantDb->prepare("
                    SELECT *
                    FROM employees
                    WHERE api_token = :api_token
                    LIMIT 1
                ");
                $stmt->execute([':api_token' => $token]);
                $user = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$user) {
                    self::sendUnauthorized('Invalid or expired token');
                }

                $stmt = $pdo->prepare("
                    SELECT 
                        t.id as tenant_id,
                        t.db_name,
                        t.db_user,
                        t.db_pass,
                        t.tenant_number,
                        t.company_name,
                        tu.id as user_id,
                        tu.email
                    FROM tenant_users tu
                    JOIN tenants t ON tu.tenant_id = t.id
                    WHERE tu.tenant_id = ?
                    LIMIT 1
                ");
                
                $stmt->execute([$tenant_id]);
                $tenant_data = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$tenant_data) {
                    self::sendUnauthorized('Invalid tenant');
                }

                $data = array(
                    'tenant_id' => $tenant_id,
                    'db_name' => $tenant_data['db_name'],
                    'db_user' => $tenant_data['db_user'],
                    'db_pass' => $tenant_data['db_pass'],
                    'tenant_number' => $tenant_data['tenant_number'],
                    'company_name' => $tenant_data['company_name'],
                    'user_id' => $user['id'],
                    'email' => $user['email'],
                    'role' => $user['role'],
                    'user_type' => 'employee'
                );
            }

            if (!$data) {
                self::sendUnauthorized('Invalid or expired token');
            }

            // Add permissions to the auth data
            // $data['permissions'] = Permissions::getRolePermissions($data['role']);
            
            $_REQUEST['auth'] = $data;
            
            return $data;

        } catch (PDOException $e) {
            self::sendUnauthorized('Authentication failed');
        }
    }

    private static function authenticateSession() {        
        if (!isset($_SESSION['user_type'])) {
            self::sendUnauthorizedSession('Not authenticated');
        } elseif ($_SESSION['user_type'] !== 'site_admin') {
            self::sendUnauthorizedSession('Not authenticated');
        }

        $data = [
            'admin_id' => $_SESSION['admin_id'],                        
            'admin_email' => $_SESSION['admin_email'],
            'admin_role' => $_SESSION['admin_role'] ?? '',
            'user_type' => $_SESSION['user_type'],
            'is_super_admin' => $_SESSION['is_super_admin'],                        
        ];

        // Add permissions to the auth data
        // $data['permissions'] = Permissions::getRolePermissions($data['role']);

        return $data;
    }

    private static function sendUnauthorized($message) {
        header('HTTP/1.1 401 Unauthorized');
        echo json_encode(['error' => $message]);
        exit;
    }

    private static function sendUnauthorizedSession($message) {
        $config = SiteConfig::getSiteConfig();        
        header('Location: ' . $config['BASE_URL'] . 'login/admin');        
        exit;
    }

    private static function sendForbidden($message) {
        header('HTTP/1.1 403 Forbidden');
        echo json_encode(['error' => $message]);
        exit;
    }
}

