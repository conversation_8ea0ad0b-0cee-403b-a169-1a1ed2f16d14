<?php

namespace Butler\Models\Dispatch;

use Butler\Models\BaseModel;
use Butler\Models\Customer\Customer;
use Butler\Models\Customer\CustomerProperty;
use Butler\Models\File;

/**
 * @property integer $id
 * @property integer $dispatch_id
 * @property integer $customer_id
 * @property integer $file_id
 * @property string $purchase_no
 * @property string $status
 * @property string $issue_date
 * @property string $work_start_date
 * @property float $grand_total
 * @property float $subtotal
 * @property float $tax_percent
 * @property float $tax_amount
 * @property float $discount_amount
 * @property integer $total_item_count
 * @property string $notes
 * @property string $payment_terms
 * @property string $valid_until
 * @property string $billing_company
 * @property string $billing_name
 * @property string $billing_email
 * @property string $billing_phone
 * @property string $billing_city
 * @property string $billing_state
 * @property string $billing_street_direction
 * @property string $billing_street_name
 * @property string $billing_street_number
 * @property string $billing_unit_number
 * @property string $billing_unit_type
 * @property string $billing_zip
 * @property string $billing_zip_four
 * @property string $customer_company
 * @property string $customer_name
 * @property string $customer_email
 * @property string $customer_phone
 * @property string $customer_city
 * @property string $customer_state
 * @property string $customer_street_direction
 * @property string $customer_street_name
 * @property string $customer_street_number
 * @property string $customer_unit_number
 * @property string $customer_unit_type
 * @property string $customer_zip
 * @property string $customer_zip_four
 * @property integer $created_by
 * @property string $created_at
 * @property integer $updated_by
 * @property string $updated_at
 * @property DispatchEstimateItem[] $items
 * @property DispatchEstimateLog[] $logs
 *
 * @property Customer $customer
 * @property File $file
 * @property Dispatch $dispatch
 */
class DispatchEstimate extends BaseModel
{
    const STATUS_DRAFT = 'draft';
    const STATUS_SENT = 'sent';
    const STATUS_ACCEPTED = 'accepted';
    const STATUS_DECLINED = 'declined';
    const STATUS_EXPIRED = 'expired';

    public $timestamps = true;

    /**
     * @var array
     */
    protected $fillable = ['dispatch_id', 'customer_id', 'file_id', 'purchase_no', 'status', 'issue_date', 'work_start_date'
        , 'grand_total'
        , 'subtotal'
        , 'tax_percent'
        , 'tax_amount'
        , 'discount_amount', 'total_item_count', 'notes', 'payment_terms', 'valid_until', 'billing_company', 'billing_name', 'billing_email', 'billing_phone', 'billing_city', 'billing_state', 'billing_street_direction', 'billing_street_name', 'billing_street_number', 'billing_unit_number', 'billing_unit_type', 'billing_zip', 'billing_zip_four', 'customer_company', 'customer_name', 'customer_email', 'customer_phone', 'customer_city', 'customer_state', 'customer_street_direction', 'customer_street_name', 'customer_street_number', 'customer_unit_number', 'customer_unit_type', 'customer_zip', 'customer_zip_four', 'created_by', 'created_at', 'updated_by', 'updated_at'];

    public static function getBoundData(&$item, $applyReverseCast = false, $unsetNull = false): array
    {
        $data = parent::getBoundData($item, $applyReverseCast, $unsetNull);

        /** @var Dispatch $dispatch */
        $dispatch = Dispatch::query()
            ->where('id', $data['dispatch_id'])
            ->with('customer')
            ->with('addresses')
            ->firstOrFail();

        /** @var Customer $customer */
        $customer = $dispatch->customer;

        if ($customer) {
            $data['customer_id'] = $customer->id;
            $data['customer_name'] = $customer->name;
        }

        if ($addressId = $data['address_id'] ?? null) {
            /** @var CustomerProperty $addr */
            $addr = CustomerProperty::find($addressId);
        } else {
            /** @var CustomerProperty $addr */
            $addr = $dispatch->addresses[0] ?? null;
        }
        if ($addr) {
            $data['customer_email'] = $customer->email;
            $data['customer_phone'] = $customer->customerPhones()->first()?->phone;
            $data['customer_city'] = $addr->city;
            $data['customer_state'] = $addr->state;
            $data['customer_street_direction'] = $addr->streetDirection;
            $data['customer_street_name'] = $addr->streetName;
            $data['customer_street_number'] = $addr->streetNumber;
            $data['customer_unit_number'] = $addr->unitNumber;
            $data['customer_unit_type'] = $addr->unitType;
            $data['customer_zip'] = $addr->zip;
            $data['customer_zip_four'] = $addr->zipFour;
        }

        return $data;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function items()
    {
        return $this->hasMany('Butler\Models\Dispatch\DispatchEstimateItem');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function logs()
    {
        return $this->hasMany('Butler\Models\Dispatch\DispatchEstimateLog');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function customer()
    {
        return $this->belongsTo('Butler\Models\Customer\Customer');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function dispatch()
    {
        return $this->belongsTo('Butler\Models\Dispatch\Dispatch');
    }

    /**
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param $tenant_id
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeTenantOnly(\Illuminate\Database\Eloquent\Builder $query, $tenant_id)
    {
        $query->whereHas('dispatch', function($builder) use (&$tenant_id) {
            $builder->where('tenant_id', $tenant_id);
        });
    }
}
