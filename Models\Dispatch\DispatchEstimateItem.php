<?php

namespace Butler\Models\Dispatch;

use Butler\Models\BaseModel;
use Butler\Models\PriceBook\PbService;

/**
 * @property integer $id
 * @property integer $dispatch_estimate_id
 * @property integer $service_id
 * @property string $name
 * @property string $description
 * @property float $qty
 * @property float $cost
 * @property float $price
 * @property float $price_total
 * @property boolean $taxable
 * @property float $tax_percent
 * @property float $tax_amount
 * @property float $total
 * @property float $duration
 * @property integer $sort_order
 * @property string $created_at
 * @property string $updated_at
 *
 * @property DispatchEstimate $estimate
 *
 * @property PbService $pbService
 */
class DispatchEstimateItem extends BaseModel
{
    public $timestamps = true;

    /**
     * @var array
     */
    protected $fillable = ['dispatch_estimate_id', 'service_id', 'name', 'description'
        , 'qty', 'cost'
        , 'price'
        , 'price_total'
        , 'taxable'
        , 'tax_percent'
        , 'tax_amount'
        , 'total'
        , 'duration'
        , 'sort_order', 'created_at', 'updated_at'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function estimate()
    {
        return $this->belongsTo('Butler\Models\Dispatch\DispatchEstimate');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function pbService()
    {
        return $this->belongsTo('Butler\Models\PriceBook\PbService', 'service_id');
    }
}
