<?php

namespace Butler\Controllers\tenants\PriceBook;

use Butler\Controllers\tenants\TenantBaseController;
use Butler\Helpers\Func;
use Butler\Helpers\SysMsg;
use Butler\Models\ButlerDB;
use Butler\Models\PriceBook\PbService;
use Butler\Models\PriceBook\PbServiceCategory;
use Butler\Models\PriceBook\PbServiceCategoryMap;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use PDO;

class ServiceController extends TenantBaseController
{
    public function __construct()
    {
        parent::__construct();

        $this->controllerBaseUrl = BASE_URL . '/tenant/pricebook/service';
        $this->templatePath = $this->templatePath . DS . 'pricebook';
        $this->menu = 'pricebook';
    }

    public function index()
    {
        $this->title = "Service List";

        $tenant_id = $this->tenant_id;

        $db = ButlerDB::getMainInstance();

        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = 10; // Items per page
        $offset = ($page - 1) * $limit;

        $qb = PbService::query();
        $qb->tenant($this->tenant_id);

        $category_id = intval($_GET['category_id'] ?? null);
        if ($category_id) {
            $qb->whereHas('pbServiceCategories', function ($qb) use (&$category_id) {
                $qb->where('id', $category_id);
            });
        }

        $category_ids = Func::csvToArr($_GET['category_ids'] ?? '');
        if ($category_ids) {
            $qb->whereHas('pbServiceCategories', function ($qb) use (&$category_ids) {
                $qb->whereIn('id', $category_ids);
            });
        }

        $keyWords = trim($_GET['keyWords'] ?? '');
        if ($keyWords) {
            $qb->where(function (Builder $qb) use (&$keyWords) {
                $qb->where('name', 'like', '%' . $keyWords . '%')
                    ->orWhere('description', 'like', '%' . $keyWords . '%')
                    ->orWhereHas('pbServiceCategories', function ($qb) use (&$keyWords) {
                        $qb->where('name', 'like', '%' . $keyWords . '%');
                    })
                ;
            });
        }

        $total_records = $qb->count();
        $total_pages = ceil($total_records / $limit);

        $qb->with('pbServiceCategories');
        $rows = $qb->get()->toArray();

        // Add pagination data to be used in the view
        $pagination = [
            'current_page' => $page,
            'total_pages' => $total_pages,
            'limit' => $limit,
            'total_records' => $total_records
        ];

        // getting parent categories list.
        $stmt = $db->prepare("
    SELECT 
        pb_service_category.id, 
        pb_service_category.name, 
        pb_service_category.parent_id, 
        c2.name as parent_name
    FROM pb_service_category
                    left join pb_service_category c2 on c2.id=pb_service_category.parent_id
    WHERE pb_service_category.tenant_id=?
    ORDER BY CONCAT(IFNULL(c2.name, pb_service_category.name), '__', IF(c2.name IS NULL, '', pb_service_category.name)) ASC
");
        $stmt->execute([$tenant_id]);
        $parents = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $this->render('service', compact('parents', 'pagination', 'rows', 'category_id', 'keyWords'));
    }

    public function add()
    {
        $this->title = "Add Service";

        if (isset($_POST['name'])) {
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $category_ids = Func::csvToArr($_POST['category_ids'] ?? '');
            $_POST['tenant_id'] = $this->tenant_id;

            if (empty($name) || empty($description)) {
                SysMsg::get_instance()->error('Please fill in required fields.');
            } else {
                try {
                    /** @var PbService $row */
                    $row = PbService::create($_POST);

                    $row->pbServiceCategories()->sync($category_ids);

                    SysMsg::get_instance()->success("Added successfully.");
                    $this->redirect();
                } catch (Exception $e) {
                    SysMsg::get_instance()->error("Could not add service: " . $e->getMessage());
                }
            }
        }

        $this->render('service_add');
    }

    public function edit()
    {
        $this->title = "Update Service";

        if (!isset($_GET['id'])) {
            header('Location: ' . $_SERVER['REQUEST_URI']);
            exit;
        }

        $id = intval($_GET['id']);

        if (isset($_POST['name'])) {
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $category_ids = Func::csvToArr($_POST['category_ids'] ?? '');
            $_POST['tenant_id'] = $this->tenant_id;

            $_POST['taxable'] = $_POST['taxable'] ?? 0;

            if (empty($name) || empty($description)) {
                SysMsg::get_instance()->error("Please fill in required fields.");
            } else {
                /** @var PbService $dbRow */
                $dbRow = PbService::query()->tenant($this->tenant_id)->where('id', $id)->firstOrFail();
                try {
                    $dbRow->update($_POST);

                    $dbRow->pbServiceCategories()->sync($category_ids);

                    SysMsg::get_instance()->success("Updated successfully.");
                    $this->redirect();
                } catch (Exception $e) {
                    SysMsg::get_instance()->error("Could not update category: " . $e->getMessage());
                }
            }
        }

        $dbRow = PbService::query()
            ->with('pbServiceCategories')
            ->where('id', $id)
            ->tenant($this->tenant_id)
            ->first()?->toArray();

        $this->render('service_edit', compact('dbRow', 'id'));
    }

    public function import()
    {
        $this->title = "Import Services";

        if (isset($_FILES['file'])) {
            $uploadDir = 'uploads/import-services/' . $this->tenant_id . '/' . date('Y') . '/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }

            $filename = basename($_FILES['file']['name']);
            $targetFile = $uploadDir . $filename;

            if (move_uploaded_file($_FILES['file']['tmp_name'], $targetFile))
            {
                // ----------------------------------
                // Import process.
                // ----------------------------------
                $fileExt = pathinfo($targetFile, PATHINFO_EXTENSION);
                if ($fileExt == 'csv') {
                    $csv = new \PhpOffice\PhpSpreadsheet\Reader\Csv();
                    $csv->setDelimiter($options['delimiter'] ?? ',');
                } else {
                    $csv = $fileExt == 'xls' ? new \PhpOffice\PhpSpreadsheet\Reader\Xls() : new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
                }

                $spreadsheet = $csv->load($targetFile);
                $sheet = $spreadsheet->getActiveSheet();
                $data = $sheet->toArray();
                if (($dataLen = count($data)) < 2) {
                    throw new Exception('CSV file data is invalid. Header and data are missing.');
                }
                $resultTmp = [];

                for ($i=1; $i < $dataLen; $i++) {
                    $row = $data[$i];

                    $category1 = $row[0];
                    $category2 = $row[1];
                    $category3 = $row[2];
                    $category4 = $row[3];
                    $name = $row[4];
                    $description = $row[5];
                    $price = $row[6];
                    $cost = $row[7];
                    $taxable = $row[8];
                    $unit_of_measurement = $row[9];
                    $task_code = $row[10];
                    $is_online_booking = $row[11];

                    if ($category1 && $category2 && ($category3 || $category4) && $name)
                    {
                        /** @var PbServiceCategory $cat1 */
                        $cat1 = PbServiceCategory::query()->where('name', $category1)->where('tenant_id', $this->tenant_id)->first();
                        if (!$cat1) {
                            $cat1 = PbServiceCategory::create(['name' => $category1, 'tenant_id' => $this->tenant_id]);
                        }

                        /** @var PbServiceCategory $cat2 */
                        $cat2 = PbServiceCategory::query()
                            ->where('name', $category2)
                            ->where('tenant_id', $this->tenant_id)
                            ->where('parent_id', $cat1->id)
                            ->first();
                        if (!$cat2) {
                            $cat2 = PbServiceCategory::create(['name' => $category2, 'tenant_id' => $this->tenant_id, 'parent_id' => $cat1->id]);
                        }

                        /** @var PbServiceCategory|null $cat3 */
                        $cat3 = null;
                        if ($category3) {
                            $cat3 = PbServiceCategory::query()
                                ->where('name', $category3)
                                ->where('tenant_id', $this->tenant_id)
                                ->where('parent_id', $cat2->id)
                                ->first();
                            if (!$cat3) {
                                $cat3 = PbServiceCategory::create(['name' => $category3, 'tenant_id' => $this->tenant_id, 'parent_id' => $cat2->id]);
                            }
                        }

                        /** @var PbServiceCategory|null $cat4 */
                        $cat4 = null;
                        if ($cat3 && $category4) {
                            $cat4 = PbServiceCategory::query()
                                ->where('name', $category4)
                                ->where('tenant_id', $this->tenant_id)
                                ->where('parent_id', $cat3->id)
                                ->first();
                            if (!$cat4) {
                                $cat4 = PbServiceCategory::create(['name' => $category4, 'tenant_id' => $this->tenant_id, 'parent_id' => $cat3->id]);
                            }
                        }

                        $finalCategoryId = $cat4?->id ?? $cat3?->id ?? $cat2?->id ?? $cat1->id;
                        if ($finalCategoryId) {
                            $serviceData = [
                                'tenant_id' => $this->tenant_id,
                                'name' => $name,
                                'description' => $description,
                                'price' => floatval($price),
                                'cost' => floatval($cost),
                                'taxable' => $taxable == 'yes' ? 1 : 0,
                                'task_code' => $task_code,
                                'unit_of_measurement' => $unit_of_measurement,
                                'is_online_booking' => $is_online_booking == 'yes' ? 1 : 0,
                            ];

                            /** @var PbService $service */
                            $service = PbService::query()->where('name', $name)->tenant($this->tenant_id)->first();
                            if (!$service) {
                                /** @var PbService $service */
                                $service = PbService::create($serviceData);
                            }

                            PbServiceCategoryMap::query()->upsert([
                                'service_id' => $service->id,
                                'category_id' => $finalCategoryId,
                            ], ['service_id', 'category_id']);
                        }
                    }
                }


                SysMsg::get_instance()->success("File uploaded to: $targetFile");
                $this->json(['result' => true]);
            } else {
                http_response_code(500);
                $this->json(['result' => false]);
            }
        }

        $this->render('service_import');
    }


    public function delete()
    {
        if (!isset($_GET['id']) || !isset($_GET['h'])) {
            $this->redirect();
        }
        $id = intval($_GET['id']);
        if (md5("uid$id") != $_GET['h']) {
            $this->redirect();
        }

        PbService::query()->tenant($this->tenant_id)->where('id', $id)->delete();

        SysMsg::get_instance()->success("Deleted successfully.");
        $this->redirect();
    }
}