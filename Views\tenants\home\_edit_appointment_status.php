<?php ?>
<div id="editAppointmentStatusModal2" class="modal-backdrop">
    <div class="modal-content2" style="width: 1400px">
        <div class="modal-header">
            <span class="modal-close estModalClose">&#10006;</span>
            <h3>Edit Job #<span id="jobId">{jobId}</span></h3>
        </div>
        <div class="modal-body">
            <div class="flex -mx-4">
                <div class="w-full flex items-start mx-4">
                  <div class="w-3/8 mx-4">
                    <div class="p-4 border-gray-200 border-none rounded-sm">
                      <div class="w-full">                        
                        <label class="w-full font-semibold pb-4">Appointment Status</label>
                        <div class="flex flex-col items-start flex-wrap gap-3">
                            <label class="flex items-center">
                                <input type="radio" name="appointment_status" value="30" class="mr-2">
                                <span class="px-2 py-1 rounded" style="background-color: #4CAF50; color: white;">Initial Appointment Scheduled</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="appointment_status" value="80" class="mr-2">
                                <span class="px-2 py-1 rounded" style="background-color: #2196F3; color: white;">Technician May Be Delayed to Appointment</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="appointment_status" value="120" class="mr-2">
                                <span class="px-2 py-1 rounded" style="background-color: #FF9800; color: white;">Customer Missed Appointment</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="appointment_status" value="220" class="mr-2">
                                <span class="px-2 py-1 rounded" style="background-color: #673AB7; color: white;">Appointment Completed</span>
                            </label>
                            <label class="flex items-center">
                                <input type="radio" name="appointment_status" value="230" class="mr-2">
                                <span class="px-2 py-1 rounded" style="background-color: #F44336; color: white;">Appointment Cancelled</span>
                            </label>
                        </div>                        
                      </div>

                      <div class="w-full">
                        <div class="flex items-center justify-between mb-2 py-4">
                          <h4 class="mb-3 font-bold">Notes</h4>
                          <button type="button" class="btn-comment-update py-1 px-4 bg-[#00796b] hover:bg-[#004d40] text-white rounded w-auto mx-1">
                              New
                          </button>
                        </div>
                        <div class="" id="notesWrap"
                            style="min-height: 300px; max-height: calc(100vh - 400px); overflow-y: auto; overflow-x: hidden">
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="w-5/8 mx-4">
                    <div class="flex items-center mb-2 px-4 pt-4">
                      <h4 class="mb-3 font-bold">SMS</h4>
                    </div>
                    <div class="mb-4 pl-4" id="smsForm">
                      <div class="mb-3 hidden">
                        <label class="block text-sm font-medium mb-1">To:</label>
                        <input type="text" id="smsTo" class="w-full p-2 border rounded" readonly>
                      </div>
                      <div class="mb-3">
                        <label class="block text-sm font-medium mb-1">Template:</label>
                        <select id="smsTemplate" class="w-full p-2 border rounded mb-2">
                          <option value="">-- Select Template --</option>
                        </select>
                      </div>
                      <div class="mb-3">
                        <label class="block text-sm font-medium mb-1">Message:</label>
                        <textarea id="smsBody" rows="4" class="w-full p-2 border rounded"></textarea>
                      </div>
                      <div class="flex justify-end">
                        <button type="button" class="btn-send-sms py-1 px-4 bg-[#00796b] hover:bg-[#004d40] text-white rounded">Send</button>
                      </div>
                    </div>  
                    <div class="" id="smsWrap"
                        style="min-height: 300px; max-height: calc(100vh - 400px); overflow-y: auto; overflow-x: hidden">
                    </div>  
                  </div>
                </div>
            </div>
        </div>
        <div class="modal-footer flex justify-end items-start">
            <button type="button" class="btn-call py-1 px-4 bg-[#4CAF50] text-white rounded w-auto mx-1">Call Customer</button>
            <button type="button" class="btn-save py-1 px-4 bg-[#00796b] hover:bg-[#004d40] text-white rounded w-auto mx-1">Save</button>
            <button type="button" class="btn-cancel py-1 px-4 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded w-auto mx-1">Cancel</button>
        </div>
    </div>
</div>

<script>
  var EditAppointmentStatusModule = function () {
    const self = {
      modalId: '#editAppointmentStatusModal2',
      modal: null,
      modalUpdateComment: null,
      job: null,
      messages: [],
      selectedMessage: null,
      smsTemplates: []
    }
    self.init = function () {
      this.modal = $(this.modalId);
      this.modalUpdateComment = $('#updateCommentDlg');

      // Save actions
      $('body').on('click', "div#editAppointmentStatusModal2 .modal-footer .btn-save", function (e) {
        const appointmentStatus = self.getSelectedAppointmentStatus();
        if (appointmentStatus) {
          self.updateJob({ appointment_status: appointmentStatus });
        } else {
          App.info("Please select an appointment status");
        }
      });
      
      // Cancel button action
      $('body').on('click', "div#editAppointmentStatusModal2 .modal-footer .btn-cancel", function (e) {
        self.modal.removeClass('active');
      });

      // Call actions
      $('body').on('click', "div#editAppointmentStatusModal2 .modal-footer .btn-call", function (e) {
        const phoneNumber = self.job?.dispatch_customers?.[0]?.customer?.customer_phones?.[0]?.phone;
        console.log("[Make Call]", phoneNumber);
        if (phoneNumber) {
          // makeCallTo("+17407593581");
          makeCallTo(phoneNumber);
        } else {
          App.info("No phone number available for this job.");
        }
      });

      // Open comment dialog
      $('body').on('click', "div#editAppointmentStatusModal2 .btn-comment-update", function (e) {
        self.selectedMessage = self.messages.find(x => x.id == $(this).closest('div.comment-row').data('message-id'));
        self.openCommentDialog(self.selectedMessage);
      });

      // Save comment
      $('body').on('click', "div#updateCommentDlg .modal-content2 .modal-footer .btn-save", function (e) {
        self.saveComment();
      });

      // SMS actions
      $('body').on('click', "div#editAppointmentStatusModal2 .btn-send-sms", function (e) {
        self.sendSms();
      });

      $('body').on('change', "div#editAppointmentStatusModal2 #smsTemplate", function (e) {
        const selectedTemplate = $(this).val();
        if (selectedTemplate) {
          $('#smsBody').val(selectedTemplate);
        }
      });

      $().fancybox({
        selector: '[data-fancybox]',
        // selector: '.comment',
        buttons: [
          "zoom",
          "slideShow",
          "fullScreen",
          "download",
          "close"
        ],
        animationEffect: "fade",
        transitionEffect: "fade",
        preventCaptionOverlap: true,
        idleTime: 3,
        gutter: 50,
        // Disable history to prevent security errors
        hash: false,
        // Disable URL changes
        backFocus: false,
        // Prevent history manipulation
        beforeShow: function (instance, current) {
          // Disable animations if they're causing issues
          if ($.fx) {
            $.fx.off = true;
          }
        },
        afterClose: function (instance, current) {
          // Re-enable animations after closing
          if ($.fx) {
            $.fx.off = false;
          }
        },
        caption: function (instance, item) {
          return $(this).attr('data-caption');
        }
      });
    };

    self.openSmsForm = function () {
      const phoneNumber = self.job?.dispatch_customers?.[0]?.customer?.customer_phones?.[0]?.phone;
      if (!phoneNumber) {
        App.info("No phone number available for this customer.");
        return;
      }

      $('#smsTo').val(phoneNumber);
      $('#smsBody').val('');
      $('#smsForm').removeClass('hidden');
      this.loadSmsTemplates();
    };

    self.closeSmsForm = function () {
      $('#smsForm').addClass('hidden');
    };

    self.loadSmsTemplates = function () {
      App.ajaxPostOk(`${baseApiUrl}/sms/get_sms_templates`, {}, (res) => {
        if (res.success && res.templates) {
          this.smsTemplates = res.templates;
          const $templateSelect = $('#smsTemplate');
          
          // Clear existing options and add default
          $templateSelect.html("<option value=''>-- Select Template --</option>");
          
          // Add templates to dropdown
          this.smsTemplates.forEach(template => {
            $templateSelect.append(`<option value="${template.template_text}">${template.template_text}</option>`);
          });
        }
      });
    };

    self.sendSms = function () {
      const to = $('#smsTo').val();
      const message = $('#smsBody').val().trim();
      
      if (!to || !message) {
        App.error("Please enter a message.");
        return;
      }
      
      App.ajaxPostOk(`${baseApiUrl}/sms/send_sms`, {
        to: to,
        message: message,
        job_id: this.job.id,
        employee_id: userId // Assuming employeeId is available globally
      }, (res) => {
        if (res.success) {
          App.success("SMS sent successfully!");
          $('#smsBody').val(''); // Clear the message field after sending
          this.loadSmsMessages(); // Reload messages to show the new one
        } else {
          App.error("Failed to send SMS: " + res.error);
        }
      });
    };

    self.loadSmsMessages = function () {
      if (!this.job?.id) return;
      
      const phoneNumber = self.job?.dispatch_customers?.[0]?.customer?.customer_phones?.[0]?.phone;
      App.ajaxPostOk(`${baseApiUrl}/employees/employee_conversation`, {phone: phoneNumber}, (res) => {
        const $smsWrap = this.modal.find('#smsWrap');
        
        if (!res.success || !res.messages || res.messages.length === 0) {
          $smsWrap.html('<div class="text-center p-4 text-gray-500">No SMS messages found</div>');
          return;
        }
        
        // Sort messages by date (newest first)
        const messages = res.messages.sort((a, b) => 
          new Date(b.created_at) - new Date(a.created_at)
        );
        
        // Create container for messages
        const $messagesContainer = $('<div>').addClass('space-y-4 p-4');
        
        // Add each message
        messages.forEach(message => {
          const date = new Date(message.timestamp);
          const formattedDate = date.toLocaleDateString() + ' ' +
            date.toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'});
          
          // Create message box with appropriate styling
          const $messageBox = $('<div>').addClass('sms-row rounded-lg shadow p-4 relative')
            .attr('data-message-id', message.id);
          
          // Determine if outgoing or incoming
          const isOutgoing = message.direction === 'outbound';
          
          if (isOutgoing) {
            $messageBox.addClass('bg-green-50 border-l-4 border-green-500');
          } else {
            $messageBox.addClass('bg-blue-50 border-l-4 border-blue-500');
          }
          
          // Message header with direction and date
          const $header = $('<div>').addClass('flex justify-between items-center mb-2');
          
          $header.append(
            $('<span>').addClass('text-sm font-semibold text-gray-800')
              .text(isOutgoing ? 'Outgoing' : 'Incoming'),
            $('<span>').addClass('text-xs text-gray-500').text(formattedDate)
          );
          
          // Message content
          const $content = $('<div>').addClass('text-gray-700 text-sm whitespace-pre-wrap')
            .text(message.body || '');
          
          $messageBox.append($header, $content);
          $messagesContainer.append($messageBox);
        });
        
        // Add container to SMS wrap
        $smsWrap.html($messagesContainer);
      }, {blockEle: this.modal.find('#smsWrap'), type: 'get'});
    };

    self.updateJob = function (data) {
      App.ajaxPostOk(`${baseApiUrl}/jobs/update_job_status`, {job_id: this.job.id, data}, (res) => {
        App.success('Appointment Status updated successfully!');
        loadKanboard();
      }, {blockEle: this.modal});
    }

    self.openEditModal = function (job) {
      this.job = job;
      const phoneNumber = self.job?.dispatch_customers?.[0]?.customer?.customer_phones?.[0]?.phone;
      $("div#editAppointmentStatusModal2 .modal-footer .btn-call")
        .html("Call (" + job?.dispatch_customers?.[0]?.customer?.name + "): " + phoneNumber)
        .attr("data-phone", phoneNumber);
      
      // Initialize SMS form
      $('#smsTo').val(phoneNumber || '');
      $('#smsBody').val('');
      this.loadSmsTemplates();
      
      if (!job?.dispatch_user) {
        App.info("Please assign this job");
      } else {
        this.modal.addClass('active');
        this.modal.find('#jobId').html(job.id);
        
        // Set the initial value of appointment status radio button
        if (job?.dispatch_status?.appointment_status) {
          const status = job.dispatch_status.appointment_status;
          this.modal.find(`input[name="appointment_status"][value="${status}"]`).prop('checked', true);
        }
      }
      
      self.loadJobMessages();
      self.loadSmsMessages();
    }

    self.getSelectedAppointmentStatus = function() {
      // Get the value of the checked radio button
      const selectedValue = this.modal.find('input[name="appointment_status"]:checked').val();
      return selectedValue;
    }

    self.openCommentDialog = function (message = null) {      
      if (message) {
        if (message.message_type === 'comment_type') {
          message.content = message.details?.dispatch_comment || '';
        }
      }

      this.commentId = message ? message.id : null;
      $('#commentText').val(message ? message.content : '');
      $('#commentFiles').val('');
      $('#fileList').empty();

      // Update dialog title based on whether we're editing or creating
      this.modalUpdateComment.find('.modal-header h3').text(message ? 'Edit Comment' : 'New Comment');

      // Show dialog
      this.modalUpdateComment.addClass('active');
    };

    self.saveComment = function () {
      if (!this.job?.id) {
        return;
      }

      const commentText = $('#commentText').val().trim();
      const files = $('#commentFiles')[0].files;

      if (!commentText && files.length === 0) {
        App.error("Please enter a comment or attach files.");
        return;
      }

      // Show loading indicator
      const $saveBtn = this.modalUpdateComment.find('.btn-save');
      const originalBtnText = $saveBtn.text();
      $saveBtn.prop('disabled', true).text('Saving...').addClass('opacity-75');
      console.log(this.job);
      // First save the comment text
      const message = {
        job_id: this.job.id,
        job_comment: commentText
      };

      // If editing, include the comment ID
      if (this.commentId) {
        message.message_id = this.commentId;
      }

      // API endpoint based on whether we're creating or updating
      const endpoint = this.commentId
        ? `${baseApiUrl}/jobs/update_employee_comment`
        : `${baseApiUrl}/jobs/add_employee_comment`;

      App.ajaxPostOk(endpoint, message, (response) => {
        // If we have files to upload
        if (files.length > 0) {
          const messageId = response.comment.message_id ?? this.commentId;
          const formData = new FormData();
          formData.append('message_id', messageId);

          for (let i = 0; i < files.length; i++) {
            formData.append(`files[]`, files[i]);
          }


          App.ajaxPostOk(`${baseApiUrl}/jobs/upload_employee_comment_attachments`, formData, (uploadResponse) => {
            if (uploadResponse.failed && uploadResponse.failed.length > 0) {
              App.error('Some files failed to upload: ' + uploadResponse.failed.join(', '))
            }

            this.finishSaveComment();
          }, {
            blockEle: this.modalUpdateComment,
            complete: (data) => {
              $saveBtn.prop('disabled', false).text(originalBtnText).removeClass('opacity-75');
            },
            processData: false,
            contentType: false,
          }, (err) => {
            App.error(`Failed to upload files` + error);
          });
        } else {
          this.finishSaveComment();
        }
      }, {
        blockEle: this.modalUpdateComment,
        complete: (data) => {
          $saveBtn.prop('disabled', false).text(originalBtnText).removeClass('opacity-75');
        }
      });
    };

    self.finishSaveComment = function () {
      this.closeCommentDialog();
      this.loadJobMessages();
      App.success("Saved successfully!");
    };

    self.closeCommentDialog = function () {
      this.modalUpdateComment.removeClass('active');
      this.commentId = null;
    };

    self.loadJobMessages = function () {
      App.ajaxPostOk(`${baseApiUrl}/jobs/get_employee_comments`, {job_id: self.job.id}, (res) => {
        const $notesWrap = this.modal.find('#notesWrap');

        if (!res.success || !res.messages || res.messages.length === 0) {
          $notesWrap.html('<div class="text-center p-4 text-gray-500">No messages found</div>');
          return;
        }

        const messages = res.messages;
        this.messages = messages;

        // Clear container
        $notesWrap.empty();

        // Create message container
        const $messagesContainer = $('<div>').addClass('space-y-4 p-4');

        // Add each message
        messages.forEach(message => {
          const isNote = message.message_type === "note_type";
          const isNcc = message.message_type === "ncc_type";
          const isComment = message.message_type === "comment_type";
          const isAdminComment = message.message_type === "admin_type";
          const isSystem = isNote || isNcc;

          const details = message.details;

          const date = new Date(message.created_at);
          const formattedDate = date.toLocaleDateString() + ' ' +
            date.toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'});

          // Create message box with appropriate styling
          const $messageBox = $('<div>').addClass('comment-row rounded-lg shadow p-4 relative')
            .attr('data-message-id', message.id)
            .attr('data-ref-id', message.message_id)
          ;

          // Message header with author and date
          const $header = $('<div>').addClass('flex justify-between items-center mb-2');

          // Author name
          let authorName = 'System';
          if (isNote) {
            authorName = details.created_by_text ?? 'Note';
          } else if (isNcc) {
            authorName = 'NCC';
          } else {
            authorName = message.details.user?.user_name;
          }


          $header.append(
            $('<span>').addClass('text-sm font-semibold text-gray-800').text(authorName),
            $('<span>').addClass('text-xs text-gray-500').text(formattedDate)
          );

          // Message content
          const $content = $('<div>').addClass('text-gray-700 text-sm whitespace-pre-wrap');

          if (isNcc) {
            $messageBox.addClass('bg-blue-50 border-l-4 border-blue-500');
            $content.html(details.status || '');
          } else if (isNote) {
            $messageBox.addClass('bg-white border border-gray-200');
            $content.html(details.note || '');
          } else if (isComment) {
            $messageBox.addClass('bg-green-50 border-l-4 border-green-500');
            $content.html(details.dispatch_comment || '');
          } else {
            $messageBox.addClass('bg-orange-50 border-l-4 border-orange-500');
            $content.text(details.dispatch_comment || '');
          }
          $messageBox.append($header, $content);

          // Add attachments if they exist
          if (details.attachments?.length) {
            const $attachmentsContainer = $('<div>').addClass('mt-3 pt-3 border-t border-gray-200');

            // Create grid for attachments
            const $attachmentsGrid = $('<div>').addClass('grid grid-cols-2 sm:grid-cols-3 gap-2');

            details.attachments.forEach(attachment => {
              const isImage = attachment.attachment_type && attachment.attachment_type.startsWith('image/');
              const attachmentUrl = `${baseUrl}/${attachment.attachment_path}`;
              const fileName = attachment.attachment_name || 'File';

              if (isImage) {
                // Create thumbnail container
                const $thumbnailContainer = $('<div>').addClass('relative group2');

                // Create thumbnail
                const $thumbnail = $('<img>')
                  .attr('src', attachmentUrl)
                  .attr('alt', fileName)
                  .addClass('comment w-full h-24 object-cover rounded border border-gray-200 cursor-pointer')
                  .attr('data-fancybox', 'gallery-' + message.id)
                  .attr('data-caption', fileName)
                  .attr('data-src', attachmentUrl);

                // Add overlay with file name on hover
                const $overlay = $('<div>')
                  .addClass('absolute inset-0 bg-black bg-opacity-50 opacity-0 group2-hover:opacity-70 transition-opacity flex items-end justify-center p-1 pointer-events-none')
                  .append($('<span>').addClass('text-white text-xs truncate w-full text-center').text(fileName));

                $thumbnailContainer.append($thumbnail, $overlay);
                $attachmentsGrid.append($thumbnailContainer);
              } else {
                // Create file link with icon
                const $fileContainer = $('<div>').addClass('flex items-center p-2 bg-gray-50 rounded border border-gray-200');

                // Choose icon based on file type
                let fileIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" /></svg>';

                if (attachment.attachment_type) {
                  if (attachment.attachment_type.includes('pdf')) {
                    fileIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" /></svg>';
                  } else if (attachment.attachment_type.includes('word') || attachment.attachment_type.includes('doc')) {
                    fileIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" /></svg>';
                  } else if (attachment.attachment_type.includes('excel') || attachment.attachment_type.includes('sheet')) {
                    fileIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" /></svg>';
                  }
                }

                const $fileLink = $('<a>')
                  .attr('href', attachmentUrl)
                  .attr('target', '_blank')
                  .addClass('flex items-center w-full overflow-hidden')
                  .append($(fileIcon))
                  .append($('<span>').addClass('text-xs text-gray-700 truncate').text(fileName));

                $fileContainer.append($fileLink);
                $attachmentsGrid.append($fileContainer);
              }
            });

            $attachmentsContainer.append($attachmentsGrid);
            $messageBox.append($attachmentsContainer);
          }

          if (isComment) {
            // Action buttons container
            const $actions = $('<div>').addClass('absolute -top-1 right-2 flex opacity-30 group-hover:opacity-100 transition-opacity');
            $messageBox.addClass('group'); // For hover effect on parent

            // Edit button/icon
            const $editBtn = $('<button>')
              .addClass('text-blue-500 hover:text-blue-700 p-1 rounded-full hover:bg-gray-100')
              .attr('title', 'Edit message')
              .html('<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" /></svg>')
              .on('click', (e) => {
                e.preventDefault();
                this.openCommentDialog(message);
              });

            // Delete button/icon
            const $deleteBtn = $('<button>')
              .addClass('text-red-500 hover:text-red-700 p-1 rounded-full hover:bg-gray-100')
              .attr('title', 'Delete message')
              .html('<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" /></svg>')
              .on('click', (e) => {
                e.preventDefault();
                const $btnEle = $(e.currentTarget);
                const messageId = $btnEle.closest('div.comment-row').data('message-id');

                // Create confirm dialog
                App.confirmDlg((uiEle) => {
                  App.ajaxPostOk(`${baseApiUrl}/jobs/delete_employee_comment`, {message_id: messageId}, (res) => {
                    App.success('Deleted successfully!');
                    // this.loadJobMessages();
                    $btnEle.closest('div.comment-row').slideUp('slow');
                  })
                }, {title: 'Delete Message'});
              });

            $actions.append($editBtn, $deleteBtn);
            $messageBox.append($actions);
          }


          $messagesContainer.append($messageBox);
        });

        // Add container to notes wrap
        $notesWrap.append($messagesContainer);

      }, {blockEle: this.modal.find('#notesWrap'), type: 'get'});
    }

    return self;
  }();

  $(document).ready(function () {
    EditAppointmentStatusModule.init();
  });
</script>


