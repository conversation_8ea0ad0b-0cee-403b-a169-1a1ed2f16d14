<?php

namespace Butler\Models;

use <PERSON>\Models\BaseModel;
use Butler\Models\User;

/**
 * @property integer $id
 * @property integer $user_id
 * @property string $user_phone
 * @property string $user_role
 * @property string $user_color
 * @property string $user_address
 * @property string $home_lat
 * @property string $home_lng
 * @property integer $is_forwarding_active
 * @property string $forwarding_number
 * @property string $msg_in_hours
 * @property string $msg_after_hours
 * @property string $created_at
 * @property string $updated_at
 * @property integer $home_geofence_radius
 * @property string $home_geofence_center
 * @property User $user
 */
class UserProfile extends BaseModel
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'user_profile';

    /**
     * @var array
     */
    protected $fillable = ['user_id', 'user_phone', 'user_role', 'user_color', 'user_address', 'home_lat', 'home_lng', 'is_forwarding_active', 'forwarding_number', 'msg_in_hours', 'msg_after_hours', 'created_at', 'updated_at', 'home_geofence_radius', 'home_geofence_center'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }
}

