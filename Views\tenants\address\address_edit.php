<?php

/** @var \Butler\Controllers\tenants\Address\TenantAddressController $this */
/** @var array $dbRow */

/** @var array $addressTypesKv */

?>
<div class="main-content container mx-auto relative h-full" id="main-content">
    <div class="mt-16">
        <div class="flex justify-center w-full mt-8">
            <form method="POST" class="flex flex-col gap-4 w-full max-w-3xl">
                <label class="block text-xl">User ID: <?php echo $dbRow['user_id'] ?? '' ?></label>

                <label class="block text-xl">Name:</label>
                <input type="text" name="user_name" class="w-full border p-2 rounded mb-4" required value="<?php echo $dbRow['user_name'] ?? '' ?>"/>

                <label class="block text-xl">Email:</label>
                <input type="email" name="user_email" class="w-full border p-2 rounded mb-4" required value="<?php echo $dbRow['user_email'] ?? '' ?>"/>

                <label class="block text-xl">Phone:</label>
                <input type="text" name="user_profile[user_phone]" class="w-full border p-2 rounded mb-4" value="<?php echo $dbRow['user_profile']['user_phone'] ?? '' ?>"/>

                <label class="block text-xl">Color:</label>
                <input type="color" name="user_profile[user_color]" class="w-full h-10 border p-2 rounded mb-4" value="<?php echo $dbRow['user_profile']['user_color'] ?? '#FF8B7E' ?>"/>

                <div class="mb-4" id="addressWrap">
                    <label class="block text-xl mb-2">Address:</label>
                    <div class="flex gap-2 mb-2">
                        <input type="text" id="user_address" name="user_profile[user_address]" class="w-full border p-2 rounded" value="<?php echo $dbRow['user_profile']['user_address'] ?? '' ?>"/>
                        <button type="button" id="searchAddressBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                    <div id="mapAddress" class="w-full h-64 border rounded mb-2"></div>
                    <div class="flex gap-4">
                        <div class="w-1/2">
                            <label class="block text-sm">Latitude:</label>
                            <input type="text" id="home_lat" name="user_profile[home_lat]" class="w-full border p-2 rounded" value="<?php echo $dbRow['user_profile']['home_lat'] ?? '' ?>"/>
                        </div>
                        <div class="w-1/2">
                            <label class="block text-sm">Longitude:</label>
                            <input type="text" id="home_lng" name="user_profile[home_lng]" class="w-full border p-2 rounded" value="<?php echo $dbRow['user_profile']['home_lng'] ?? '' ?>"/>
                        </div>
                    </div>
                </div>

                <label class="block text-xl">User Type</label>
                <select name="user_type" class="w-full border p-2 rounded mb-4">
                    <option class="text-black bg-white text-black dark:text-black dark:bg-white" value="address">Employee</option>
                </select>

                <label class="block text-xl">Roles</label>
                <?php echo \Butler\Helpers\FuncHtml::form_dropdown('tenant_role_id', array_replace([''=> ''], $roles), $dbRow['tenant_roles'][0]['id'] ?? null, "class='w-full border p-2 rounded'") ?>


                <label class="block text-xl">GPS Device</label>
                <?php echo \Butler\Helpers\FuncHtml::form_dropdown('device_id', array_replace([''=> ''], $devices), $dbRow['driver_device']['id'] ?? null, "class='w-full border p-2 rounded'") ?>

                <label class="block text-xl">Password: </label>
                <input type="password" name="password" class="w-full border p-2 rounded mb-4"/>

                <label class="block text-xl">Confirm Password: </label>
                <input type="password" name="password_confirmed" class="w-full border p-2 rounded mb-4"/>

                <div class="flex gap-3">
                    <button type="submit" class="w-full bg-[#00796b] hover:bg-[#004d40] text-white p-2 rounded cursor-pointer">Update</button>
                    <a href="<?php echo $this->controllerBaseUrl ?>"
                       class="w-full bg-gray-100 border-1 border-gray-200 hover:border-gray-300 p-2 rounded text-gray-900 text-center cursor-pointer">Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
<script src="<?php echo BASE_URL; ?>assets/js/google_map.js"></script>
<?php require_once '_address_form_base.php' ?>