import React from 'react';
import { Form, Input, But<PERSON>, Select, DatePicker, Card, message } from 'antd';
interface FormField {
    name: string;
    label: string;
    type: 'text' | 'email' | 'select' | 'date';
    required?: boolean;
    options?: Array<{ label: string; value: any }>;
}
interface CustomFormProps {
    title?: string;
    fields?: FormField[];
    layout?: 'horizontal' | 'vertical';
    onSubmit?: (values: any) => void;
}
const CustomForm: React.FC<CustomFormProps> = ({
    title = 'Custom Form',
    fields = [],
    layout = 'vertical',
    onSubmit,
}) => {
    const [form] = Form.useForm();
    const defaultFields: FormField[] = [
        { name: 'name', label: 'Name', type: 'text', required: true },
        { name: 'email', label: 'Email', type: 'email', required: true },
        { name: 'phone', label: 'Phone', type: 'text' },
    ];
    const formFields = fields.length > 0 ? fields : defaultFields;
    const handleSubmit = (values: any) => {
        if (onSubmit) {
            onSubmit(values);
        } else {
            message.success('Form submitted successfully!');
            console.log('Form values:', values);
        }
    };
    const renderField = (field: FormField) => {
        const rules = field.required ? [{ required: true, message: `Please input ${ field.label }!` }] : [];

    switch (field.type) {
        case 'email':
            return (
                <Form.Item
                    key={field.name}
                    name={field.name}
                    label={field.label}
                    rules={[...rules, { type: 'email', message: 'Please enter a valid email!' }]}
                >
                    <Input />
                </Form.Item>
            );
        case 'select':
            return (
                <Form.Item key={field.name} name={field.name} label={field.label} rules={rules}>
                    <Select>
                        {field.options?.map(option => (
                            <Select.Option key={option.value} value={option.value}>
                                {option.label}
                            </Select.Option>
                        ))}
                    </Select>
                </Form.Item>
            );
        case 'date':
            return (
                <Form.Item key={field.name} name={field.name} label={field.label} rules={rules}>
                    <DatePicker style={{ width: '100%' }} />
                </Form.Item>
            );
        default:
            return (
                <Form.Item key={field.name} name={field.name} label={field.label} rules={rules}>
                    <Input />
                </Form.Item>
            );
    }
}


return (
    <div>
        {formFields.map(renderField)}

        <Form.Item>
            <Button style={{ marginLeft: 8 }} onClick={() => form.resetFields()}>
                Reset</Button>
        </Form.Item>
    </div>
);
};
export default CustomForm;