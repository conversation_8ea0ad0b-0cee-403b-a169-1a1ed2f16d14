<?php

declare(strict_types=1);

namespace Butler\Controllers\api;

use <PERSON>\Helpers\Constants;
use <PERSON>\Helpers\Func;
use <PERSON>\Lib\FileLib;
use Butler\Models\File;

final class DownloadFileApi extends ApiBaseController
{
    /**
     * download file with File object.
     *
     * @return void
     */
    public function download()
    {
        $params = $this->data;
        $idSecured = $params['id'];
        // security key version.
        $kv = $params['kv'] ?? 1;

        $id = Func::mcrypt('decrypt', $idSecured, ...Constants::FILE_SEC_KEYS[$kv]);
        if (!$id) {
            $this->jsonWithError(new \Exception('File not found or invalid request.'));
        }

        /** @var File $file */
        $file = File::findOrFail($id);
        $file->increment('hits');


        $force = $params['force'] ?? null;


        $filePath = $file->abs_path;
        if (!file_exists($filePath)) {
            \App\Exception\Base::raiseInvalidRequest('File not exists. Absolute path: ' . $filePath);
        }

        $tmpFileLib = new FileLib('');
        $mime = $tmpFileLib->mime2ext($file->type, true);

        $info = pathinfo($filePath);
        $filename = $info['basename'];
        $x = explode('.', $filename);
        $extension = end($x);

        $filesize = @filesize($filePath);

        /*if ($set_mime === TRUE) {
            if (count($x) === 1 or $extension === '') {
                /* If we're going to detect the MIME type,
                 * we'll need a file extension.
                 * /
                return;
            }
        }*/

        /* It was reported that browsers on Android 2.1 (and possibly older as well)
         * need to have the filename extension upper-cased in order to be able to
         * download it.
         *
         * Reference: http://digiblog.de/2011/04/19/android-and-the-download-file-headers/
         */
        if (count($x) !== 1 && isset($_SERVER['HTTP_USER_AGENT']) && preg_match('/Android\s(1|2\.[01])/', $_SERVER['HTTP_USER_AGENT'])) {
            $x[count($x) - 1] = strtoupper($extension);
            $filename = implode('.', $x);
        }

        if (($fp = @fopen($filePath, 'rb')) === FALSE) {
            \App\Exception\Base::raiseInvalidRequest('Internal server error while opening file.', 500);
        }

        // Clean output buffer
        if (ob_get_level() !== 0 && @ob_end_clean() === FALSE) {
            @ob_clean();
        }

        // Generate the server headers
        header('Content-Type: ' . $mime);
        if ($force)
            header('Content-Disposition: attachment; filename="' . $filename . '"');
        else
            header('Content-Disposition: inline; filename="' . $filename . '"');

        header('Expires: 0');
        header('Content-Transfer-Encoding: binary');
        header('Content-Length: ' . $filesize);
        header('Cache-Control: private, no-transform, no-store, must-revalidate');

        // Flush 1MB chunks of data
        while (!feof($fp) && ($data = fread($fp, 1048576)) !== FALSE) {
            echo $data;
        }

        fclose($fp);

        exit;
    }

    /**
     * Download file without file object.
     *
     * @return void
     */
    public function downloadByPath() {
        $params = $this->data;
        $key = urldecode($params['k'] ?? '');
        $type = ($params['type'] ?? '');
        $b64 = $params['b64'] ?? null;

        if (!$key) {
            $this->jsonWithError(new \Exception('Type and Key are required!'));
        }

        $force = $params['force'] ?? null;

        $filePath = UPLOAD_PATH . DS . $key;

        if (file_exists($filePath)) {

            if ($b64) {
                $result = ['b64' => base64_encode(file_get_contents($filePath))];
                return $this->jsonWithSuccess($result);
            }

            $mime = FileLib::mime2ext($type, true);

            $info = pathinfo($key);
            $filename = $info['basename'];
            $x = explode('.', $filename);
            $extension = end($x);

            $filesize = @filesize($filePath);

            /*if ($set_mime === TRUE) {
                if (count($x) === 1 or $extension === '') {
                    /* If we're going to detect the MIME type,
                     * we'll need a file extension.
                     * /
                    return;
                }
            }*/

            /* It was reported that browsers on Android 2.1 (and possibly older as well)
             * need to have the filename extension upper-cased in order to be able to
             * download it.
             *
             * Reference: http://digiblog.de/2011/04/19/android-and-the-download-file-headers/
             */
            if (count($x) !== 1 && isset($_SERVER['HTTP_USER_AGENT']) && preg_match('/Android\s(1|2\.[01])/', $_SERVER['HTTP_USER_AGENT'])) {
                $x[count($x) - 1] = strtoupper($extension);
                $filename = implode('.', $x);
            }

            if (($fp = @fopen($filePath, 'rb')) === FALSE) {
                $this->jsonWithError(new \Exception('Internal server error while opening file.'));
            }

            // Clean output buffer
            if (ob_get_level() !== 0 && @ob_end_clean() === FALSE) {
                @ob_clean();
            }

            // Generate the server headers
            if ($force)
                header('Content-Disposition: attachment; filename="' . $filename . '"');
            else
                header('Content-Disposition: inline; filename="' . $filename . '"');

            // Generate the server headers
            header('Content-Type: ' . $mime);
            header('Expires: 0');
            header('Content-Transfer-Encoding: binary');
            header('Content-Length: ' . $filesize);
            header('Cache-Control: private, no-transform, no-store, must-revalidate');

            // Flush 1MB chunks of data
            while (!feof($fp) && ($data = fread($fp, 1048576)) !== FALSE) {
                echo $data;
            }

            fclose($fp);
            exit;
        }


    }
}
