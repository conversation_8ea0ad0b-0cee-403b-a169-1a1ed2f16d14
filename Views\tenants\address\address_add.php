<?php
/** @var \Butler\Controllers\tenants\Address\TenantAddressController $this */
/** @var array $dbRow */
/** @var array $addressTypesKv */

$dbRow = $this->data ?? [];
?>
<div class="main-content container mx-auto relative h-full" id="main-content">
    <div class="mt-16">
        <div class="flex justify-center w-full mt-8">
            <form method="POST" class="flex flex-col gap-4 w-full max-w-3xl">
                <label class="block text-xl">Address Type</label>
                <?php echo \Butler\Helpers\FuncHtml::form_dropdown('address_type', array_replace([''=> ''], $addressTypesKv), null, "class='w-full border p-2 rounded'") ?>

                <label class="block text-xl">Company:</label>
                <input type="text" name="name" class="w-full border p-2 rounded mb-4" required value="<?php echo $dbRow['company'] ?? '' ?>"/>

                <label class="block text-xl">Email:</label>
                <input type="email" name="email" class="w-full border p-2 rounded mb-4" required value="<?php echo $dbRow['email'] ?? '' ?>"/>

                <label class="block text-xl">Name:</label>
                <input type="text" name="name" class="w-full border p-2 rounded mb-4" required value="<?php echo $dbRow['name'] ?? '' ?>"/>

                <label class="block text-xl">Phone:</label>
                <input type="text" name="phone" class="w-full border p-2 rounded mb-4" value="<?php echo $dbRow['phone'] ?? '' ?>"/>

                <div class="mb-4" id="addressWrap">
                    <div class="felx -mx-8">
                        <div class="w-2/3">
                            <label class="block text-xl">Street Number:</label>
                            <input type="text" name="street_number" class="w-full border p-2 rounded mb-4" value="<?php echo $dbRow['street_number'] ?? '' ?>"/>
                            <label class="block text-xl">Street Name:</label>
                            <input type="text" name="street_name" class="w-full border p-2 rounded mb-4" value="<?php echo $dbRow['street_name'] ?? '' ?>"/>
                            <label class="block text-xl">Street Direction:</label>
                            <input type="text" name="street_direction" class="w-full border p-2 rounded mb-4" value="<?php echo $dbRow['street_direction'] ?? '' ?>"/>
                            <label class="block text-xl">City:</label>
                            <input type="text" name="city" class="w-full border p-2 rounded mb-4" value="<?php echo $dbRow['city'] ?? '' ?>"/>
                            <label class="block text-xl">State:</label>
                            <input type="text" name="state" class="w-full border p-2 rounded mb-4" value="<?php echo $dbRow['state'] ?? '' ?>"/>
                            <label class="block text-xl">Zip:</label>
                            <input type="text" name="zip" class="w-full border p-2 rounded mb-4" value="<?php echo $dbRow['zip'] ?? '' ?>"/>
                        </div>
                        <div class="w-2/3">
                            <label class="block text-xl mb-2">Address:</label>
                            <div class="flex gap-2 mb-2">
                                <input type="text" id="address" name="address" class="w-full border p-2 rounded" value="<?php echo $dbRow['full_address'] ?? '' ?>"/>
                                <button type="button" id="searchAddressBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </div>
                            <div id="mapAddress" class="w-full h-64 border rounded mb-2"></div>
                        </div>
                    </div>

                </div>

                <div class="flex gap-3">
                    <button type="submit" class="w-full bg-[#00796b] hover:bg-[#004d40] text-white p-2 rounded cursor-pointer">Add</button>
                    <a href="<?php echo $this->controllerBaseUrl ?>"
                       class="w-full bg-gray-100 border-1 border-gray-200 hover:border-gray-300 p-2 rounded text-gray-900 text-center cursor-pointer">Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
<script src="<?php echo BASE_URL; ?>assets/js/google_map.js"></script>
<?php require_once '_address_form_base.php' ?>