<?php ?>
<div id="editJobModal2" class="modal-backdrop">
    <div class="modal-content2" style="width: 1400px">
        <div class="modal-header">
            <span class="modal-close estModalClose">&#10006;</span>
            <h3>Edit Job #<span id="jobId">{jobId}</span></h3>
        </div>
        <div class="modal-body">
            <div class="flex -mx-4">
                <div class="w-5/8 mx-4">
                    <div class="estimateDetail p-4 border-gray-200 border-none rounded-sm">
                        <div class="modal-row">
                            <label for="estNo" style="width: 150px">Tech:</label>
                            <select id="editJobTech"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5"
                            ></select>
                        </div>

                        <div class="modal-row">
                            <label for="estNo" style="width: 150px">Status:</label>
                            <select id="editJobStatus"
                                    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5"
                            >
                              <option value="260">Accepted</option>
                              <option value="150">On Hold</option>
                              <option value="140">Incompleted</option>
                              <option value="40">Canceled</option>
                              <option value="20">In Progress</option>
                              <option value="10">Completed</option>
                            </select>
                        </div>

                        <div class="modal-row flex pt-4">
                            <label for="issueDate" style="width: 150px">Customer:</label>
                            <div class="customerDetail border-gray-200">
                                <h5 class="name font-bold"></h5>
                                <div class="address my-2"></div>
                                <div class="phone my-2"></div>
                            </div>
                        </div>

                        <div class="modal-row">
                            <label for="issueDate" style="width: 150px">Tasks:</label>
                        </div>
                        <div class="modal-row" id="tasksWrap"></div>
                    </div>
                </div>
                <div class="w-3/8 mx-4" id="notesWrapOuter">
                    <div class="flex items-center justify-between mb-2 pl-4">
                        <h4 class="mb-3 font-bold">Notes</h4>
                        <button type="button" class="btn-comment-update py-1 px-4 bg-[#00796b] hover:bg-[#004d40] text-white rounded w-auto mx-1">
                            New
                        </button>
                    </div>
                    <div class="" id="notesWrap"
                         style="min-height: 300px; max-height: calc(100vh - 400px); overflow-y: auto; overflow-x: hidden"></div>
                </div>
            </div>
        </div>
        <div class="modal-footer flex justify-end items-start">
            <button type="button" class="btn-save py-1 px-4 bg-[#00796b] hover:bg-[#004d40] text-white rounded w-auto mx-1">Save</button>
            <button type="button" class="btn-cancel py-1 px-4 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded w-auto mx-1">Cancel</button>
        </div>
    </div>
</div>

<script>
  var EditJobModule = function () {
    const self = {
      modalId: '#editJobModal2',
      modal: null,
      modalUpdateComment: null,
      job: null,
      messages: [],
      selectedMessage: null,
    }
    self.init = function () {
      this.modal = $(this.modalId);
      this.modalUpdateComment = $('#updateCommentDlg');

      // Save actions
      $('body').on('click', "div#editJobModal2 .modal-footer .btn-save", function (e) {
        self.updateJob({
          assigned_techs: self.modal.find('#editJobTech').val(),
          job_status: self.modal.find('#editJobStatus').val()
        });
      });

      // Open comment dialog
      $('body').on('click', "div#editJobModal2 .btn-comment-update", function (e) {
        self.selectedMessage = self.messages.find(x => x.id == $(this).closest('div.comment-row').data('message-id'));
        self.openCommentDialog(self.selectedMessage);
      });

      // Save comment
      $('body').on('click', "div#updateCommentDlg .modal-content2 .modal-footer .btn-save", function (e) {
        self.saveComment();
      });

      $().fancybox({
        selector: '[data-fancybox]',
        // selector: '.comment',
        buttons: [
          "zoom",
          "slideShow",
          "fullScreen",
          "download",
          "close"
        ],
        animationEffect: "fade",
        transitionEffect: "fade",
        preventCaptionOverlap: true,
        idleTime: 3,
        gutter: 50,
        // Disable history to prevent security errors
        hash: false,
        // Disable URL changes
        backFocus: false,
        // Prevent history manipulation
        beforeShow: function (instance, current) {
          // Disable animations if they're causing issues
          if ($.fx) {
            $.fx.off = true;
          }
        },
        afterClose: function (instance, current) {
          // Re-enable animations after closing
          if ($.fx) {
            $.fx.off = false;
          }
        },
        caption: function (instance, item) {
          return $(this).attr('data-caption');
        }
      });
    };

    self.updateJob = function (data) {
      App.ajaxPostOk(`${baseApiUrl}/jobs/updateAssignedTechs`, {job_id: this.job.id, ...data}, (res) => {
        App.success('Job updated successfully!');        
      }, {blockEle: this.modal});

      App.ajaxPostOk(`${baseApiUrl}/jobs/update_job_status`, {job_id: this.job.id, data}, (res) => {        
        loadKanboard();
      }, {blockEle: this.modal});
    }

    self.openEditModal = function (job) {
      this.job = job;
      this.modal.addClass('active');
      this.modal.find('#jobId').html(job.id);

      const $techSelectModal = this.modal.find("#editJobTech");
      $techSelectModal.html('<option value="">(Unassigned)</option>');
      $.each(employeeMapById, function (id, e) {
        $techSelectModal.append($("<option>").val(e.id).text(e.user_name));
      });
      $techSelectModal.val(job.dispatch_user?.user_id || '');

      const $statusSelectModal = this.modal.find("#editJobStatus");
      $statusSelectModal.val(job.dispatch_status?.job_status || '');

      // setting tasks
      const taskData = job.dispatch_items;

      // Create table element
      const $table = $('<table>').addClass('w-full border-collapse rounded-md');

      // Create table header
      const $thead = $('<thead>').addClass('bg-gray-100');
      const $headerRow = $('<tr>');

      // Add header columns
      $headerRow.append(
        $('<th>').addClass('border border-gray-300 px-4 py-2 text-left').text('Task'),
        $('<th>').addClass('border border-gray-300 px-4 py-2 text-left').text('Status'),
        $('<th>').addClass('border border-gray-300 px-4 py-2 text-left').text('Symptoms'),
      );

      $thead.append($headerRow);
      $table.append($thead);
      const $tbody = $('<tbody>');

      // Add rows for each task
      taskData.forEach(task => {
        const $row = $('<tr>').addClass('hover:bg-gray-50');

        // Task name cell
        $row.append(
          $('<td>').addClass('border border-gray-300 px-4 py-2').text(task.description)
        );

        // Status cell with colored badge
        const statusClass = task.status === 'Completed' ? 'bg-green-100 text-green-800' :
          task.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
            'bg-yellow-100 text-yellow-800';

        $row.append(
          $('<td>').addClass('border border-gray-300 px-4 py-2').append(
            $('<span>').addClass(`inline-block rounded-full px-2 py-1 text-xs font-semibold ${statusClass}`).text(task.status)
          )
        );

        let $symptoms = $('<div>');
        if (task.symptoms?.length) {
          task.symptoms.forEach(x => {
            $symptoms.append($('<div>').html(typeof x.content === 'string' ? x.content : x.content?.join(', ')));
          });
        }
        $row.append($('<td>').addClass('border border-gray-300 px-4 py-2').html($symptoms.html()));
        $tbody.append($row);
      });

      $table.append($tbody);

      this.modal.find('#tasksWrap').empty();
      this.modal.find('#tasksWrap').append($table);

      const customer = this.job.dispatch_customers?.[0]?.customer || {};
      const addr = customer.customer_properties?.[0] || {};
      const phone = customer.customer_phones?.[0] || {};
      this.modal.find(".customerDetail .name").html(customer.name);
      this.modal.find(".customerDetail .address").html(Est.getFullAddress(addr));
      this.modal.find(".customerDetail .phone").html(phone.phone);

      this.loadJobMessages();
    }

    self.openCommentDialog = function (message = null) {
      if (message) {
        if (message.message_type === 'comment_type') {
          message.content = message.details?.dispatch_comment || '';
        }
      }

      this.commentId = message ? message.id : null;
      $('#commentText').val(message ? message.content : '');
      $('#commentFiles').val('');
      $('#fileList').empty();

      // Update dialog title based on whether we're editing or creating
      this.modalUpdateComment.find('.modal-header h3').text(message ? 'Edit Comment' : 'New Comment');

      // Show dialog
      this.modalUpdateComment.addClass('active');
    };

    self.closeCommentDialog = function () {
      this.modalUpdateComment.removeClass('active');
      this.commentId = null;
    };

    self.updateFileList = function () {
      const files = $('#commentFiles')[0].files;
      const $fileList = $('#fileList');
      $fileList.empty();

      if (files.length > 0) {
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          const $fileItem = $('<div>').addClass('flex items-center justify-between p-2 bg-gray-50 rounded');

          // File info
          const $fileInfo = $('<div>').addClass('flex items-center');

          // File icon based on type
          let iconHtml = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" /></svg>';

          if (file.type.startsWith('image/')) {
            iconHtml = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>';
          }

          $fileInfo.append($(iconHtml));

          // File name and size
          const fileSize = (file.size / 1024).toFixed(2) + ' KB';
          $fileInfo.append($('<span>').addClass('text-sm').text(`${file.name} (${fileSize})`));

          $fileItem.append($fileInfo);
          $fileList.append($fileItem);
        }
      }
    };

    self.saveComment = function () {
      if (!this.job?.id) {
        return;
      }
      
      const commentText = $('#commentText').val().trim();
      const files = $('#commentFiles')[0].files;

      if (!commentText && files.length === 0) {
        App.error("Please enter a comment or attach files.");
        return;
      }

      // Show loading indicator
      const $saveBtn = this.modalUpdateComment.find('.btn-save');
      const originalBtnText = $saveBtn.text();
      $saveBtn.prop('disabled', true).text('Saving...').addClass('opacity-75');

      // First save the comment text
      const message = {
        job_id: this.job.id,
        job_comment: commentText
      };

      // If editing, include the comment ID
      if (this.commentId) {
        message.message_id = this.commentId;
      }

      // API endpoint based on whether we're creating or updating
      const endpoint = this.commentId
        ? `${baseApiUrl}/jobs/update_employee_comment`
        : `${baseApiUrl}/jobs/add_employee_comment`;

      App.ajaxPostOk(endpoint, message, (response) => {
        // If we have files to upload
        if (files.length > 0) {
          const messageId = response.comment.message_id ?? this.commentId;
          const formData = new FormData();
          formData.append('message_id', messageId);

          for (let i = 0; i < files.length; i++) {
            formData.append(`files[]`, files[i]);
          }


          App.ajaxPostOk(`${baseApiUrl}/jobs/upload_employee_comment_attachments`, formData, (uploadResponse) => {
            if (uploadResponse.failed && uploadResponse.failed.length > 0) {
              App.error('Some files failed to upload: ' + uploadResponse.failed.join(', '))
            }

            this.finishSaveComment();
          }, {
            blockEle: this.modalUpdateComment,
            complete: (data) => {
              $saveBtn.prop('disabled', false).text(originalBtnText).removeClass('opacity-75');
            },
            processData: false,
            contentType: false,
          }, (err) => {
            App.error(`Failed to upload files` + error);
          });
        } else {
          this.finishSaveComment();
        }
      }, {
        blockEle: this.modalUpdateComment,
        complete: (data) => {
          $saveBtn.prop('disabled', false).text(originalBtnText).removeClass('opacity-75');
        }
      });
    };

    self.finishSaveComment = function () {
      this.closeCommentDialog();
      this.loadJobMessages();
      App.success("Saved successfully!");
    };

    // Load job messages
    self.loadJobMessages = function () {
      App.ajaxPostOk(`${baseApiUrl}/jobs/get_employee_comments`, {job_id: self.job.id}, (res) => {
        const $notesWrap = this.modal.find('#notesWrap');

        if (!res.success || !res.messages || res.messages.length === 0) {
          $notesWrap.html('<div class="text-center p-4 text-gray-500">No messages found</div>');
          return;
        }

        const messages = res.messages;
        this.messages = messages;

        // Clear container
        $notesWrap.empty();

        // Create message container
        const $messagesContainer = $('<div>').addClass('space-y-4 p-4');

        // Add each message
        messages.forEach(message => {
          const isNote = message.message_type === "note_type";
          const isNcc = message.message_type === "ncc_type";
          const isComment = message.message_type === "comment_type";
          const isAdminComment = message.message_type === "admin_type";
          const isSystem = isNote || isNcc;

          const details = message.details;

          const date = new Date(message.created_at);
          const formattedDate = date.toLocaleDateString() + ' ' +
            date.toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'});

          // Create message box with appropriate styling
          const $messageBox = $('<div>').addClass('comment-row rounded-lg shadow p-4 relative')
            .attr('data-message-id', message.id)
            .attr('data-ref-id', message.message_id)
          ;

          // Message header with author and date
          const $header = $('<div>').addClass('flex justify-between items-center mb-2');

          // Author name
          let authorName = 'System';
          if (isNote) {
            authorName = details.created_by_text ?? 'Note';
          } else if (isNcc) {
            authorName = 'NCC';
          } else {
            authorName = message.details.user?.user_name;
          }


          $header.append(
            $('<span>').addClass('text-sm font-semibold text-gray-800').text(authorName),
            $('<span>').addClass('text-xs text-gray-500').text(formattedDate)
          );

          // Message content
          const $content = $('<div>').addClass('text-gray-700 text-sm whitespace-pre-wrap');

          if (isNcc) {
            $messageBox.addClass('bg-blue-50 border-l-4 border-blue-500');
            $content.html(details.status || '');
          } else if (isNote) {
            $messageBox.addClass('bg-white border border-gray-200');
            $content.html(details.note || '');
          } else if (isComment) {
            $messageBox.addClass('bg-green-50 border-l-4 border-green-500');
            $content.html(details.dispatch_comment || '');
          } else {
            $messageBox.addClass('bg-orange-50 border-l-4 border-orange-500');
            $content.text(details.dispatch_comment || '');
          }
          $messageBox.append($header, $content);

          // Add attachments if they exist
          if (details.attachments?.length) {
            const $attachmentsContainer = $('<div>').addClass('mt-3 pt-3 border-t border-gray-200');

            // Create grid for attachments
            const $attachmentsGrid = $('<div>').addClass('grid grid-cols-2 sm:grid-cols-3 gap-2');

            details.attachments.forEach(attachment => {
              const isImage = attachment.attachment_type && attachment.attachment_type.startsWith('image/');
              const attachmentUrl = `${baseUrl}/${attachment.attachment_path}`;
              const fileName = attachment.attachment_name || 'File';

              if (isImage) {
                // Create thumbnail container
                const $thumbnailContainer = $('<div>').addClass('relative group2');

                // Create thumbnail
                const $thumbnail = $('<img>')
                  .attr('src', attachmentUrl)
                  .attr('alt', fileName)
                  .addClass('comment w-full h-24 object-cover rounded border border-gray-200 cursor-pointer')
                  .attr('data-fancybox', 'gallery-' + message.id)
                  .attr('data-caption', fileName)
                  .attr('data-src', attachmentUrl);

                // Add overlay with file name on hover
                const $overlay = $('<div>')
                  .addClass('absolute inset-0 bg-black bg-opacity-50 opacity-0 group2-hover:opacity-70 transition-opacity flex items-end justify-center p-1 pointer-events-none')
                  .append($('<span>').addClass('text-white text-xs truncate w-full text-center').text(fileName));

                $thumbnailContainer.append($thumbnail, $overlay);
                $attachmentsGrid.append($thumbnailContainer);
              } else {
                // Create file link with icon
                const $fileContainer = $('<div>').addClass('flex items-center p-2 bg-gray-50 rounded border border-gray-200');

                // Choose icon based on file type
                let fileIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" /></svg>';

                if (attachment.attachment_type) {
                  if (attachment.attachment_type.includes('pdf')) {
                    fileIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" /></svg>';
                  } else if (attachment.attachment_type.includes('word') || attachment.attachment_type.includes('doc')) {
                    fileIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" /></svg>';
                  } else if (attachment.attachment_type.includes('excel') || attachment.attachment_type.includes('sheet')) {
                    fileIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" /></svg>';
                  }
                }

                const $fileLink = $('<a>')
                  .attr('href', attachmentUrl)
                  .attr('target', '_blank')
                  .addClass('flex items-center w-full overflow-hidden')
                  .append($(fileIcon))
                  .append($('<span>').addClass('text-xs text-gray-700 truncate').text(fileName));

                $fileContainer.append($fileLink);
                $attachmentsGrid.append($fileContainer);
              }
            });

            $attachmentsContainer.append($attachmentsGrid);
            $messageBox.append($attachmentsContainer);
          }

          if (isComment) {
            // Action buttons container
            const $actions = $('<div>').addClass('absolute -top-1 right-2 flex opacity-30 group-hover:opacity-100 transition-opacity');
            $messageBox.addClass('group'); // For hover effect on parent

            // Edit button/icon
            const $editBtn = $('<button>')
              .addClass('text-blue-500 hover:text-blue-700 p-1 rounded-full hover:bg-gray-100')
              .attr('title', 'Edit message')
              .html('<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" /></svg>')
              .on('click', (e) => {
                e.preventDefault();
                this.openCommentDialog(message);
              });

            // Delete button/icon
            const $deleteBtn = $('<button>')
              .addClass('text-red-500 hover:text-red-700 p-1 rounded-full hover:bg-gray-100')
              .attr('title', 'Delete message')
              .html('<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" /></svg>')
              .on('click', (e) => {
                e.preventDefault();
                const $btnEle = $(e.currentTarget);
                const messageId = $btnEle.closest('div.comment-row').data('message-id');

                // Create confirm dialog
                App.confirmDlg((uiEle) => {
                  App.ajaxPostOk(`${baseApiUrl}/jobs/delete_employee_comment`, {message_id: messageId}, (res) => {
                    App.success('Deleted successfully!');
                    // this.loadJobMessages();
                    $btnEle.closest('div.comment-row').slideUp('slow');
                  })
                }, {title: 'Delete Message'});
              });

            $actions.append($editBtn, $deleteBtn);
            $messageBox.append($actions);
          }


          $messagesContainer.append($messageBox);
        });

        // Add container to notes wrap
        $notesWrap.append($messagesContainer);

      }, {blockEle: this.modal.find('#notesWrap'), type: 'get'});
    }

    return self;
  }();

  $(document).ready(function () {
    EditJobModule.init();
  });
</script>


