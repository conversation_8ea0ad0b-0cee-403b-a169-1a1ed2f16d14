<?php
/**
 * <PERSON><PERSON> job script to synchronize data from Development Butler
 * 
 * (Runs every 4 hours)
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once __DIR__ . '/../custom-autoloader.php';

use <PERSON>\Controllers\api\SyncApi;
use Butler\Config\SiteConfig;
use Illuminate\Database\Capsule\Manager as Capsule;

// Initialize database connection
$capsule = new Capsule;
$config = SiteConfig::getSiteConfig();
$capsule->addConnection([
    'driver' => 'mysql',
    'host' => $config['DB_HOST'],
    'database' => $config['DB_NAME'],
    'username' => $config['DB_USER'],
    'password' => $config['DB_PASS'],
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_general_ci',
    'prefix' => '',
]);

$capsule->setAsGlobal();
$capsule->bootEloquent();

// Create instance of S<PERSON><PERSON><PERSON> controller
$syncApi = new SyncApi();

try {
    // Run the sync function
    $result = $syncApi->syncDevelopmentButler();
    
    // Log success
    error_log(date('Y-m-d H:i:s') . " - Development Butler sync completed successfully\n", 3, __DIR__ . '/../logs/sync.log');
    
    echo "Sync completed successfully\n";
} catch (\Exception $e) {
    // Log error
    error_log(date('Y-m-d H:i:s') . " - Development Butler sync failed: " . $e->getMessage() . "\n", 3, __DIR__ . '/../logs/sync.log');
    
    echo "Sync failed: " . $e->getMessage() . "\n";
}
