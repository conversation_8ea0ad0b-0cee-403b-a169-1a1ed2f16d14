<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Api\V2010\Account\IncomingPhoneNumber;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Options;
use Twilio\Stream;
use Twilio\Values;
use Twilio\Version;
use Twilio\Serialize;


class MobileList extends ListResource
    {
    /**
     * Construct the MobileList
     *
     * @param Version $version Version that contains the resource
     * @param string $accountSid The SID of the [Account](https://www.twilio.com/docs/iam/api/account) that will create the resource.
     */
    public function __construct(
        Version $version,
        string $accountSid
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        'accountSid' =>
            $accountSid,
        
        ];

        $this->uri = '/Accounts/' . \rawurlencode($accountSid)
        .'/IncomingPhoneNumbers/Mobile.json';
    }

    /**
     * Create the MobileInstance
     *
     * @param string $phoneNumber The phone number to purchase specified in [E.164](https://www.twilio.com/docs/glossary/what-e164) format.  E.164 phone numbers consist of a + followed by the country code and subscriber number without punctuation characters. For example, +***********.
     * @param array|Options $options Optional Arguments
     * @return MobileInstance Created MobileInstance
     * @throws TwilioException When an HTTP error occurs.
     */
    public function create(string $phoneNumber, array $options = []): MobileInstance
    {

        $options = new Values($options);

        $data = Values::of([
            'PhoneNumber' =>
                $phoneNumber,
            'ApiVersion' =>
                $options['apiVersion'],
            'FriendlyName' =>
                $options['friendlyName'],
            'SmsApplicationSid' =>
                $options['smsApplicationSid'],
            'SmsFallbackMethod' =>
                $options['smsFallbackMethod'],
            'SmsFallbackUrl' =>
                $options['smsFallbackUrl'],
            'SmsMethod' =>
                $options['smsMethod'],
            'SmsUrl' =>
                $options['smsUrl'],
            'StatusCallback' =>
                $options['statusCallback'],
            'StatusCallbackMethod' =>
                $options['statusCallbackMethod'],
            'VoiceApplicationSid' =>
                $options['voiceApplicationSid'],
            'VoiceCallerIdLookup' =>
                Serialize::booleanToString($options['voiceCallerIdLookup']),
            'VoiceFallbackMethod' =>
                $options['voiceFallbackMethod'],
            'VoiceFallbackUrl' =>
                $options['voiceFallbackUrl'],
            'VoiceMethod' =>
                $options['voiceMethod'],
            'VoiceUrl' =>
                $options['voiceUrl'],
            'IdentitySid' =>
                $options['identitySid'],
            'AddressSid' =>
                $options['addressSid'],
            'EmergencyStatus' =>
                $options['emergencyStatus'],
            'EmergencyAddressSid' =>
                $options['emergencyAddressSid'],
            'TrunkSid' =>
                $options['trunkSid'],
            'VoiceReceiveMode' =>
                $options['voiceReceiveMode'],
            'BundleSid' =>
                $options['bundleSid'],
        ]);

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json' ]);
        $payload = $this->version->create('POST', $this->uri, [], $data, $headers);

        return new MobileInstance(
            $this->version,
            $payload,
            $this->solution['accountSid']
        );
    }


    /**
     * Reads MobileInstance records from the API as a list.
     * Unlike stream(), this operation is eager and will load `limit` records into
     * memory before returning.
     *
     * @param array|Options $options Optional Arguments
     * @param int $limit Upper limit for the number of records to return. read()
     *                   guarantees to never return more than limit.  Default is no
     *                   limit
     * @param mixed $pageSize Number of records to fetch per request, when not set
     *                        will use the default value of 50 records.  If no
     *                        page_size is defined but a limit is defined, read()
     *                        will attempt to read the limit with the most
     *                        efficient page size, i.e. min(limit, 1000)
     * @return MobileInstance[] Array of results
     */
    public function read(array $options = [], ?int $limit = null, $pageSize = null): array
    {
        return \iterator_to_array($this->stream($options, $limit, $pageSize), false);
    }

    /**
     * Streams MobileInstance records from the API as a generator stream.
     * This operation lazily loads records as efficiently as possible until the
     * limit
     * is reached.
     * The results are returned as a generator, so this operation is memory
     * efficient.
     *
     * @param array|Options $options Optional Arguments
     * @param int $limit Upper limit for the number of records to return. stream()
     *                   guarantees to never return more than limit.  Default is no
     *                   limit
     * @param mixed $pageSize Number of records to fetch per request, when not set
     *                        will use the default value of 50 records.  If no
     *                        page_size is defined but a limit is defined, stream()
     *                        will attempt to read the limit with the most
     *                        efficient page size, i.e. min(limit, 1000)
     * @return Stream stream of results
     */
    public function stream(array $options = [], ?int $limit = null, $pageSize = null): Stream
    {
        $limits = $this->version->readLimits($limit, $pageSize);

        $page = $this->page($options, $limits['pageSize']);

        return $this->version->stream($page, $limits['limit'], $limits['pageLimit']);
    }

    /**
     * Retrieve a single page of MobileInstance records from the API.
     * Request is executed immediately
     *
     * @param mixed $pageSize Number of records to return, defaults to 50
     * @param string $pageToken PageToken provided by the API
     * @param mixed $pageNumber Page Number, this value is simply for client state
     * @return MobilePage Page of MobileInstance
     */
    public function page(
        array $options = [],
        $pageSize = Values::NONE,
        string $pageToken = Values::NONE,
        $pageNumber = Values::NONE
    ): MobilePage
    {
        $options = new Values($options);

        $params = Values::of([
            'Beta' =>
                Serialize::booleanToString($options['beta']),
            'FriendlyName' =>
                $options['friendlyName'],
            'PhoneNumber' =>
                $options['phoneNumber'],
            'Origin' =>
                $options['origin'],
            'PageToken' => $pageToken,
            'Page' => $pageNumber,
            'PageSize' => $pageSize,
        ]);

        $headers = Values::of(['Content-Type' => 'application/x-www-form-urlencoded', 'Accept' => 'application/json']);
        $response = $this->version->page('GET', $this->uri, $params, [], $headers);

        return new MobilePage($this->version, $response, $this->solution);
    }

    /**
     * Retrieve a specific page of MobileInstance records from the API.
     * Request is executed immediately
     *
     * @param string $targetUrl API-generated URL for the requested results page
     * @return MobilePage Page of MobileInstance
     */
    public function getPage(string $targetUrl): MobilePage
    {
        $response = $this->version->getDomain()->getClient()->request(
            'GET',
            $targetUrl
        );

        return new MobilePage($this->version, $response, $this->solution);
    }


    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Api.V2010.MobileList]';
    }
}
