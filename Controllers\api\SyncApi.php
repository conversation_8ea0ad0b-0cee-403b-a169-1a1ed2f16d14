<?php
namespace Butler\Controllers\api;

use Butler\Lib\Auth\AuthMiddleware;
use <PERSON>\Lib\Auth\EmployeeAuthMiddleware;
use Butler\Lib\Auth\TenantAuthMiddleware;
use Butler\Models\ButlerDB;
use Butler\Controllers\api\ApiBaseController;
use Butler\Models\Tenant;
use Butler\Models\DevelopmentbutlerSync;
use Butler\Models\Dispatch\Dispatch;
use Butler\Models\Dispatch\DispatchOrg;
use Butler\Models\Dispatch\DispatchUser;
use Butler\Models\Dispatch\DispatchMessage;
use Butler\Models\Dispatch\DispatchAppointment;
use Butler\Models\Dispatch\DispatchCoverage;
use Butler\Models\Dispatch\DispatchCoverageNote;
use Butler\Models\Dispatch\DispatchComment;
use Butler\Models\Dispatch\DispatchNcc;
use Butler\Models\Dispatch\DispatchItem;
use Butler\Models\Dispatch\DispatchCommentAttachment;
use Butler\Models\Dispatch\DispatchStatus;
use Butler\Models\Dispatch\DispatchCustomer;
use Butler\Models\Dispatch\DispatchCustomerProperty;
use Butler\Models\Dispatch\DispatchMeta;
use Butler\Models\Customer\Customer;
use Butler\Models\Customer\CustomerPhone;
use Butler\Models\Customer\CustomerProperty;
use Butler\Models\Customer\CustomerPropertyGeolocation;
use Butler\Lib\FrontDoor\FrontDoorInBound;
use Butler\Helpers\Func;
use PDO;

class SyncApi extends ApiBaseController {
    public function syncDevelopmentButler() {
        $tenantId = 2; // for dispatchbutler.com
        $lastSync = DevelopmentbutlerSync::query()
            ->where('tenant_id', $tenantId)
            ->orderBy('created_at', 'desc')
            ->first();

        $page = $lastSync ? $lastSync->page + 1 : 0;
        $url = 'https://developmentbutler.com/api/other/fetch_dispatch_data.php?page=' . $page;
        
        // Initialize curl
        $curl = curl_init();
        
        // Set curl options
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Accept: application/json'
            ]
        ]);
        
        // Execute the request
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $error = curl_error($curl);
        
        // Close curl
        curl_close($curl);
        
        // Handle errors
        if ($error) {
            return $this->error("cURL Error: " . $error);
        }

        $data = json_decode($response, true);

        if (isset($data['jobs']) && is_array($data['jobs']) && count($data['jobs'])) {

            foreach ($data['jobs'] as $row) {
                DevelopmentbutlerSync::create([
                    'tenant_id' => $tenantId,
                    'status' => 'received',
                    'data' => $row,
                    'page' => $page
                ]);

                $org_name = strtoupper($row['job_source']);
                $org = DispatchOrg::updateOrCreate([
                    'external_id' => $org_name
                ]);

                $dispatch = Dispatch::where('external_id', $row['id'])
                    ->where('tenant_id', $tenantId)
                    ->where('job_source', 'developmentbutler')
                    ->where('env', 'sandbox')
                    ->first();
                if ($dispatch) {
                    continue;
                }

                // Dispatch
                $dispatch = Dispatch::create([
                    'tenant_id' => $tenantId,
                    'external_id' => $row['id'],
                    'dispatchType' => $row['type'],
                    'trade' => "",
                    'priority' => "",
                    'date' => $row['created_at'],
                    'isAuthoRequired' => "false",
                    'source_org_id' => $org->id,
                    'job_source' => 'developmentbutler',
                    'env' => 'production'
                ]);

                DispatchCoverage::create([
                    'dispatch_id' => $dispatch->id,
                    'header' => $row['job_title'],
                    'content' => $row['job_description']
                ]);        
                
                DispatchItem::create([
                    'dispatch_id' => $dispatch->id,
                    'external_id' => "",
                    'description' => $row['job_service_type'],
                    'status' => 'Open'
                ]);

                // Customer
                $customer = Customer::create([
                    'external_id' => $row['customer_id'],
                    'name' => $row['customer_first_name'] . ' ' . $row['customer_last_name'],
                    'email' => $row['customer_email'],                    
                    'source' => 'developmentbutler',
                    'env' => 'production'
                ]);

                CustomerPhone::create([
                    'customer_id' => $customer->id,
                    'phone' => $row['customer_phone'],
                    'phone_type' => 'mobile',                    
                ]);

                $property = CustomerProperty::create([
                    'customer_id' => $customer->id,
                    'external_id' => "",
                    'city' => $row['home_city'],
                    'state' => $row['home_state'],
                    'streetDirection' => "",
                    'streetName' => $row['home_street_1'],
                    'streetNumber' => "",
                    'unitNumber' => "",
                    'unitType' => "",
                    'zip' => $row['home_postal_code'],
                    'zipFour' => ""
                ]);

                $geo = CustomerPropertyGeolocation::create([
                    'property_id' => $property->id,
                    'lat' => $row['cust_lat'],
                    'lng' => $row['cust_long'],
                    'zip' => $row['home_postal_code']
                ]);

                // Middle Table
                DispatchCustomer::create([
                    'dispatch_id' => $dispatch->id,
                    'customer_id' => $customer->id                   
                ]);

                DispatchCustomerProperty::create([
                    'dispatch_id' => $dispatch->id,
                    'customer_id' => $customer->id,
                    'property_id' => $property->id
                ]);

                // Status
                DispatchStatus::create([
                    'dispatch_id' => $dispatch->id,
                    'job_status' => DispatchStatus::STATUS_JOB_ASSIGNED                    
                ]);

            }
        }
        
        // Return the result
        $this->json($data);
    }
}
