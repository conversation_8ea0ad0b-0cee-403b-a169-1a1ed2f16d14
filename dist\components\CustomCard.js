/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/components/CustomCard/index.tsx":
/*!*********************************************!*\
  !*** ./src/components/CustomCard/index.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var antd_es_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! antd/es/card */ \"./node_modules/antd/es/card/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\n\n\n\n\nconst CustomCard = ({\n  title = 'Custom Card',\n  description = 'This is a custom card component',\n  avatar,\n  cover,\n  actions = true,\n  hoverable = true,\n  status = 'default',\n  tags = [],\n  onClick\n}) => {\n  const cardActions = actions ? [] : undefined;\n  const statusColors = {\n    success: 'green',\n    processing: 'blue',\n    error: 'red',\n    warning: 'orange',\n    default: 'default'\n  };\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd_es_card__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n    title: title,\n    cover: cover && cover,\n    actions: cardActions,\n    hoverable: hoverable,\n    onClick: onClick,\n    style: {\n      borderRadius: 8\n    },\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"h3\", {\n      children: \"Ok, test 002\"\n    })\n  });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CustomCard);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/CustomCard/index.tsx\n");

/***/ }),

/***/ "react":
/*!**************************************************************************************!*\
  !*** external {"root":"React","commonjs2":"react","commonjs":"react","amd":"react"} ***!
  \**************************************************************************************/
/***/ ((module) => {

module.exports = undefined;

/***/ }),

/***/ "react-dom":
/*!*****************************************************************************************************!*\
  !*** external {"root":"ReactDOM","commonjs2":"react-dom","commonjs":"react-dom","amd":"react-dom"} ***!
  \*****************************************************************************************************/
/***/ ((module) => {

module.exports = undefined;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			loaded: false,
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Flag the module as loaded
/******/ 		module.loaded = true;
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = __webpack_modules__;
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/chunk loaded */
/******/ 	(() => {
/******/ 		var deferred = [];
/******/ 		__webpack_require__.O = (result, chunkIds, fn, priority) => {
/******/ 			if(chunkIds) {
/******/ 				priority = priority || 0;
/******/ 				for(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];
/******/ 				deferred[i] = [chunkIds, fn, priority];
/******/ 				return;
/******/ 			}
/******/ 			var notFulfilled = Infinity;
/******/ 			for (var i = 0; i < deferred.length; i++) {
/******/ 				var [chunkIds, fn, priority] = deferred[i];
/******/ 				var fulfilled = true;
/******/ 				for (var j = 0; j < chunkIds.length; j++) {
/******/ 					if ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {
/******/ 						chunkIds.splice(j--, 1);
/******/ 					} else {
/******/ 						fulfilled = false;
/******/ 						if(priority < notFulfilled) notFulfilled = priority;
/******/ 					}
/******/ 				}
/******/ 				if(fulfilled) {
/******/ 					deferred.splice(i--, 1)
/******/ 					var r = fn();
/******/ 					if (r !== undefined) result = r;
/******/ 				}
/******/ 			}
/******/ 			return result;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/global */
/******/ 	(() => {
/******/ 		__webpack_require__.g = (function() {
/******/ 			if (typeof globalThis === 'object') return globalThis;
/******/ 			try {
/******/ 				return this || new Function('return this')();
/******/ 			} catch (e) {
/******/ 				if (typeof window === 'object') return window;
/******/ 			}
/******/ 		})();
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/harmony module decorator */
/******/ 	(() => {
/******/ 		__webpack_require__.hmd = (module) => {
/******/ 			module = Object.create(module);
/******/ 			if (!module.children) module.children = [];
/******/ 			Object.defineProperty(module, 'exports', {
/******/ 				enumerable: true,
/******/ 				set: () => {
/******/ 					throw new Error('ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: ' + module.id);
/******/ 				}
/******/ 			});
/******/ 			return module;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/jsonp chunk loading */
/******/ 	(() => {
/******/ 		// no baseURI
/******/ 		
/******/ 		// object to store loaded and loading chunks
/******/ 		// undefined = chunk not loaded, null = chunk preloaded/prefetched
/******/ 		// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded
/******/ 		var installedChunks = {
/******/ 			"CustomCard": 0
/******/ 		};
/******/ 		
/******/ 		// no chunk on demand loading
/******/ 		
/******/ 		// no prefetching
/******/ 		
/******/ 		// no preloaded
/******/ 		
/******/ 		// no HMR
/******/ 		
/******/ 		// no HMR manifest
/******/ 		
/******/ 		__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);
/******/ 		
/******/ 		// install a JSONP callback for chunk loading
/******/ 		var webpackJsonpCallback = (parentChunkLoadingFunction, data) => {
/******/ 			var [chunkIds, moreModules, runtime] = data;
/******/ 			// add "moreModules" to the modules object,
/******/ 			// then flag all "chunkIds" as loaded and fire callback
/******/ 			var moduleId, chunkId, i = 0;
/******/ 			if(chunkIds.some((id) => (installedChunks[id] !== 0))) {
/******/ 				for(moduleId in moreModules) {
/******/ 					if(__webpack_require__.o(moreModules, moduleId)) {
/******/ 						__webpack_require__.m[moduleId] = moreModules[moduleId];
/******/ 					}
/******/ 				}
/******/ 				if(runtime) var result = runtime(__webpack_require__);
/******/ 			}
/******/ 			if(parentChunkLoadingFunction) parentChunkLoadingFunction(data);
/******/ 			for(;i < chunkIds.length; i++) {
/******/ 				chunkId = chunkIds[i];
/******/ 				if(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {
/******/ 					installedChunks[chunkId][0]();
/******/ 				}
/******/ 				installedChunks[chunkId] = 0;
/******/ 			}
/******/ 			return __webpack_require__.O(result);
/******/ 		}
/******/ 		
/******/ 		var chunkLoadingGlobal = self["webpackChunkcustom_components_builder"] = self["webpackChunkcustom_components_builder"] || [];
/******/ 		chunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));
/******/ 		chunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module depends on other loaded chunks and execution need to be delayed
/******/ 	var __webpack_exports__ = __webpack_require__.O(undefined, ["vendors"], () => (__webpack_require__("./src/components/CustomCard/index.tsx")))
/******/ 	__webpack_exports__ = __webpack_require__.O(__webpack_exports__);
/******/ 	
/******/ })()
;