<?php

namespace Butler\Controllers\tenants;

use Butler\Controllers\BaseController;

class TenantBaseController extends BaseController
{
    public function __construct()
    {
        parent::__construct();

        $this->templatePath = VIEW_PATH . DS . 'tenants';
    }

    public function render($view, $data = [])
    {
        extract($data);

        // extract values
        $title = $this->title;
        $menu = $this->menu;
        $tenant_id = $this->tenant_id;

        ob_start();

        include VIEW_PATH . '/heads/head.php';
        include VIEW_PATH . '/heads/content_header.php';
        include VIEW_PATH . '/menu/tenant_nav.php';
        include $this->templatePath . DS . $view . (strpos($view, '.php') === false ? '.php' : '');
        include VIEW_PATH . '/footers/footer.php';

        ob_end_flush();
    }
}