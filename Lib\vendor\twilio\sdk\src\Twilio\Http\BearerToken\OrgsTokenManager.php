<?php
namespace Twi<PERSON>\Http\BearerToken;
use <PERSON><PERSON><PERSON>\CredentialProvider\NoAuthCredentialProvider;
use <PERSON><PERSON><PERSON>\Exceptions\TwilioException;
use <PERSON><PERSON><PERSON>\Rest\Client;
use <PERSON>wi<PERSON>\Rest\Iam\V1;
use <PERSON><PERSON><PERSON>\Rest\Iam\V1\TokenList;
use Twilio\Rest\IamBase;


/**
 * Class OrgsTokenManager
 * Token manager class for public OAuth
 * @property string $token The bearer token
 * @property string $tokenManager The manager for the bearer token
 */

class OrgsTokenManager extends TokenManager {
    private $options;

    public function __construct(array $options = []) {
        $this->options = $options;
    }

    public function getOptions(): array {
        return $this->options;
    }

    /**
     * Fetches the bearer token
     * @throws TwilioException
     */
    public function fetchToken(?Client $client = null): string {
        if ($client === null) {
            $client = new Client();
        }
        $noAuthCredentialProvider = new NoAuthCredentialProvider();
        $client->setCredentialProvider($noAuthCredentialProvider);
        $base = new IamBase($client);
        $v1 = new V1($base);
        $tokenList = new TokenList($v1);

        try {
            return $tokenList->create(
                $this->options['grantType'],
                $this->options['clientId'],
                $this->options
            )->accessToken;
        }

        catch (TwilioException $e) {
            throw new TwilioException($e->getMessage());
        }
    }
}
