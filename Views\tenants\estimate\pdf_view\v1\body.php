<?php

/** @var \Butler\Models\Dispatch\DispatchEstimate $estimate */

?>
    <div class="header">
        <div class="company-name"><?php echo $estimate->billing_company ?></div>
        <div class="estimate-title">ESTIMATE</div>
    </div>

    <div class="info-section">
        <table class="info-table">
            <tr>
                <td width="50%">
                    <table>
                        <tbody>
                        <tr>
                            <td><span class="label">Bill To:</span></td>
                            <td><?php echo $estimate->customer_name ?></td>
                        </tr>
                        <tr>
                            <td colspan="2"><?php echo $estimate->customer_full_address ?></td>
                        </tr>
                        <tr>
                            <td><span class="label">Phone:</span></td>
                            <td><?php echo $estimate->customer_phone ?></td>
                        </tr>
                        <tr>
                            <td><span class="label">Email:</span></td>
                            <td><?php echo $estimate->customer_email ?></td>
                        </tr>
                        </tbody>
                    </table>
                </td>
                <td width="50%" style="text-align: right;">
                    <table>
                        <tbody>
                        <tr>
                            <td><span class="label">Estimate #:</span></td>
                            <td><?php echo $estimate->id ?></td>
                        </tr>
                        <tr>
                            <td><span class="label">PO #:</span></td>
                            <td><?php echo $estimate->purchase_no ?></td>
                        </tr>
                        <tr>
                            <td><span class="label">Date:</span></td>
                            <td><?php echo $estimate->issue_date ?></td>
                        </tr>
                        <tr>
                            <td><span class="label">Payment Terms:</span></td>
                            <td><?php echo $estimate->payment_terms ?></td>
                        </tr>
                        </tbody>
                    </table>
                </td>
            </tr>
        </table>
    </div>

    <div class="line-items">
        <table>
            <thead>
            <tr>
                <th width="50%">Description</th>
                <th width="15%" style="text-align: center;">Qty</th>
                <th width="15%" style="text-align: right;">Price</th>
                <th width="20%" style="text-align: right;">Taxable</th>
                <th width="20%" style="text-align: right;">Amount</th>
            </tr>
            </thead>
            <tbody>
            <?php
            foreach ($estimate->items as $item) {
                ?>
                <tr>
                    <td style="text-align: left;"><?php echo $item->name ?></td>
                    <td style="text-align: center;"><?php echo number_format($item->qty) ?></td>
                    <td style="text-align: right;">$<?php echo number_format($item->price, 2) ?></td>
                    <td style="text-align: right;"><?php echo $item->tax_percent > 0 ? number_format($item->tax_percent, 2) . '%' : '' ?></td>
                    <td style="text-align: right;">$<?php echo number_format($item->price_total) ?></td>
                </tr>
                <?php
            }
            ?>
            </tbody>
        </table>
    </div>

    <div class="totals">
        <table>
            <tr>
                <td>Subtotal:</td>
                <td style="text-align: right;">$<?php echo number_format($estimate->subtotal, 2) ?></td>
            </tr>
            <?php
            if (floatval($estimate->tax_amount) > 0) {
                ?>
                <tr>
                    <td>Tax:</td>
                    <td style="text-align: right;">$<?php echo number_format($estimate->tax_amount, 2) ?></td>
                </tr>
            <?php } ?>
            <?php
            if (floatval($estimate->discount_amount) > 0) {
                ?>
                <tr>
                    <td>Discount:</td>
                    <td style="text-align: right;">$<?php echo number_format($estimate->discount_amount, 2) ?></td>
                </tr>
            <?php } ?>
            <tr class="total-row">
                <td>Total:</td>
                <td style="text-align: right;">$<?php echo number_format($estimate->grand_total, 2) ?></td>
            </tr>
        </table>
    </div>
<?php
$html = '';
if (!empty($estimate->notes)) {
    $html .= '
<div class="notes">
    <span class="label">Notes:</span><br>
    ' . nl2br(htmlspecialchars($estimate->notes)) . '
</div>';
}

$html .= '
<div style="margin-top: 40px; text-align: center; color: #666; font-size: 9px;">
    Thank you for your business!
</div>';

echo $html;