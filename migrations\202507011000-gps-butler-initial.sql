CREATE TABLE `gps_dispatches`
(
    `dispatch_id`      int(10) unsigned NOT NULL,
    `sms_sent`         int(11)          NOT NULL DEFAULT 1,
    `complete_address` text                      DEFAULT NULL comment 'Referenced address. Should be matched with customer address in dispatch',
    `cust_lat`         decimal(10, 7)            DEFAULT NULL,
    `cust_long`        decimal(10, 7)            DEFAULT NULL,
    `details`          longtext         NOT NULL comment 'details info in JSON.',
    `geofence_polygon` text                      DEFAULT NULL comment 'Geofence data array in JSON.',
    `completed`        tinyint(1)       NOT NULL DEFAULT 0,
    PRIMARY KEY (`dispatch_id`),
    CONSTRAINT `FK_gps_dispatches_dispatch_id` FOREIGN KEY (`dispatch_id`) REFERENCES `dispatches` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;

drop table if exists gps_dispatch_board;

CREATE TABLE `gps_dispatch_board`
(
    `id`                 int(11)          NOT NULL AUTO_INCREMENT,
    `dispatch_id`        int(10) unsigned NOT NULL,
    `device_id`          int(10) unsigned          DEFAULT NULL,
    `user_id`            int(10) unsigned          DEFAULT NULL comment 'User assigned to device_id at the moment. Reference only',
    `on_site`            datetime                  DEFAULT NULL,
    `off_site`           datetime                  DEFAULT NULL,
    `time`               int(11)                   DEFAULT NULL,
    `exclude_from_board` tinyint(1)       NOT NULL DEFAULT 0 COMMENT 'Set to 1 to exclude this job card from the board, 0 to display.',
    PRIMARY KEY (`id`),
    UNIQUE KEY `UQ_gps_dispatch_board_mix` (`dispatch_id`, `device_id`),
    CONSTRAINT `FK_gps_dispatch_board_dispatch_id` FOREIGN KEY (`dispatch_id`) REFERENCES `dispatches` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_gps_dispatch_board_device_id` FOREIGN KEY (`device_id`) REFERENCES `driver_devices` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_gps_dispatch_board_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `gps_dispatch_statuses`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT,
    `status_name` varchar(255) NOT NULL,
    `color_hex`   varchar(7)   NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `UQ_gps_dispatch_statuses_status_name` (`status_name`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;

CREATE TABLE `gps_dispatch_notes`
(
    `id`           int(11)          NOT NULL AUTO_INCREMENT,
    `dispatch_id`  int(10) unsigned NOT NULL,
    `device_id`    int(10) unsigned          DEFAULT NULL,
    `driver_name`  varchar(255)     NOT NULL comment 'Display name on GPS Platform',
    `note_date`    datetime         NOT NULL,
    `issue_note`   text                      DEFAULT NULL,
    `has_pics`     enum ('Yes','No')         DEFAULT 'No',
    `job_status`   varchar(255)              DEFAULT NULL,
    `created_at`   timestamp        NOT NULL DEFAULT current_timestamp(),
    `dispatch_url` varchar(2048)             DEFAULT NULL,
    `smart_url`    varchar(2048)             DEFAULT NULL,
    `acknowledged` varchar(50)      NOT NULL DEFAULT 'new',
    `submitted`    tinyint(1)       NOT NULL DEFAULT 0,
    PRIMARY KEY (`id`),
    CONSTRAINT `FK_gps_dispatch_notes_dispatch_id` FOREIGN KEY (`dispatch_id`) REFERENCES `dispatches` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT `FK_gps_dispatch_notes_device_id` FOREIGN KEY (`device_id`) REFERENCES `driver_devices` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;


alter table user_profile
    add column home_geofence_radius   int(11)      DEFAULT 100 comment 'Radius in miles',
    add column `home_geofence_center` varchar(255) DEFAULT NULL comment 'Geofence Center in JSON array'
;

CREATE TABLE `user_profile_home_history`
(
    `id`                   int(11)        NOT NULL AUTO_INCREMENT,
    `user_id`              int(10) unsigned default NULL,
    `device_id`            int(10) unsigned default NULL,
    `home_address`         varchar(255)   NOT NULL,
    `home_lat`             decimal(10, 7) NOT NULL,
    `home_lng`             decimal(10, 7) NOT NULL,
    `updated_at`           datetime         DEFAULT current_timestamp(),
    `home_geofence_radius` int(11)          DEFAULT 100,
    `home_geofence_center` varchar(255)     DEFAULT NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT `FK_user_profile_home_history_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT `FK_user_profile_home_history_device_id` FOREIGN KEY (`device_id`) REFERENCES `driver_devices` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci;




