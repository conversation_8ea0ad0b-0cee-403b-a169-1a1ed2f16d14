<?php
namespace Butler\Lib\Route;

use Butler\Config\SiteConfig;
use Butler\Views\Base\View;

class Router {
    private $routes = [];
    private $config = [];

    public function __construct() {
        $this->config = SiteConfig::getSiteConfig();
    }
    
    public function add($route, $controller, $action = null, $options = []) {        
        if ($action === null && is_callable($controller)) {            
            $this->routes[$route] = ['callback' => $controller];
            if (isset($options['auth'])) {
                $this->routes[$route]['auth'] = $options['auth'];
            }
        } else {
            $this->routes[$route] = ['controller' => $controller, 'action' => $action];           
            if (isset($options['auth'])) {
                $this->routes[$route]['auth'] = $options['auth'];
            }            
        }
    }
    
    public function dispatch($url) {        
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        if (array_key_exists($url, $this->routes)) {            
            $route = $this->routes[$url];
                        
            if (isset($route['auth'])) {
                if (is_array($route['auth'])) {
                    foreach ($route['auth'] as $key => $authClassName) {
                        $authClass = 'Butler\\Lib\\Auth\\' . $authClassName;
                        $authClass::authenticate();
                    }
                }
            }
            
            if (isset($route['callback'])) {                
                call_user_func($route['callback']);
            } else {
                $controller = $route['controller'];
                $action = $route['action'];
                if(!$action) $action = 'index';

                if (str_contains($controller, 'Butler\\')) {
                    $controllerPath = str_replace('Butler\\', '', $controller);
                    $controllerPath = str_replace('\\', DS, $controllerPath);
                    require_once APP_PATH . DS . $controllerPath . '.php';

                    $controllerClass = $controller;
                } else {
                    // Handle controllers in subfolders (e.g., "tenants/ZoneController")
                    $controllerPath = str_replace('\\', '/', $controller);
                    require_once __DIR__ . "/../../Controllers/{$controllerPath}.php";

                    // Create the full class name with namespace
                    if (strpos($controller, '\\') === false) {
                        // If there's a slash in the controller path, extract the class name
                        if (strpos($controller, '/') !== false) {
                            $parts = explode('/', $controller);
                            $className = end($parts);
                            $namespace = 'Butler\\Controllers\\' . str_replace('/', '\\', $controller);
                            $controllerClass = $namespace;
                        } else {
                            $controllerClass = 'Butler\\Controllers\\' . $controller;
                        }
                    }
                }
                
                $controllerInstance = new $controllerClass();
                $controllerInstance->$action();
            }
        } else {
            // Handle 404
            header("HTTP/1.0 404 Not Found");

            $title = "404 - Page Not Found";
            $base_url = $this->config['BASE_URL'];
            View::render([
                'heads/head.php',                 
                '404.php',
                'footers/footer.php'                
            ], 
            ['title' => $title,
            'base_url' => $base_url
        ]);
        }
    }
}




