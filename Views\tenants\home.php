<script>
  var userId = '<?php echo $user_id; ?>';
  var assignedNumber = '<?php echo $user_assigned_phone_number; ?>';
  // Global Variables & Config
  let currentJobsData = [];     // Unassigned jobs
  let scheduledJobs = [];     // Scheduled jobs
  let employeeMapById = {};
  let jobMarkers = [];
  let zipcodeMarkers = [];
  let stateMarkers = [];
  let directionsRenderers = [];
  let driverMarkers = [];
  let driverColors = {};
  let techVisibility = {};
  let currentlyViewingTechId = null;
  let currentlyViewingDate = null;

  // Track if we're currently viewing a tech's jobs
  let isViewingTechJobs = false;

  // Function to check if we're viewing tech jobs
  function checkIfViewingTechJobs() {
    return jobMarkers.length > 0 && zipcodeMarkers.length === 0 && stateMarkers.length === 0;
  }

  // Function to refresh tech jobs on map after assignment changes
  async function refreshTechJobsOnMap() {
    if (currentlyViewingTechId && currentlyViewingDate) {
      try {
        console.log(`Fetching jobs for tech ${currentlyViewingTechId} on ${currentlyViewingDate}`);

        // Call existing fetchJobs API to get jobs for this tech and date
        const techJobs = await fetchJobs(currentlyViewingTechId, null, null, currentlyViewingDate, '20,140,150');

        console.log(`API returned ${techJobs.length} jobs for tech ${currentlyViewingTechId} on ${currentlyViewingDate}`);

        // Only proceed if we have jobs
        if (techJobs && techJobs.length > 0) {
          showTechJobsOnMap(currentlyViewingTechId, currentlyViewingDate, techJobs);
        } else {
          console.warn(`No jobs found for tech ${currentlyViewingTechId} on ${currentlyViewingDate}`);
          // Clear the map if no jobs found
          clearMapMarkers();
        }
      } catch (err) {
        console.error(`Error fetching jobs for tech ${currentlyViewingTechId}:`, err);
        App.error("Failed to load technician's jobs");
        clearMapMarkers();
      }
    }
  }

  const BASE_API_URL = '<?php echo BASE_API_URL; ?>';
</script>
<script src="<?php echo BASE_URL; ?>assets/plugins/sortablejs-1.15.0/Sortable.min.js"></script>
<script src="<?php echo BASE_URL; ?>assets/plugins/jquery-ui-1.14.1/jquery-ui.min.js"></script>
<script src="<?php echo BASE_URL; ?>assets/js/google_map.js"></script>
<script src="<?php echo BASE_URL; ?>assets/js/tenant-twilio-device.js"></script>

<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/plugins/jquery-ui-1.14.1/jquery-ui.min.css">
<!--<link rel="stylesheet" href="<?php /*echo BASE_URL; */ ?>/assets/plugins/jquery-ui-1.14.1/jquery-ui.theme.min.css">-->
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/index.css">
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/popup_style.css">
<link rel="stylesheet" href="<?php echo BASE_URL; ?>/assets/css/tenants/home.css">
<script>

  // Google Maps
  var map, directionsService;
  window.initMap = function () {
    map = new google.maps.Map(document.getElementById('map'), {
      center: { lat: 39.5, lng: -98.35 },
      zoom: 4,
      mapId: window.google_map_key
    });
    directionsService = new google.maps.DirectionsService();
  };
</script>
<div class="header-filters">
  <label>Date:</label>
  <input type="date" id="datePicker"
    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5" />

  <label>Add Days (from tomorrow):</label>
  <select id="daysForward"
    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5">
    <option value="1">+1 day</option>
    <option value="2">+2 days</option>
    <option value="3">+3 days</option>
    <option value="4">+4 days</option>
    <option value="5">+5 days</option>
    <option value="6">+6 days</option>
    <option value="7">+7 days</option>
  </select>
  <button id="addDaysBtn" class="bg-cyan-500 px-4 py-1">Add Days</button>

  <label>Driver:</label>
  <select id="driverSelect"
    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5 max-w-30">
    <option value="all" selected>All</option>
  </select>
  <button id="loadDriverBtn" class="bg-cyan-500 px-4 py-1">Load Driver(s)</button>

  <label>Color:</label>
  <input type="color" id="driverColorInput" value="#FF0000"
    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 p1" />
  <button id="setDriverColorBtn" class="bg-cyan-500 px-4 py-1">Set Color</button>

  <!-- Overlay Open Calls -->
  <!-- <button id="overlayOpenCallsBtn" onclick="overlayOpenCallsByZone()" class="bg-cyan-500 px-4 py-1">
        Overlay Open Calls by Zone
    </button> -->

  <!-- Manage Status Lights (admin only) -->
  <button id="manageLightsBtn" style="display:none;" class="bg-cyan-500 px-4 py-1">
    Manage Status Lights
  </button>

  <!-- <button onclick="showInboundSmsModal('+17409570883')" class="bg-cyan-500 px-4 py-1">
        Test Inbound SMS Modal
    </button> -->

  <!-- Open Parking Garage Button -->
  <button id="openGarageBtn" class="bg-cyan-500 px-4 py-1">
    Open Parking Garage
  </button>
</div>

<!-- <div class="button-container">
  <button onclick="openNewWindow('https://developmentbutler.com/manage_status_categories.html', 'Job status indicators')">
    Job status indicators
  </button>
</div> -->

<h1></h1>

<div class="kanboard-container h-full" id="kanboard"></div>

<div class="map-popup" id="mapPopup">
  <div class="map-popup-header" id="mapPopupHeader">
    Map (Drag me!)
    <div class="map-controls flex items-center space-x-2 mb-2 mt-2">
      <select id="stateSelector"
        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-1.5">
        <option value="all">All States</option>
        <!-- States will be populated dynamically -->
      </select>
      <button id="refreshMapBtn" class="bg-cyan-500 hover:bg-cyan-600 text-white px-3 py-1 rounded text-sm">
        Refresh Map
      </button>
    </div>
  </div>
  <div class="map-popup-body">

    <div class="distance-label" id="distanceLabel"></div>
    <div id="map"></div>
    <div class="map-popup-resizer" id="mapResizer"></div>
  </div>
</div>

<!-- Modal for editing job details -->
<div class="modal-backdrop" id="editJobBackdrop">
  <div class="modal-content" id="editJobModal">
    <span class="modal-close" id="closeEditModal">&#10006;</span>
    <h3>Edit Job <span id="editJobId"></span></h3>

    <div class="modal-row">
      <label>Address:</label>
      <input type="text" id="editJobAddress" style="width:200px;" />
    </div>

    <div class="modal-row">
      <label>Notes:</label>
      <textarea id="editJobNotes" rows="3" cols="30"></textarea>
    </div>

    <div class="modal-row">
      <label>Tech:</label>
      <select id="editJobTech"
        class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5">
        <option value="">(Unassigned)</option>
      </select>
    </div>

    <div class="modal-row">
      <label>Tasks:</label>
      <textarea id="editJobTasks" rows="2" cols="30"></textarea>
    </div>

    <button id="saveEditJobBtn" class="bg-cyan-500 px-4 py-1">Save Changes</button>
  </div>
</div>

<!-- Modal for managing status lights -->
<div class="modal-backdrop" id="lightsConfigBackdrop">
  <div class="modal-content" id="lightsConfigModal">
    <span class="modal-close" id="closeLightsModal">&#10006;</span>
    <h3>Manage Status Lights</h3>

    <div class="modal-row">
      <label>Cat1 Name:</label>
      <input type="text" id="catName1" style="width:150px;" />
    </div>

    <div class="modal-row">
      <label>Cat1 Color:</label>
      <input type="color" id="catColor1" />
    </div>

    <div class="modal-row">
      <label>Cat1 Mappings:</label>
      <input type="text" id="catKeywords1" style="width:280px;" placeholder="e.g. urgent:red, normal:yellow" />
    </div>

    <div class="modal-row">
      <label>Flash Enabled:</label>
      <input type="checkbox" id="catFlash1" />
    </div>

    <div class="modal-row">
      <label>Flash Threshold (min):</label>
      <input type="number" id="catThreshold1" value="0" min="0" />
    </div>

    <hr />

    <div class="modal-row">
      <label>Cat2 Name:</label>
      <input type="text" id="catName2" style="width:150px;" />
    </div>

    <div class="modal-row">
      <label>Cat2 Color:</label>
      <input type="color" id="catColor2" />
    </div>

    <div class="modal-row">
      <label>Cat2 Mappings:</label>
      <input type="text" id="catKeywords2" style="width:280px;" placeholder="e.g. kit ready:green, delayed:orange" />
    </div>

    <div class="modal-row">
      <label>Flash Enabled:</label>
      <input type="checkbox" id="catFlash2" />
    </div>

    <div class="modal-row">
      <label>Flash Threshold (min):</label>
      <input type="number" id="catThreshold2" value="0" min="0" />
    </div>

    <hr />

    <div class="modal-row">
      <label>Cat3 Name:</label>
      <input type="text" id="catName3" style="width:150px;" />
    </div>

    <div class="modal-row">
      <label>Cat3 Color:</label>
      <input type="color" id="catColor3" />
    </div>

    <div class="modal-row">
      <label>Cat3 Mappings:</label>
      <input type="text" id="catKeywords3" style="width:280px;" placeholder="e.g. critical:darkred, info:blue" />
    </div>

    <div class="modal-row">
      <label>Flash Enabled:</label>
      <input type="checkbox" id="catFlash3" />
    </div>

    <div class="modal-row">
      <label>Flash Threshold (min):</label>
      <input type="number" id="catThreshold3" value="0" min="0" />
    </div>

    <button id="saveCategoriesBtn" class="bg-cyan-500 px-4 py-1">Save Categories</button>
  </div>
</div>

<script>
  let futureDays = [];
  window.employeeMapById = {};
  let openCallMarkers = [];
  let statusCategories = []; // from get_categories.php
  let mappings = {}; // from get_category_mappings.php    
  let stateColors = {};

  $(document).ready(function () {
    (async function () {
      try {
        var employees = await fetchEmployees();
        $.each(employees, function (i, emp) {
          employeeMapById[emp.id] = emp;
        });
        loadGoogleMaps();
        await setInitialize();
      } catch (err) {
        console.error("Error loading employees =>", err);
      }
    })();

    var now = new Date();
    var yyyy = now.getFullYear();
    var mm = String(now.getMonth() + 1).padStart(2, "0");
    var dd = String(now.getDate()).padStart(2, "0");
    $("#datePicker").val(yyyy + "-" + mm + "-" + dd);

    (async function () {
      await loadSentimentData();
    })();

    $("#stateSelector").on("change", function () {
      refreshMapWithJobs();
      updateOpenCallsColumn();
    });

    $("#refreshMapBtn").on("click", function () {
      refreshMapWithJobs();
      updateOpenCallsColumn();
    });

    $('#openGarageBtn').on('click', function () {      
      showGarageOverlay();
    });

    TwilioCallModule.init();

  });

  // Helper Functions
  function getAppointment(job) {
    // return (job.appointment_set || "").trim();
    return job.dispatch_appointment?.appointment_date || '';
  }

  function isOpenCall(job) {
    const appointment_status = isNaN(parseInt(job.dispatch_status?.appointment_status)) ? false : true;
    return job.dispatch_user == null && appointment_status == false && (job.dispatch_status?.job_status == 250 || job.dispatch_status?.job_status == 260);
  }

  function groupByDateTech(jobs) {
    const groups = {};

    jobs.forEach(job => {
      const appt = getAppointment(job);
      if (!appt || appt === '') return;

      const tech = job.assigned_techs || 'Unassigned';
      const key = appt + "_" + tech;

      if (!groups[key]) groups[key] = [];
      groups[key].push(job);
    });

    return groups;
  }

  function groupByTech(arr) {
    const out = {};

    arr.forEach(j => {
      const key = j.assigned_techs || 'Unassigned';
      if (!out[key]) out[key] = [];
      out[key].push(j);
    });

    return out;
  }

  function shouldFlash(job, cat) {
    if (!cat.flash_enabled) return false;

    var apptStr = getAppointment(job);
    var apptDate = new Date(apptStr);

    if (isNaN(apptDate.getTime())) return false;

    var now = new Date();
    var today = now.toISOString().slice(0, 10);
    var jobDate = apptStr.slice(0, 10);

    if (today === jobDate) return false;

    var thresholdMs = (cat.flash_threshold || 0) * 60 * 1000;
    return (now.getTime() > apptDate.getTime() + thresholdMs);
  }

  // ----------------------------------------------------------------------------
  // Load mappings from API
  async function loadMappings() {
    try {
      const res = await fetch(BASE_API_URL + 'other/get_category_mappings');
      const data = await res.json();

      mappings = {};
      if (data?.success) {
        data.data.forEach(function (mapping) {
          if (!mappings[mapping.category_id]) {
            mappings[mapping.category_id] = [];
          }
          mappings[mapping.category_id].push(mapping);
        });
      }
    } catch (err) {
      console.error("Failed to load mappings:", err);
    }
  }


  // Modal functions for status lights configuration
  function openLightsModal() {
    document.getElementById('catName1').value = statusCategories[0].name || '';
    document.getElementById('catColor1').value = statusCategories[0].color || '#ff0000';
    document.getElementById('catKeywords1').value = statusCategories[0].keywords || '';
    document.getElementById('catFlash1').checked = !!statusCategories[0].flash_enabled;
    document.getElementById('catThreshold1').value = statusCategories[0].flash_threshold || 0;
    document.getElementById('catName2').value = statusCategories[1].name || '';
    document.getElementById('catColor2').value = statusCategories[1].color || '#00ff00';
    document.getElementById('catKeywords2').value = statusCategories[1].keywords || '';
    document.getElementById('catFlash2').checked = !!statusCategories[1].flash_enabled;
    document.getElementById('catThreshold2').value = statusCategories[1].flash_threshold || 0;
    document.getElementById('catName3').value = statusCategories[2].name || '';
    document.getElementById('catColor3').value = statusCategories[2].color || '#0000ff';
    document.getElementById('catKeywords3').value = statusCategories[2].keywords || '';
    document.getElementById('catFlash3').checked = !!statusCategories[2].flash_enabled;
    document.getElementById('catThreshold3').value = statusCategories[2].flash_threshold || 0;
    document.getElementById('lightsConfigBackdrop').classList.add('active');
  }

  function closeLightsModal() {
    document.getElementById('lightsConfigBackdrop').classList.remove('active');
  }

  async function saveCategoriesConfig() {

    const newCats = [
      {
        name: document.getElementById('catName1').value.trim(),
        color: document.getElementById('catColor1').value,
        keywords: document.getElementById('catKeywords1').value.trim(),
        flash_enabled: document.getElementById('catFlash1').checked ? 1 : 0,
        flash_threshold: parseInt(document.getElementById('catThreshold1').value, 10) || 0
      },
      {
        name: document.getElementById('catName2').value.trim(),
        color: document.getElementById('catColor2').value,
        keywords: document.getElementById('catKeywords2').value.trim(),
        flash_enabled: document.getElementById('catFlash2').checked ? 1 : 0,
        flash_threshold: parseInt(document.getElementById('catThreshold2').value, 10) || 0
      },
      {
        name: document.getElementById('catName3').value.trim(),
        color: document.getElementById('catColor3').value,
        keywords: document.getElementById('catKeywords3').value.trim(),
        flash_enabled: document.getElementById('catFlash3').checked ? 1 : 0,
        flash_threshold: parseInt(document.getElementById('catThreshold3').value, 10) || 0
      }
    ];

    try {
      const res = await fetch(BASE_API_URL + 'other/save_categories.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newCats)
      });

      const result = await res.json();
      if (result.success) {
        statusCategories = newCats;
        await loadKanboard();
        closeLightsModal();
      } else {
        alert('Failed to save categories');
      }
    } catch (err) {
      console.error('Error saving categories:', err);
      alert('Error saving categories');
    }
  }


  // ----------------------------------------------------------------------------

  // Map & Directions Functions      
  const ROUTE_COLORS = ['#FF0000', '#0000FF', '#008000', '#FFA500', '#800080', '#008B8B', '#DC143C', '#FF1493', '#8A2BE2', '#228B22'];

  function getCustGeoByJob(job) {
    const geo = job?.dispatch_customers?.[0]?.customer?.customer_properties?.[0]?.customer_property_geolocation;
    if (geo) {
      return [parseFloat(geo.lat), parseFloat(geo.lng)];
    } else {
      return [0, 0, true]
    }
  }

  function requestOptimizedRoute(jobs, directionsRenderer, homeLat, homeLong) {
    console.log('OK[requestOptimizedRoute]', 'TotalJobs', jobs.length, jobs);
    if (jobs.length < 2) return;

    const waypoints = jobs.map(j => {
      const geo = getCustGeoByJob(j);
      if (geo[2]) return;

      return {
        location: new google.maps.LatLng(...geo),
        stopover: true
      };
    });

    console.log('waypoints', waypoints);

    let origin, destination;
    if (homeLat && homeLong) {
      origin = new google.maps.LatLng(homeLat, homeLong);
      destination = new google.maps.LatLng(homeLat, homeLong);
    } else {
      const firstJob = jobs[0];
      const employeeGeoInFirstJob = [
        parseFloat(employeeMapById[firstJob.assigned_techs]?.user_profile?.home_lat ?? 0)
        , parseFloat(employeeMapById[firstJob.assigned_techs]?.user_profile?.home_lng ?? 0)
      ];

      if (firstJob.assigned_techs && employeeMapById[firstJob.assigned_techs] && employeeGeoInFirstJob[0] != 0 && employeeGeoInFirstJob[1] != 0) {
        const latf = employeeGeoInFirstJob[0];
        const lngf = employeeGeoInFirstJob[1];

        if (latf && lngf) {
          origin = new google.maps.LatLng(latf, lngf);
          destination = new google.maps.LatLng(latf, lngf);
        }
      }

      if (!origin) {
        jobs.sort(function (a, b) {
          return (a.stop_sequence || 0) - (b.stop_sequence || 0);
        });

        const custGeo1 = getCustGeoByJob(firstJob);
        const custGeoN = getCustGeoByJob(jobs[jobs.length - 1]);
        origin = new google.maps.LatLng(...custGeo1);
        destination = new google.maps.LatLng(...custGeoN);
      }
    }

    const req = {
      origin: origin,
      destination: destination,
      waypoints: waypoints,
      travelMode: google.maps.TravelMode.DRIVING,
      optimizeWaypoints: true
    };

    /* console.log('Home param', homeLat, homeLong);
    console.log('Home', origin.lat(), origin.lng());
    console.log('Destination', destination.lat(), destination.lng());
    console.log('1', waypoints[0].location.lat(), waypoints[0].location.lng());
    console.log('1', waypoints[1].location.lat(), waypoints[1].location.lng()); */


    directionsService.route(req, function (res, status) {
      if (status === 'OK') {
        directionsRenderer.setDirections(res);
        const wOrder = res.routes[0].waypoint_order || [];

        // Disable real-time updates here.
        // const newArr = reorderJobsByWaypointOrder(jobs, wOrder);
        // updateTechJobsStopSequence(newArr);
        var totalMeters = 0;

        res.routes[0].legs.forEach(function (leg) {
          totalMeters += leg.distance.value;
        });

        var totalMiles = totalMeters / 1609.34;
        document.getElementById('distanceLabel').textContent = "Route Distance: " + totalMiles.toFixed(2) + " mi";
      } else {
        directionsRenderer.setDirections({ routes: [] });
        console.error("Directions =>", status);
      }
    });
  }

  function reorderJobsByWaypointOrder(jobs, wOrder) {
    if (jobs.length < 1) return jobs;
    const newArr = new Array(jobs.length);
    wOrder.forEach(function (oldIndex, newIndex) {
      newArr[newIndex] = jobs[oldIndex];
    });
    newArr.forEach(function (job, i) {
      job.stop_sequence = i + 1;
    });

    return newArr;
  }

  async function updateTechJobsStopSequence(jobsArr) {
    for (var i = 0; i < jobsArr.length; i++) {
      try {
        const res = await updateJob(jobsArr[i].id, { stop_sequence: jobsArr[i].stop_sequence });
        console.log("Updated job", jobsArr[i].id, "to sequence", jobsArr[i].stop_sequence, res);
      } catch (err) {
        console.error("Error updating sequence for job", jobsArr[i].id, err);
      }
    }
  }


  // Overlay: Show open calls on map
  function overlayOpenCallsByZone() {
    // Clear existing markers
    $.each(openCallMarkers, function (i, marker) {
      marker.setMap(null);
    });
    openCallMarkers = [];
    const openCalls = currentJobsData;

    $.each(openCalls, function (i, job) {
      let markerColor = "#FFFF00";
      const cust = job.dispatch_customers?.[0]?.customer || '';
      console.log(cust?.customer_properties?.[0]);
      if (cust?.customer_properties?.[0]?.zip) {
        markerColor = getColorFromZipcode(cust?.customer_properties?.[0]?.zip);
      }

      const $contentDiv = $("<div>")
        .addClass("bg-[" + markerColor + "] border border-black p-[1px_3px] text-[9px] max-w-[40px] whitespace-nowrap overflow-hidden truncate text-black cursor-pointer")
        .attr('title', Est.getFullAddress(cust?.customer_properties?.[0]) || cust.name)
        .text(Est.getFullAddress(cust?.customer_properties?.[0]) || cust.name || "Open Call");


      $contentDiv.on('click', function () {
        App.info(Est.getFullAddress(cust?.customer_properties?.[0]) || cust.name || "No Address Provided");
      });

      const custGeo = getCustGeoByJob(job);
      const lat = parseFloat(custGeo[0]);
      const lng = parseFloat(custGeo[1]);

      if (isNaN(lat) || isNaN(lng)) {
        console.warn("Invalid coordinates for open call job", job.id);
        return;
      }

      const mk = new google.maps.marker.AdvancedMarkerElement({
        map: map,
        position: new google.maps.LatLng(lat, lng),
        content: $contentDiv[0],
        title: job.billing_address || cust.name || "Open Call"
      });

      openCallMarkers.push(mk);
    });
  }

  // API Call Functions
  async function fetchJobs(empId = "all", startDate = null, endDate = null, date = null, jobStatus = null) {
    let url = BASE_API_URL + "jobs/search_dispatches";

    const jobs = await App.ajax(
      url,
      {
        employeeId: empId,
        startDate,
        endDate,
        date,
        jobStatus,
        with: 'dispatchItems.symptoms,customer,customer.customerPhones,addresses,addresses.customerGeolocation',
      },
      (res) => {
        return res;
      },
      (err) => {
        console.error("Failed to fetch jobs =>", err);
      }
    );

    jobs.forEach((x) => {
      x.assigned_techs = x.dispatch_user?.user_id;
      x.assigned_tech = x.dispatch_user?.user_id;
    })

    console.log('[fetchJob]', 'empId', empId, jobs);
    return jobs;
  }

  async function updateJob(jobId, data, type = 'sequence') {
    const url = BASE_API_URL + "jobs/update";
    const resp = await fetch(url, {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        job_id: jobId,
        ...data,
        type
      })
    });
    if (!resp.ok) {
      console.error("Failed to update job =>", resp.statusText);
      return null;
    }
    return resp.json();
  }

  async function assignJob(jobId, data) {
    const url = BASE_API_URL + "jobs/assign";
    const res = await App.ajaxPostOk(
      url,
      { job_id: jobId, ...data },
      undefined
    );

    if (res?.success) {
      App.success('Job assigned successfully!');

      // If we're viewing a tech's jobs and this job was assigned to or from that tech,
      // refresh the map view
      if (isViewingTechJobs &&
        (data.assigned_techs === currentlyViewingTechId ||
          currentJobsData.find(j => j.id == jobId)?.assigned_techs === currentlyViewingTechId)) {
        refreshTechJobsOnMap();
      }
    } else {
      App.error(res?.message);
    }

    return res;
  }

  async function acceptDeclineJob(jobId, data) {
    const url = BASE_API_URL + "jobs/accept_decline";
    const res = await App.ajaxPostOk(url, { job_id: jobId, ...data }, (res) => {
      return res;
    });

    if (res?.success) {
      await loadKanboard();
    }

    return res
  }

  async function fetchDriverPositions(userId = null) {
    let url = baseApiUrl + "/tenants/driver_positions";

    if (userId) {
      url += "?user_id=" + encodeURIComponent(userId);
    }
    const r = await fetch(url);
    if (!r.ok) {
      App.info(`Failed to fetch driver positions: ${r.statusText}`);
      return [];
    }
    return r.json();
  }

  // Kanboard Logic
  const datePicker = $("#datePicker");
  const daysForwardSelect = $("#daysForward");
  const addDaysBtn = $("#addDaysBtn");
  const driverSelect = $("#driverSelect");
  const loadDriverBtn = $("#loadDriverBtn");
  const driverColorInput = $("#driverColorInput");
  const setDriverColorBtn = $("#setDriverColorBtn");
  datePicker.on("change", async function () {
    futureDays = [];
    await loadKanboard();
  });
  addDaysBtn.on("click", addFutureDays);
  loadDriverBtn.on("click", loadDriversOnMap);
  setDriverColorBtn.on("click", setDriverColor);

  async function loadKanboard() {
    const mainDateVal = datePicker.val() || null;

    try {
      const jobsData = await fetchJobs("unassigned", null, null, null, '250,260,20,140,150');
      const exitJobsData = await fetchExitGarageJobs();
      currentJobsData = jobsData;

      // Check if we're currently viewing a tech's jobs on the map
      isViewingTechJobs = checkIfViewingTechJobs();

      // Only update map state selector and view if not viewing tech jobs
      if (!isViewingTechJobs && map) {
        populateStateSelector();
        fetchJobsByState();
      }

      renderKanboard(jobsData, exitJobsData);

      // Only update routes if not viewing tech jobs
      if (!isViewingTechJobs && map) {
        DriverOnMap.updateMapWithRoutes();
      } else if (isViewingTechJobs && map) {
        // If viewing tech jobs, refresh the current tech view
        refreshTechJobsOnMap();
      }
    } catch (err) {
      console.error("Error loadKanboard =>", err);
    }
  }


  async function fetchExitGarageJobs() {
    const url = BASE_API_URL + "jobs/get_exit_garage";
    try {
      const r = await fetch(url);
      if (!r.ok) {
        console.error("Failed to fetch exit garage jobs =>", r.statusText);
        return [];
      }
      return await r.json();
    } catch (err) {
      console.error("Error fetching exit garage jobs:", err);
      return [];
    }
  }

  async function fetchParkedGarageJobs() {
    const url = BASE_API_URL + "jobs/get_parked_garage";
    try {
      return await App.ajax(
        url,
        {},
        (res) => {
          return res;
        },
        (err) => {
          console.error("Failed to fetch exit garage jobs =>", err);
          return [];
        }
      );
    } catch (err) {
      console.error("Error fetching exit garage jobs:", err);
      return [];
    }
  }

  function addFutureDays() {
    const val = parseInt(daysForwardSelect.val(), 10);
    futureDays = [];
    for (var i = 1; i <= val; i++) {
      if (!futureDays.includes(i)) {
        futureDays.push(i);
      }
    }
    loadKanboard();
  }

  function getTomorrow() {

    var d = new Date();

    d.setHours(0, 0, 0, 0);

    d.setDate(d.getDate() + 1);

    return d;

  }


  function formatDate(d) {

    var y = d.getFullYear();

    var m = String(d.getMonth() + 1).padStart(2, "0");

    var dd = String(d.getDate()).padStart(2, "0");

    return y + "-" + m + "-" + dd;

  }


  function getDayOfWeek(d) {

    var arr = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

    return arr[d.getDay()];

  }


  async function renderKanboard(jobs, exitJobs = []) {
    let $board = $("#kanboard");
    $board.empty();

    // 🚗 Parking Exit Column (from exitJobs parameter)
    let $parkingCol = $(createParkingExitColumn("Parking Exit", exitJobs));
    $board.append($parkingCol);

    // Open Calls Column
    let $oc = $(createOpenCallsColumn("Open Calls (Grouped by Zone)", jobs));
    $board.append($oc);

    // Today's Scheduled Column
    let mainVal = datePicker.val() || null;
    if (mainVal) {
      let $mainCol = $(await createScheduledDateColumn("Scheduled: " + mainVal, mainVal));
      $board.append($mainCol);
    }

    // Future Scheduled Columns
    futureDays.sort((a, b) => a - b);
    let tomorrow = getTomorrow();
    // Process future days columns
    for (let i = 0; i < futureDays.length; i++) {
      let offset = futureDays[i];
      let dd = new Date(tomorrow);
      dd.setDate(dd.getDate() + (offset - 1));
      let dayName = getDayOfWeek(dd);
      let dateStr = formatDate(dd);
      let colTitle = "(" + dayName + ") " + dateStr;
      let $col = await createScheduledDateColumn(colTitle, dateStr);
      $board.append($col);
    }
  }


  function groupByStateAndZone(jobs) {
    return jobs.reduce((acc, job) => {
      // Split the billing address by commas
      // Format assumed: "Street, City, State, ZIP"
      let state = "Unknown";
      let zip = "Unknown";
      let parts = null;
      if (job.dispatch_customers.length) {
        parts = job.dispatch_customers[0].customer.customer_properties[0];
      }
      state = parts?.state ?? "Unknown";
      zip = parts?.zip ?? "Unknown";
      const groupKey = `${state} Zone ${zip}`;

      if (!acc[groupKey]) {
        acc[groupKey] = [];
      }
      acc[groupKey].push(job);

      return acc;
    }, {});
  }

  function createParkingExitColumn(title, jobsArr) {
    if (jobsArr && jobsArr.length) {
      // Create the main column wrapper with jQuery
      const $col = $("<div>")
        .addClass("kan-col overflow-y-auto")
        .attr("data-col-type", "parkingExit");

      // Column heading
      const $h3 = $("<h3>").text(title);
      $col.append($h3);

      // Container for job cards
      const $container = $("<div>").addClass("calls-container");

      // Sort jobs by stop_sequence
      jobsArr.sort((a, b) => (a.id || 0) - (b.id || 0));

      // Create job cards
      $.each(jobsArr, function (i, job) {
        $container.append(createJobCard(job));
      });

      $col.append($container);

      // Enable drag-and-drop within and between columns
      new Sortable($container[0], {
        group: "jobs",
        animation: 150,
        onEnd: handleDragEnd
      });

      return $col[0]; // Return the DOM element
    }

  }

  async function resequenceContainer(container, type = null) {
    let updates = [];
    if (type == 'techgroup') {
      const $techGroups = $(container).closest(".tech-group");
      if ($techGroups.length) {
        $techGroups.each(function () {
          const techId = $(this).data("tech-val");
          const appointmentDate = $(this).data("date-value");
          const $cards = $(this).find(".call-card");
          updates = [];
          let seq = 1;
          $cards.each(function () {
            const jobId = parseInt($(this).data("job-id"));
            if (!isNaN(jobId) && jobId != null) {
              updates.push({
                job_id: jobId,
                assigned_tech: techId,
                appointment_date: appointmentDate,
                stop_sequence: seq
              });
              seq++;
            }
          });
        });
      }
    } else {
      console.info("Container Type not given");
    }

    await Promise.all(updates.map(update =>
      updateJob(
        update.job_id,
        {
          stop_sequence: update.stop_sequence,
          assigned_tech: update.assigned_tech,
          appointment_date: update.appointment_date,
        },
        'sequence'
      )
    ));

    await loadKanboard();

    // After resequencing and reloading the kanboard, refresh the tech jobs on map
    // if we're currently viewing a tech's jobs
    if (isViewingTechJobs && currentlyViewingTechId && currentlyViewingDate) {
      refreshTechJobsOnMap();
    }
  }


  function createOpenCallsColumn(title, jobsArr) {
    // Create the main column wrapper
    var $col = $("<div>").addClass("kan-col overflow-y-auto").attr("data-col-type", "openCalls");

    // Column heading
    var $h3 = $("<h3>").text(title);
    $col.append($h3);

    // === GROUP BY STATE + ZONE HERE ===
    const grouped = groupByStateAndZone(jobsArr);

    // Container to hold all groups
    var $container = $("<div>").addClass("calls-container");

    // Iterate over each "state zone" key
    $.each(grouped, function (groupKey, groupJobs) {
      var $zoneDiv = $("<div>").addClass("zone-group");
      var $zoneHeader = $("<div>").css({
        "font-weight": "bold",
        "margin-bottom": "5px"
      });

      // e.g. "OH Zone 2 (4 calls)"
      $zoneHeader.text(groupKey + " (" + groupJobs.length + " calls)");
      $zoneDiv.append($zoneHeader);

      var $zoneCalls = $("<div>").addClass("zone-calls");

      // Sort by stop_sequence if you want
      groupJobs.sort(function (a, b) {
        return (a.id || 0) - (b.id || 0);
      });

      // Create each job card
      $.each(groupJobs, function (i, job) {
        $zoneCalls.append(createJobCard(job));
      });

      $zoneDiv.append($zoneCalls);
      $container.append($zoneDiv);

      // (Optional) Enable drag-and-drop reordering
      new Sortable($zoneCalls[0], {
        group: "jobs",
        animation: 150,
        onAdd: handleDragEnd
      });
    });

    $col.append($container);

    return $col[0];
  }


  async function createScheduledDateColumn(title, dateStr) {
    // Search Scheduled Dispatches
    let jobsArr = await fetchJobs("all", null, null, dateStr, '20,140,150');
    scheduledJobs = jobsArr;

    // Create the main column wrapper with jQuery
    const $col = $("<div>")
      .addClass("kan-col overflow-y-auto bg-white rounded-lg shadow-md")
      .attr("data-col-type", "dateColumn")
      .attr("data-date-value", dateStr);

    // Column heading
    const $h3 = $("<h3>").text(title)
      .addClass("text-lg font-semibold p-3 bg-gray-100 rounded-t-lg border-b");
    $col.append($h3);

    // "Load Technician" form
    const $techForm = $("<div>")
      .addClass("p-3 border-b");

    const $lbl = $("<span>")
      .text("Load Technician: ")
      .addClass("text-sm font-medium text-gray-700 mr-2");
    $techForm.append($lbl);

    // Dropdown of employees
    const $techSelect = $("<select>")
      .addClass("bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2 mr-2");

    // Default blank option
    $techSelect.append($("<option>")
      .val("")
      .text("(Select Tech)"));

    // Add all employees from the map
    $.each(employeeMapById, function (id, e) {
      $techSelect.append($("<option>").val(id).text(e.user_name));
    });

    $techForm.append($techSelect);

    // Define callsContainer before adding the event listener
    const $callsContainer = $("<div>")
      .addClass("calls-container p-3 space-y-3 overflow-y-auto");

    // Add button
    const $addBtn = $("<button>")
      .text("Add Tech")
      .addClass("bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm transition-colors")
      .on("click", function () {
        const chosen = $techSelect.val();
        if (!chosen) {
          App.error("Please select a technician.");
          return;
        }

        const $groupDiv = $(createTechGroup(dateStr, chosen, []));

        // Insert the new tech group at the top of the container
        if ($callsContainer.children().length > 0) {
          $callsContainer.prepend($groupDiv);
        } else {
          $callsContainer.append($groupDiv);
        }
      });

    $techForm.append($addBtn);
    $col.append($techForm);
    $col.append($callsContainer);

    // Group jobs by assigned_techs
    const grouped = {};
    $.each(jobsArr, function (i, job) {
      const key = job?.dispatch_user?.user_id || "";
      if (!grouped[key]) grouped[key] = [];
      grouped[key].push(job);
    });

    $.each(grouped, function (techVal, techJobs) {
      const $sub = $(createTechGroup(dateStr, techVal, techJobs))
        .attr("draggable", "true");
      $callsContainer.append($sub);
    });

    // Make the container sortable
    new Sortable($callsContainer[0], {
      group: "groups",
      animation: 150,
      onEnd: handleDragTech
    });

    return $col[0];
  }

  function createTechGroup(dateStr, techVal, jobsArr) {
    const $groupDiv = $("<div>")
      .addClass("tech-group")
      .attr({
        "data-col-type": "techGroup",
        "data-date-value": dateStr,
        "data-tech-val": techVal,
        "draggable": "true"
      });

    const $header = $("<div>").addClass("tech-group-header flex justify-start items-center");
    const $cbox = $("<input>").attr("type", "checkbox");
    const key = dateStr + "_" + techVal;

    if (typeof techVisibility[key] === "undefined") {
      techVisibility[key] = false;
    }
    $cbox.prop("checked", techVisibility[key]);
    $cbox.on("change", function () {
      techVisibility[key] = $cbox.prop("checked");
      DriverOnMap.updateMapWithRoutes();
    });

    $header.append($cbox);
    let displayName = "Unassigned";

    if (techVal && employeeMapById[techVal]) {
      displayName = employeeMapById[techVal].user_name;
    } else if (techVal) {
      displayName = techVal;
    }

    const $lbl = $(`<label class="ml-2">`).text(" " + displayName);
    $header.append($lbl);

    // Add "Show on Map" button
    const $showOnMapBtn = $("<button>")
      .addClass("ml-auto bg-cyan-500 hover:bg-blue-600 text-white text-xs px-2 py-1 rounded")
      .html(`
        <span>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
            <path stroke-linecap="round" stroke-linejoin="round" d="M9 6.75V15m6-6v8.25m.503 3.498 4.875-2.437c.381-.19.622-.58.622-1.006V4.82c0-.836-.88-1.38-1.628-1.006l-3.869 1.934c-.317.159-.69.159-1.006 0L9.503 3.252a1.125 1.125 0 0 0-1.006 0L3.622 5.689C3.24 5.88 3 6.27 3 6.695V19.18c0 .836.88 1.38 1.628 1.006l3.869-1.934c.317-.159.69-.159 1.006 0l4.994 2.497c.317.158.69.158 1.006 0Z" />
          </svg>
        </span>
      `)
      .on("click", function (e) {
        e.preventDefault();
        e.stopPropagation();

        // Show this tech's jobs on map
        showTechJobsOnMap(techVal, dateStr, jobsArr);
      });

    $header.append($showOnMapBtn);
    $groupDiv.append($header);
    const $callsContainer = $("<div>").addClass("calls-container");
    $groupDiv.append($callsContainer);
    jobsArr.sort(function (a, b) {
      return (a.dispatch_appointment.stop_sequence || 0) - (b.dispatch_appointment.stop_sequence || 0);
    });
    $.each(jobsArr, function (i, job) {
      $callsContainer.append(createJobCard(job));
    });

    // Inner sortable for jobs within this group
    new Sortable($callsContainer[0], {
      group: "jobs",
      animation: 150,
      onEnd: async function (evt) {
        if (evt.to == evt.from) {
          await resequenceContainer(evt.to, 'techgroup');
          // refreshTechJobsOnMap will be called by resequenceContainer if needed
        }
      },
      onAdd: async function (evt) {
        const item = evt.item;
        const jobId = item.dataset.jobId;
        await assignJob(jobId, {
          appointment_set: dateStr,
          assigned_techs: techVal
        });
        await resequenceContainer(evt.to, 'techgroup');

        if ($(evt.from).closest(".tech-group").length) {
          await resequenceContainer(evt.from, 'techgroup');
        }
        // refreshTechJobsOnMap will be called by resequenceContainer if needed
      },
      onRemove: async function (evt) {
        // Map will be refreshed by the onAdd handler of the destination container
        // or by assignJob if the job is moved to an unscheduled area        
      }
    });

    return $groupDiv[0];
  }


  async function handleDragTech(evt) {
    const toContainer = evt.to;
    const toContainerClasses = $(toContainer).attr('class').split(' ');
    const hasCallsContainer = toContainerClasses.includes('calls-container');
    const item = evt.item;
    const itemClasses = $(item).attr('class').split(' ');
    const hasTechGroup = itemClasses.includes('tech-group');
    let updates = [];

    if (hasCallsContainer && hasTechGroup) {
      const $kanCol = $(toContainer).closest('.kan-col');
      console.log($kanCol);
      if ($kanCol.length) {
        const colType = $($kanCol[0]).data('col-type');
        if (colType == 'dateColumn') {
          const appointmentDate = $($kanCol[0]).data('date-value');
          const techId = $(item).data('tech-val');
          const $cards = $(item).find('.call-card');
          updates = [];
          let seq = 1;
          $cards.each(function () {
            const jobId = parseInt($(this).data("job-id"));
            if (!isNaN(jobId) && jobId != null) {
              updates.push({
                job_id: jobId,
                assigned_tech: techId,
                appointment_date: appointmentDate,
                stop_sequence: seq
              });
              seq++;
            }
          });
        }
      }
    }

    await Promise.all(updates.map(update =>
      updateJob(
        update.job_id,
        {
          stop_sequence: update.stop_sequence,
          assigned_tech: update.assigned_tech,
          appointment_date: update.appointment_date,
        },
        'sequence'
      )
    ));
  }

  // DragEnd handler from Open Calls and Parking Exit Calls to Schedule Columns
  async function handleDragEnd(evt) {
    const toContainer = evt.to;
    const toContainerClasses = $(toContainer).attr('class').split(' ');
    const hasCallsContainer = toContainerClasses.includes('zone-calls');
    const item = evt.item;
    const itemClasses = $(item).attr('class').split(' ');
    const hasCallCard = itemClasses.includes('call-card');
    let updates = [];

    if (hasCallsContainer && hasCallCard) {
      const $kanCol = $(toContainer).closest('.kan-col');

      if ($kanCol.length) {
        const colType = $($kanCol[0]).data('col-type');
        if (colType == 'openCalls') {
          const $cards = $(item);
          console.log($cards);
          updates = [];
          $cards.each(function () {
            const jobId = parseInt($(this).data("job-id"));
            if (!isNaN(jobId) && jobId != null) {
              updates.push({
                job_id: jobId
              });
            }
          });
        }
      }
    }

    await Promise.all(updates.map(update =>
      updateJob(
        update.job_id,
        {},
        'remove_appointment'
      )
    ));

    await loadKanboard();
  }

  function getNewAssignmentForTarget(containerEl, draggedItem) {
    let node = containerEl;
    while (node && !node.dataset.colType && node !== document.body) {
      node = node.parentElement;
    }
    if (!node || !node.dataset.colType) return null;

    const colType = node.dataset.colType;
    if (colType === "parkingExit") {
      return { newDate: "2001-01-01", newTech: "", extraField: "exit_garage" };
    }

    if (colType === "openCalls") {
      return { newDate: "2001-01-01", newTech: "" };
    } else if (colType === "techGroup") {
      return { newDate: node.dataset.dateValue, newTech: node.dataset.techVal };
    } else if (colType === "dateColumn" && draggedItem.dataset.assignedTech) {
      const existingTech = draggedItem.dataset.assignedTech || "";
      return { newDate: node.dataset.dateValue, newTech: existingTech };
    } else if (colType === "dateColumn" && !draggedItem.dataset.assignedTech && draggedItem.dataset.techVal) {
      const existingTech = draggedItem.dataset.techVal || "";

      return {
        newDate: node.dataset.dateValue,
        newTech: existingTech
      };
    }

    return null;
  }

  // Drivers Logic
  async function refreshDriverSelect() {
    const ds = document.getElementById("driverSelect");
    ds.innerHTML = "";
    const optAll = document.createElement("option");
    optAll.value = "";
    optAll.textContent = "All";
    ds.appendChild(optAll);

    const arr = await fetchDriverPositions();
    arr.forEach(function (user) {
      var o = document.createElement("option");
      o.value = user.id;
      o.textContent = `${user.user_name} | ${user.driver_device?.display_name}`;
      ds.appendChild(o);
    });
  }

  async function loadDriversOnMap() {
    const arr = await fetchDriverPositions(driverSelect.val());
    updateDriverPositionsOnMap(arr);
  }

  function updateDriverPositionsOnMap(driverList) {
    clearMapMarkers();

    driverList.forEach(function (d, ind) {
      const lastPosition = d.driver_device?.last_position;
      if (lastPosition) {
        const lat = parseFloat(lastPosition.lat);
        const lng = parseFloat(lastPosition.lng);
        const angle = parseFloat(lastPosition.angle || 0);

        if (isNaN(lat) || isNaN(lng)) {
          App.error(`Invalid driver coordinates for ${d.user_name}: lat: ${lastPosition.lat}, lng: ${lastPosition.lng}`);
          return;
        }

        if (ind == 0) {
          map.setCenter(new google.maps.LatLng(lat, lng));
        }

        // Get marker color from employee map or use default
        let markerColor = "#fff";
        for (var id in employeeMapById) {
          if (id === d.id && employeeMapById[id].user_profile?.user_color) {
            markerColor = employeeMapById[id].user_profile?.user_color;
            break;
          }
        }

        // Create SVG arrow icon with rotation based on angle
        // Create SVG arrow icon with rotation based on angle
        const arrowSvg = `
        <svg width="22.5" height="22.5" viewBox="0 0 66.145833 66.14583" 
          style="transform: rotate(${angle}deg); transform-origin: center;">
          <g transform="translate(-64.435504,-108.9663)">
            <path style="fill:#FF8B7E;fill-opacity:1;stroke:#0a0a0a;stroke-width:5.66929125;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1" 
              d="M 125,2.0471908 C 90.320014,89.900702 58.905314,166.12113 26.456325,248.19674 l 98.262975,-47.86176 98.4415,47.84966 z" 
              transform="matrix(0.26458333,0,0,0.26458333,64.435504,108.94744)">
            </path>
          </g>
        </svg>
      `;

        // Create a container for the marker
        const markerContainer = document.createElement("div");
        markerContainer.style.position = "relative";
        markerContainer.innerHTML = arrowSvg;

        // Add driver name label below the arrow
        const nameLabel = document.createElement("div");
        nameLabel.style.position = "absolute";
        nameLabel.style.top = "100%";
        nameLabel.style.left = "50%";
        nameLabel.style.transform = "translateX(-50%)";
        nameLabel.style.backgroundColor = "rgba(255, 255, 255, 0.8)";
        nameLabel.style.padding = "2px 4px";
        nameLabel.style.borderRadius = "2px";
        nameLabel.style.fontSize = "10px";
        nameLabel.style.fontWeight = "bold";
        nameLabel.style.whiteSpace = "nowrap";
        nameLabel.style.border = "1px solid #000";
        nameLabel.style.marginTop = "2px";
        nameLabel.textContent = `${d.user_name}${lastPosition.drive_status ? ` | ${lastPosition.drive_status}` : ''}`;
        markerContainer.appendChild(nameLabel);

        // Create the marker
        const mk = new google.maps.marker.AdvancedMarkerElement({
          map: map,
          position: new google.maps.LatLng(lat, lng),
          content: markerContainer,
          title: d.user_name,
          zIndex: 1000 // Ensure driver markers appear above job markers
        });


        // Add click listener to show address
        const infoWindow = new google.maps.InfoWindow();
        let statusText = lastPosition.drive_status_duration_value && lastPosition.drive_status_duration_unit == 's' ? App.secondsToDhms(lastPosition.drive_status_duration_value) : '';
        if (statusText) statusText = ` (${statusText})`;

        // Show loading message
        infoWindow.setContent(`
        <div style="min-width: 200px; padding: 8px;">
          <h3 style="margin: 0 0 8px 0; font-size: 16px;">${d.user_name}</h3>
          <p style="margin: 0 0 8px 0;">
            <strong>Position:</strong> ${lat.toFixed(6)}, ${lng.toFixed(6)}
          </p>
          <p style="margin: 0 0 8px 0;">
            <strong>Heading:</strong> ${angle}°
          </p>
          <p style="margin: 0 0 8px 0;">
            <strong>Address:</strong> <span id="driver-address-${d.id}">Loading...</span>
          </p>
          <p style="margin: 0 0 8px 0;"><strong>Status:</strong> <span>${lastPosition.drive_status}</span> <span>${statusText}</span></p>
        </div>
      `);

        mk.addListener("click", function () {
          // Create info window
          infoWindow.open(map, mk);

          // Reverse geocode to get address
          const geocoder = new google.maps.Geocoder();
          geocoder.geocode({ location: { lat, lng } }, function (results, status) {
            const addressElement = document.getElementById(`driver-address-${d.id}`);

            if (status === "OK" && results[0]) {
              // Update info window with address
              addressElement.textContent = results[0].formatted_address;

              // Add timestamp if available
              if (lastPosition.dt_server) {
                const timestamp = document.createElement("p");
                $(timestamp).attr('class', 'lastUpdated');
                timestamp.style.margin = "8px 0 8px 0";
                timestamp.style.fontSize = "12px";
                timestamp.style.color = "#666";

                // Format the timestamp
                const date = new Date(lastPosition.dt_server);
                const formattedDate = date.toLocaleString();
                timestamp.innerHTML = `<strong>Last updated:</strong> ${formattedDate}`;
                $(addressElement.parentNode).find('.lastUpdated').remove();
                addressElement.parentNode.appendChild(timestamp);
              }
            } else {
              // Show error if geocoding failed
              addressElement.textContent = "Address not available";
            }
          });
        });

        driverMarkers.push(mk);
      }
    });

    DriverOnMap.updateMapWithRoutes();
  }


  function setDriverColor() {
    const driverId = driverSelect.val();

    if (!driverId) {
      App.info("Pick a specific driver first!");
      return;
    }
    const c = driverColorInput.value || "#FF0000";
    driverColors[driverId] = c;
    console.log('colors: ', driverColors);
    console.log("Set color for driver \"" + driverId + "\" => " + c);
    loadKanboard();
  }

  // Edit Job Modal Logic
  var editJobBackdrop = document.getElementById("editJobBackdrop");
  var editJobModal = document.getElementById("editJobModal");
  var closeModalBtn = document.getElementById("closeEditModal");
  var editJobIdSpan = document.getElementById("editJobId");
  var editJobAddress = document.getElementById("editJobAddress");
  var editJobNotes = document.getElementById("editJobNotes");
  var editJobTech = document.getElementById("editJobTech");
  var editJobTasks = document.getElementById("editJobTasks");
  var editingJobId = null;

  function openEditModal(job) {
    console.log('openEditModal', job);
    editingJobId = job.id;
    editJobIdSpan.textContent = job.id;
    editJobAddress.value = job.billing_address || "";
    editJobNotes.value = job.job_notes || "";
    editJobTasks.value = job.job_tasks || "";
    editJobTech.value = job.assigned_techs || "";
    editJobBackdrop.classList.add("active");
  }

  function closeEditModal() {
    editJobBackdrop.classList.remove("active");
    editingJobId = null;
  }

  async function saveEditJobChanges() {
    if (!editingJobId) {
      alert("No job is being edited");
      return;
    }
    var address = editJobAddress.value.trim();
    var notes = editJobNotes.value.trim();
    var tasks = editJobTasks.value.trim();
    var assigned = editJobTech.value || "";

    try {
      var lat = null, lng = null;
      if (address) {
        var geo = await geocodeAddress(address);
        lat = geo.lat;
        lng = geo.lng;
      }
      var updateData = {
        assigned_techs: assigned,
        job_notes: notes,
        job_tasks: tasks,
        billing_address: address
      };

      if (lat && lng) {
        updateData.cust_lat = lat.toFixed(6);
        updateData.cust_long = lng.toFixed(6);
      }

      await assignJob(editingJobId, updateData);
      closeEditModal();
      loadKanboard();
    } catch (err) {
      console.error("Error in saveEditJobChanges =>", err);
      alert("Failed to update job. Check console for details.");
    }
  }


  // ----------------------------------------------------------------------------

  // Geocoding Function

  function geocodeAddress(address) {

    return new Promise(function (resolve, reject) {

      var geocoder = new google.maps.Geocoder();

      geocoder.geocode({ address: address }, function (results, status) {

        if (status === google.maps.GeocoderStatus.OK) {

          var loc = results[0].geometry.location;

          resolve({ lat: loc.lat(), lng: loc.lng() });

        } else {

          reject("Geocode failed => " + status);

        }

      });

    });

  }


  // ----------------------------------------------------------------------------

  // createJobCard: Builds a job card with sequence number, status lights, and displays logged actions.

  function getSentimentColor(count) {

    // For each extra "unhappy" count beyond 2, reduce green and blue channels.

    const extra = count - 2;

    const reduction = extra * 20;

    const gb = Math.max(0, 255 - reduction);

    // Returns an rgb string (e.g. "rgb(255,215,215)")

    return `rgb(255, ${gb}, ${gb})`;

  }


  // -----------------------------------------------------------------------------

  // Helper function: showReasonsModal()

  // This creates a modal overlay and displays each reason (with alternating background colors)

  function showReasonsModal(reasons) {

    let modalOverlay = document.getElementById("reasonModalOverlay");

    if (!modalOverlay) {

      modalOverlay = document.createElement("div");

      modalOverlay.id = "reasonModalOverlay";

      modalOverlay.style.position = "fixed";

      modalOverlay.style.top = "0";

      modalOverlay.style.left = "0";

      modalOverlay.style.width = "100%";

      modalOverlay.style.height = "100%";

      modalOverlay.style.backgroundColor = "rgba(0,0,0,0.5)";

      modalOverlay.style.display = "flex";

      modalOverlay.style.alignItems = "center";

      modalOverlay.style.justifyContent = "center";

      modalOverlay.style.zIndex = "9999";

      document.body.appendChild(modalOverlay);

    }

    // Clear any previous content

    modalOverlay.innerHTML = "";


    // Create modal content container

    const modalContent = document.createElement("div");

    modalContent.style.backgroundColor = "#fff";

    modalContent.style.padding = "20px";

    modalContent.style.borderRadius = "5px";

    modalContent.style.maxWidth = "600px";

    modalContent.style.maxHeight = "80%";

    modalContent.style.overflowY = "auto";

    modalContent.style.boxShadow = "0 0 10px rgba(0,0,0,0.3)";


    // Title

    const title = document.createElement("h3");

    title.textContent = "Reasons for Unhappiness";

    modalContent.appendChild(title);


    // Display each reason in its own block with alternating colors

    reasons.forEach((reason, index) => {

      const reasonDiv = document.createElement("div");

      reasonDiv.style.padding = "10px";

      reasonDiv.style.marginBottom = "5px";

      reasonDiv.style.borderRadius = "3px";

      reasonDiv.style.backgroundColor = (index % 2 === 0) ? "#f9f9f9" : "#e9e9e9";

      reasonDiv.textContent = "On this call, the reason was: " + reason;

      modalContent.appendChild(reasonDiv);

    });


    // Close button

    const closeBtn = document.createElement("button");

    closeBtn.textContent = "Close";

    closeBtn.style.marginTop = "10px";

    closeBtn.addEventListener("click", () => {

      modalOverlay.style.display = "none";

    });

    modalContent.appendChild(closeBtn);


    modalOverlay.appendChild(modalContent);

    modalOverlay.style.display = "flex";

  }


  // -----------------------------------------------------------------------------

  // Helper function: showReasonsModal()

  // This creates a modal overlay and displays each reason with its audio (if available)

  // in alternating shaded blocks.


  function showSmsDriverModal(driverPhone, customerAddress) {

    let modal = document.getElementById("smsDriverModal");

    if (!modal) {

      // Create the modal container

      modal = document.createElement("div");

      modal.id = "smsDriverModal";

      // Modal container styles – hidden by default

      modal.style.position = "fixed";

      modal.style.top = "0";

      modal.style.left = "0";

      modal.style.width = "100%";

      modal.style.height = "100%";

      modal.style.backgroundColor = "rgba(0, 0, 0, 0.7)";

      modal.style.display = "none";  // Hidden by default

      modal.style.justifyContent = "center";

      modal.style.alignItems = "center";

      modal.style.zIndex = "47";


      // Modal content HTML

      modal.innerHTML = `

      <div id="smsDriverModalContent" style="background:#fff; padding:20px; border-radius:5px; width:400px; max-width:90%; box-shadow:0 0 10px rgba(0,0,0,0.3);">

        <div style="text-align:right;">

          <button id="closeSmsDriverModal" style="background:none; border:none; font-size:24px; cursor:pointer;">&times;</button>

        </div>

        <h3>Text the Driver</h3>

        <div style="margin-bottom:10px;">

          <label style="font-weight:bold;">Driver Phone:</label>

          <input type="text" id="smsDriverTo" readonly style="width:100%; padding:5px; margin-top:5px;"/>

        </div>

        <div style="margin-bottom:10px;">

          <label style="font-weight:bold;">Customer Address:</label>

          <input type="text" id="smsDriverAddress" readonly style="width:100%; padding:5px; margin-top:5px;"/>

        </div>

        <div style="margin-bottom:10px;">

          <label style="font-weight:bold;">Message:</label>

          <textarea id="smsDriverBody" rows="4" style="width:100%; padding:5px; margin-top:5px;"></textarea>

        </div>

        <div style="text-align:center;">

          <button id="sendSmsDriverBtn" style="padding:8px 12px;" class="bg-cyan-500 px-4 py-1">Send SMS</button>

        </div>

        <div id="smsDriverMessages" style="margin-top:15px; border-top:1px solid #ccc; padding-top:10px;"></div>

      </div>

    `;

      document.body.appendChild(modal);


      // Close button: Hide the modal when clicked.

      document.getElementById("closeSmsDriverModal").addEventListener("click", function () {

        modal.style.display = "none";

      });


      // Send SMS button: Send the SMS then hide the modal on success.

      document.getElementById("sendSmsDriverBtn").addEventListener("click", function () {

        const to = document.getElementById("smsDriverTo").value;

        const address = document.getElementById("smsDriverAddress").value;

        const message = document.getElementById("smsDriverBody").value;

        // Prepend customer address to the message.

        const fullMessage = address + " " + message;

        fetch(BASE_API_URL + "sms/send_sms_driver.php", {

          method: "POST",

          headers: { "Content-Type": "application/json" },

          body: JSON.stringify({

            to: to,

            message: fullMessage,

            from: "+19412080001"

          })

        })

          .then(res => res.json())

          .then(data => {

            if (data.success) {

              alert("Message sent!");

              loadSmsDriverMessages(to);

              modal.style.display = "none";

            } else {

              alert("Failed to send SMS: " + data.error);

            }

          })

          .catch(err => {

            console.error("Error sending SMS to driver", err);

            alert("Error sending SMS.");

          });

      });

    }


    // Update the modal fields with the current driver phone and customer address.

    document.getElementById("smsDriverTo").value = driverPhone;

    document.getElementById("smsDriverAddress").value = customerAddress;

    document.getElementById("smsDriverBody").value = "";

    loadSmsDriverMessages(driverPhone);


    // Show the modal by setting display to flex.

    modal.style.display = "flex";

  }


  // Helper function: loadSmsDriverMessages()

  // Loads returned SMS messages for the driver from the new receive endpoint.

  function loadSmsDriverMessages(driverPhone) {

    fetch(BASE_API_URL + "sms/receive_sms_driver.php?to=" + encodeURIComponent(driverPhone))

      .then(res => res.json())

      .then(data => {

        const messagesDiv = document.getElementById("smsDriverMessages");

        messagesDiv.innerHTML = "";

        if (Array.isArray(data) && data.length > 0) {

          data.forEach(msg => {

            const msgDiv = document.createElement("div");

            msgDiv.style.borderBottom = "1px solid #ccc";

            msgDiv.style.padding = "5px 0";

            msgDiv.textContent = msg.message; // Adjust the field name as needed.

            messagesDiv.appendChild(msgDiv);

          });

        } else {

          messagesDiv.textContent = "No messages yet.";

        }

      })

      .catch(err => {

        console.error("Error loading driver SMS messages", err);

      });

  }

  // Helper function: getDriverPhoneByName()
  // Loops through employeeMapById to find a driver whose name matches (case-insensitive)
  // and returns their phone number.
  function getDriverPhoneByName(driverName) {

    if (!driverName) return "";

    for (let id in employeeMapById) {

      const emp = employeeMapById[id];

      if (emp.name && emp.name.trim().toLowerCase() === driverName.trim().toLowerCase()) {
        return emp.phone ? emp.phone.trim() : "";
      }
    }

    return "";

  }


  // Helper function: loadSmsDriverMessages()

  // Loads returned SMS messages for the driver using the new receive endpoint.

  function loadSmsDriverMessages(driverPhone) {

    fetch(BASE_API_URL + "sms/receive_sms_driver.php?to=" + encodeURIComponent(driverPhone))

      .then(res => res.json())

      .then(data => {

        const messagesDiv = document.getElementById("smsDriverMessages");

        messagesDiv.innerHTML = "";

        if (Array.isArray(data) && data.length > 0) {

          data.forEach(msg => {

            const msgDiv = document.createElement("div");

            msgDiv.style.borderBottom = "1px solid #ccc";

            msgDiv.style.padding = "5px 0";

            msgDiv.textContent = msg.message; // Adjust field name as needed.

            messagesDiv.appendChild(msgDiv);

          });

        } else {

          messagesDiv.textContent = "No messages yet.";

        }

      })

      .catch(err => {

        console.error("Error loading driver SMS messages", err);

      });

  }

  // Updated loadSentimentData (unchanged from previous version).
  async function loadSentimentData() {
    try {
      const res = await fetch(BASE_API_URL + "other/get_sentiment");

      if (!res.ok) {
        console.error("Failed to load sentiment data");
        window.sentimentLookup = {};
        return;
      }

      const data = await res.json();
      window.sentimentLookup = {};

      if (data?.success) {
        data.data.forEach(record => {
          if (record.phone_number) {
            const key = record.phone_number.trim();
            window.sentimentLookup[key] = {
              unhappy_count: record.unhappy_count,
              reason_for_unhappiness: record.reason_for_unhappiness,
              recording_url: record.recording_url
            };
          }
        });
      }

    } catch (err) {
      console.error("Error loading sentiment data:", err);
      window.sentimentLookup = {};
    }
  }

  function getColorFromZipcode(zipcode) {
    // Simple hash function to generate colors
    let hash = 0;
    for (let i = 0; i < zipcode.length; i++) {
      hash = zipcode.charCodeAt(i) + ((hash << 5) - hash);
    }

    // Convert to hex color
    const r = ((hash & 0xFF) % 200 + 55).toString(16).padStart(2, '0');
    const g = (((hash >> 8) & 0xFF) % 200 + 55).toString(16).padStart(2, '0');
    const b = (((hash >> 16) & 0xFF) % 200 + 55).toString(16).padStart(2, '0');

    return `#${r}${g}${b}`;
  }

  const buildStatusIcon = (statusName, job) => {
    const $icon = $(`<span id="job_${statusName}_${job.id}"></span>`);

    // Define tooltip text based on status name and current status
    let tooltipText = "";

    if (statusName == "appointment") {
      let color = DispatchColors.appointment_status[job?.dispatch_status?.appointment_status];
      if (!color) {
        $icon.addClass('bg-gray-500');
        tooltipText = "No appointment status set";
      } else {
        $icon.css('background', color);
        tooltipText = DispatchStatus.appointment_status[job?.dispatch_status?.appointment_status] || "Appointment Status";
      }

      $icon.html(`
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
          <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 6.75c0 8.284 6.716 15 15 15h2.25a2.25 2.25 0 0 0 2.25-2.25v-1.372c0-.516-.351-.966-.852-1.091l-4.423-1.106c-.44-.11-.902.055-1.173.417l-.97 1.293c-.282.376-.769.542-1.21.38a12.035 12.035 0 0 1-7.143-7.143c-.162-.441.004-.928.38-1.21l1.293-.97c.363-.271.527-.734.417-1.173L6.963 3.102a1.125 1.125 0 0 0-1.091-.852H4.5A2.25 2.25 0 0 0 2.25 4.5v2.25Z" />
        </svg>
      `);
    } else if (statusName == "authorization") {
      let color = DispatchColors.authorization_status[job?.dispatch_status?.authorization_status];
      if (!color) {
        $icon.addClass('bg-gray-500');
        tooltipText = "No authorization status set";
      } else {
        $icon.css('background', color);
        tooltipText = DispatchStatus.authorization_status[job?.dispatch_status?.authorization_status] || "Authorization Status";
      }

      const isAutho = job?.isAuthoRequired == "true" ? true : false;
      if (isAutho) {
        $icon.addClass("border-2 border-orange-400");
      }

      $icon.html(`
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
          <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 15.75V18m-7.5-6.75h.008v.008H8.25v-.008Zm0 2.25h.008v.008H8.25V13.5Zm0 2.25h.008v.008H8.25v-.008Zm0 2.25h.008v.008H8.25V18Zm2.498-6.75h.007v.008h-.007v-.008Zm0 2.25h.007v.008h-.007V13.5Zm0 2.25h.007v.008h-.007v-.008Zm0 2.25h.007v.008h-.007V18Zm2.504-6.75h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V13.5Zm0 2.25h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V18Zm2.498-6.75h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V13.5ZM8.25 6h7.5v2.25h-7.5V6ZM12 2.25c-1.892 0-3.758.11-5.593.322C5.307 2.7 4.5 3.65 4.5 4.757V19.5a2.25 2.25 0 0 0 2.25 2.25h10.5a2.25 2.25 0 0 0 2.25-2.25V4.757c0-1.108-.806-2.057-1.907-2.185A48.507 48.507 0 0 0 12 2.25Z" />
        </svg>
      `);
    } else if (statusName == "equipment") {
      let color = DispatchColors.parts_status[job?.dispatch_status?.parts_status];
      if (!color) {
        $icon.addClass('bg-gray-500');
        tooltipText = "No equipment status set";
      } else {
        $icon.css('background', color);
        tooltipText = DispatchStatus.parts_status[job?.dispatch_status?.parts_status] || "Equipment Status";
      }

      $icon.html(`
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
          <path stroke-linecap="round" stroke-linejoin="round" d="M11.42 15.17 17.25 21A2.652 2.652 0 0 0 21 17.25l-5.877-5.877M11.42 15.17l2.496-3.03c.317-.384.74-.626 1.208-.766M11.42 15.17l-4.655 5.653a2.548 2.548 0 1 1-3.586-3.586l6.837-5.63m5.108-.233c.55-.164 1.163-.188 1.743-.14a4.5 4.5 0 0 0 4.486-6.336l-3.276 3.277a3.004 3.004 0 0 1-2.25-2.25l3.276-3.276a4.5 4.5 0 0 0-6.336 4.486c.091 1.076-.071 2.264-.904 2.95l-.102.085m-1.745 1.437L5.909 7.5H4.5L2.25 3.75l1.5-1.5L7.5 4.5v1.409l4.26 4.26m-1.745 1.437 1.745-1.437m6.615 8.206L15.75 15.75M4.867 19.125h.008v.008h-.008v-.008Z" />
        </svg>
      `);
    } else if (statusName == "ncc") {
      let color = DispatchColors.ncc_status[job?.dispatch_status?.ncc_status];
      if (!color) {
        $icon.addClass('bg-gray-500');
        tooltipText = "No NCC status set";
      } else {
        $icon.css('background', color);
        tooltipText = DispatchStatus.ncc_status[job?.dispatch_status?.ncc_status] || "NCC Status";
      }

      $icon.html(`
        <svg viewBox="0 0 24 24" fill="none" stroke-width="1.5" stroke="currentColor" class="size-6" xmlns="http://www.w3.org/2000/svg">
          <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2"/>
          <path d="M8 10C8 8.34315 9.34315 7 11 7H13C14.6569 7 16 8.34315 16 10C16 11.6569 14.6569 13 13 13H11C9.34315 13 8 14.3431 8 16C8 17.6569 9.34315 19 11 19H13C14.6569 19 16 17.6569 16 16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          <line x1="4" y1="4" x2="20" y2="20" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>

      `);
    } else if (statusName == "drive") {
      let color = DispatchColors.drive_status[job?.dispatch_status?.drive_status];
      if (!color) {
        $icon.addClass('bg-gray-500');
        tooltipText = "No Drive status";
      } else {
        $icon.css('background', color);
        tooltipText = DispatchStatus.drive_status[job.dispatch_status.drive_status] || "NCC Status";
      }

      $icon.html(`
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
          <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12" />
        </svg>
      `);
    } else {
      tooltipText = "Unknown status";
      $icon.html(`
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
          <path stroke-linecap="round" stroke-linejoin="round" d="M10.343 3.94c.09-.542.56-.94 1.11-.94h1.093c.55 0 1.02.398 1.11.94l.149.894c.07.424.384.764.78.93.398.164.855.142 1.205-.108l.737-.527a1.125 1.125 0 0 1 1.45.12l.773.774c.39.389.44 1.002.12 1.45l-.527.737c-.25.35-.272.806-.107 1.204.165.397.505.71.93.78l.893.15c.543.09.94.559.94 1.109v1.094c0 .55-.397 1.02-.94 1.11l-.894.149c-.424.07-.764.383-.929.78-.165.398-.143.854.107 1.204l.527.738c.32.447.269 1.06-.12 1.45l-.774.773a1.125 1.125 0 0 1-1.449.12l-.738-.527c-.35-.25-.806-.272-1.203-.107-.398.165-.71.505-.781.929l-.149.894c-.09.542-.56.94-1.11.94h-1.094c-.55 0-1.019-.398-1.11-.94l-.148-.894c-.071-.424-.384-.764-.781-.93-.398-.164-.854-.142-1.204.108l-.738.527c-.447.32-1.06.269-1.45-.12l-.773-.774a1.125 1.125 0 0 1-.12-1.45l.527-.737c.25-.35.272-.806.108-1.204-.165-.397-.506-.71-.93-.78l-.894-.15c-.542-.09-.94-.56-.94-1.109v-1.094c0-.55.398-1.02.94-1.11l.894-.149c.424-.07.765-.383.93-.78.165-.398.143-.854-.108-1.204l-.526-.738a1.125 1.125 0 0 1 .12-1.45l.773-.773a1.125 1.125 0 0 1 1.45-.12l.737.527c.35.25.807.272 1.204.107.397-.165.71-.505.78-.929l.15-.894Z" />
          <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
        </svg>
      `);
    }

    $icon.addClass("cursor-pointer p-1 rounded-full text-white select-none");

    // Add tooltip attribute
    $icon.attr("title", tooltipText);

    $icon.on("click", async (e) => {
      e.stopPropagation();
      switch (statusName) {
        case 'appointment':
          EditAppointmentStatusModule.openEditModal(job);
          break;
        // Add more cases for other status types as needed
      }
    });

    return $icon[0];
  }

  // Revised createJobCard function.
  function createJobCard(job) {
    const stopSeq = job.dispatch_appointment ? "#" + job.dispatch_appointment.stop_sequence : "";
    const zoneStr = job.zone_name ? "Zone " + job.zone_name : "";
    let custName = "(No Name)";
    if (job.dispatch_customers.length) {
      custName = job.dispatch_customers[0].customer.name;
    }

    let address = "";
    if (job.dispatch_customers) {
      if (job.dispatch_customers.length > 0) {
        if (job.dispatch_customers[0].customer.customer_properties.length > 0) {
          address = job.dispatch_customers[0].customer.customer_properties[0].streetName + " " +
            job.dispatch_customers[0].customer.customer_properties[0].streetNumber + " " +
            job.dispatch_customers[0].customer.customer_properties[0].city + " " +
            job.dispatch_customers[0].customer.customer_properties[0].state + " " +
            job.dispatch_customers[0].customer.customer_properties[0].zip;
        }
      }
    }
    const phone = (job.phone_number || "").trim();
    const assignedTech = job?.dispatch_user ? job?.dispatch_user : null;

    let cardBorderColor = '#eee';
    if (DispatchColors.job_status[job?.dispatch_status?.job_status]) {
      cardBorderColor = DispatchColors.job_status[job?.dispatch_status?.job_status];
    }

    // Create card with jQuery
    const $card = $("<div>")
      .addClass(`call-card min-w-[300px] collapsed border-l-4 border-[${cardBorderColor}]`)
      .attr("data-job-id", job.id)
      .attr("data-job-status", job?.dispatch_status?.job_status)
      .attr("data-assigned-tech", assignedTech?.user_id || "");

    // Set background from tech color
    let bgColor = "bg-gray-200";
    const assignedId = assignedTech?.user_id;

    if (assignedId) {
      bgColor = "bg-green-200";
    }
    $card.addClass(bgColor);

    // Header section
    const isProdJob = job.env == "production" ? "bg-blue-300" : "bg-purple-300";
    const $headerDiv = $("<div>")
      .addClass("card-header")
      .addClass(isProdJob)
      .html(`
        <div class="flex justify-between font-bold">
          ${stopSeq} ${zoneStr} ${custName}
          <span>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
              <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 15 12 18.75 15.75 15m-7.5-6L12 5.25 15.75 9" />
            </svg>
          </span>
        </div>
        <div class="card-address text-xs py-2">${address}</div>`);
    $card.append($headerDiv);

    // Status row (📞 💰 🔧 🅿️ + lights + emoji)
    const $statusWrapper = $("<div>")
      .addClass("always-visible-status flex flex-col items-start gap-2");

    const $statusContainer = $("<div>")
      .css({
        display: "flex",
        alignItems: "center",
        gap: "5px"
      });

    if (job?.dispatch_status?.job_status == "250") {
      const $details = $("<div>").addClass("dispatch-accept flex gap-2");
      const $acceptBtn = $("<button>")
        .text("Accept")
        .addClass("bg-cyan-500 px-4 py-1")
        .on("click", (e) => {
          e.stopPropagation();

          acceptDeclineJob(
            job.id,
            {
              action: "accept"
            }
          );
        });

      const $declineBtn = $("<button>")
        .text("Decline")
        .addClass("bg-gray-500 px-4 py-1")
        .on("click", (e) => {
          e.stopPropagation();

          acceptDeclineJob(
            job.id,
            {
              action: "decline"
            }
          );
        });
      $details.append($acceptBtn);
      $details.append($declineBtn);
      $card.append($details);
      return $card[0];
    }


    $statusContainer.append(buildStatusIcon("appointment", job));
    $statusContainer.append(buildStatusIcon("authorization", job));
    $statusContainer.append(buildStatusIcon("equipment", job));
    $statusContainer.append(buildStatusIcon("drive", job));
    $statusContainer.append(buildStatusIcon("ncc", job));

    // 🅿️ Parking icon 📞 💰 🔧 🅿️
    const $garageIcon = $("<span>")
      .text("🅿️")      
      .addClass("cursor-pointer text-lg rounded-full select-none relative")
      .attr("title", "Set Garage Status")
      .css({
        'padding': '2px 4px',        
        'backgroundColor': job.dispatch_status?.parking_status === 'parked' ? '#ffb3b3' : 
                          job.dispatch_status?.parking_status === 'unparked' ? '#b3d1ff' : '#f0f0f0',
        'border': job.dispatch_status?.parking_status === 'parked' ? '1px solid #ff8080' : 
                 job.dispatch_status?.parking_status === 'unparked' ? '1px solid #80a3ff' : '1px solid #ddd',
        'opacity': job.dispatch_status?.parking_status === 'parked' || job.dispatch_status?.parking_status === 'unparked' ? '1' : '0.7'
      })
      .on("click", function (e) {
        e.preventDefault();
        e.stopPropagation();
        showGaragePopup(job, $card[0]);
      });

    $statusContainer.append($garageIcon);

    // Status lights
    const $lightsDiv = $("<div>")
      .addClass("status-lights")
      .css({
        display: "flex",
        gap: "3px"
      });

    statusCategories.forEach(cat => {
      const $ind = $("<span>").addClass("status-indicator");
      const text = `${job.job_notes || ""} ${job.job_tasks || ""}`.toLowerCase();
      let color = cat.default_color;
      (mappings[cat.id] || []).forEach(m => {
        if (text.includes(m.keyword.toLowerCase())) color = m.color;
      });
      $ind.css("backgroundColor", color);
      $lightsDiv.append($ind);
    });

    // Sentiment emoji
    const $sentiment = $("<span>");
    const data = window.sentimentLookup?.[phone];
    const count = data?.unhappy_count || 0;
    const emoji = count === 0 ? "😀" : count === 1 ? "😐" : "☹️";
    $sentiment.text(`${emoji} (${count})`);
    $sentiment.css("fontWeight", "bold");

    if (count > 2) {
      const gb = Math.max(0, 255 - (count - 2) * 20);
      $sentiment.css("color", `rgb(255,${gb},${gb})`);
    }

    $sentiment.on("click", (e) => {
      e.stopPropagation();
      if (data?.reason_for_unhappiness?.length) {
        showReasonsModal(data.reason_for_unhappiness);
      } else {
        alert("No reason provided.");
      }
    });

    $statusWrapper.append($statusContainer);
    $statusWrapper.append($lightsDiv);
    $statusWrapper.append($sentiment);
    $card.append($statusWrapper);

    // Detail section (collapsible)
    const $details = $("<div>").addClass("card-details");

    const user = window.loggedInEmployee?.name || "";
    if (user) {
      const $userDiv = $("<div>")
        .css({
          fontSize: "10px",
          color: "#555"
        })
        .text("Logged in as: " + user);
      $details.append($userDiv);
    }

    const $addrDiv = $("<div>")
      .css({
        fontSize: "10px",
        color: "#333"
      })
      .text(address);
    $details.append($addrDiv);

    if (phone) {
      const $phoneDiv = $("<div>")
        .css({
          fontSize: "10px",
          color: "#333"
        })
        .html(`Phone: <a href="tel:${phone}" onclick="event.stopPropagation();">${phone}</a>`);
      $details.append($phoneDiv);
    }

    const tech = employeeMapById[job.assigned_techs]?.user_name || "Unassigned";
    const $techDiv = $("<div>")
      .css({
        fontSize: "10px",
        fontStyle: "italic"
      })
      .text("Driver: " + tech);
    $details.append($techDiv);

    const $actionsDiv = $("<div>")
      .addClass("job-actions")
      .css({
        fontSize: "9px",
        marginTop: "5px",
        borderTop: "1px dashed #ccc",
        paddingTop: "3px"
      })
      .text("Loading actions...");
    $details.append($actionsDiv);

    // Add buttons to card
    addSmsDriverButtonToCard($details, job);
    // todo @deprecated 2025-06-18
    // addSmsButtonToCard($details, job);

    addEstimateButtonToCard($details, job);
    const $editBtn = $("<button>")
      .text("Edit")
      .addClass("edit-btn bg-cyan-500 px-4 py-1")
      .on("click", (e) => {
        e.stopPropagation();
        if (job.dispatch_status?.job_status == "250" || job.dispatch_status?.job_status == "260") {
          App.info("Cannot edit a job that is not accepted.");
          return;
        }
        EditJobModule.openEditModal(job);
      });
    $details.append($editBtn);

    $card.append($details);

    // Add click handler to header
    $headerDiv.on("click", (e) => {
      e.stopPropagation();
      $card.toggleClass("collapsed");
    });

    $actionsDiv.remove();

    return $card[0]; // Return the DOM element
  }

  // New helper: addSmsDriverButtonToCard()
  // This version uses the driver name (from the assigned tech) to look up the driver's phone number.
  function addSmsDriverButtonToCard(card, job) {

    if (job.assigned_techs) {

      let driverName = "";

      if (employeeMapById[job.assigned_techs] && employeeMapById[job.assigned_techs].name) {

        driverName = employeeMapById[job.assigned_techs].name;

      } else {

        driverName = job.assigned_techs; // fallback if job.assigned_techs is already the name

      }

      const driverPhone = getDriverPhoneByName(driverName);

      if (driverPhone) {

        const smsDriverBtn = document.createElement("button");

        smsDriverBtn.textContent = "SMS Driver";

        smsDriverBtn.classList.add("sms-driver-btn", "bg-cyan-500", "px-4", "py-1");

        smsDriverBtn.style.marginTop = "5px";

        smsDriverBtn.addEventListener("click", (e) => {

          e.stopPropagation();

          showSmsDriverModal(driverPhone, job.billing_address || "");

        });

        card.appendChild(smsDriverBtn);

      } else {

        console.log("No SMS driver button - phone not found for driver name:", driverName);

      }

    }

  }

  // Helper function to add the "Send SMS" button
  function addSmsButtonToCard($card, job) {
    const $smsBtn = $("<button>")
      .text("Send SMS")
      .addClass("bg-cyan-500 px-4 py-1")
      .css({
        fontSize: "10px",
        marginTop: "5px"
      })
      .on("click", function (e) {
        e.stopPropagation();

        // Use job.phone_number field for sending SMS.
        const phoneNumber = job.dispatch_customers?.[0]?.customer?.customer_phones?.[0]?.phone || "";

        if (!phoneNumber) {
          App.info("No phone number available for this job.");
          return;
        }

        openSmsModal(job.id, phoneNumber);
      });

    $card.append($smsBtn);
  }

  function addEstimateButtonToCard($card, job) {
    // Assigned or inProgress?
    if (job.dispatch_status?.job_status == "250" || job.dispatch_status?.job_status == "20") {
      const estBtn = document.createElement("button");

      estBtn.textContent = "Estimate";
      estBtn.classList.add("bg-cyan-500", "px-4", "py-1", "ml-1");
      estBtn.style.fontSize = "10px";
      estBtn.style.marginTop = "5px";

      estBtn.addEventListener("click", function (e) {
        e.stopPropagation();
        openEstModal(job);
      });
      $card.append(estBtn);
    }
  }

  // ----------------------------------------------------------------------------

  // Status Lights Modal Logic (unchanged)

  function openLightsModal() {

    document.getElementById("catName1").value = statusCategories[0].name || "";

    document.getElementById("catColor1").value = statusCategories[0].color || "#ff0000";

    document.getElementById("catKeywords1").value = statusCategories[0].keywords || "";

    document.getElementById("catName2").value = statusCategories[1].name || "";

    document.getElementById("catColor2").value = statusCategories[1].color || "#00ff00";

    document.getElementById("catKeywords2").value = statusCategories[1].keywords || "";

    document.getElementById("catName3").value = statusCategories[2].name || "";

    document.getElementById("catColor3").value = statusCategories[2].color || "#0000ff";

    document.getElementById("catKeywords3").value = statusCategories[2].keywords || "";

    document.getElementById("lightsConfigBackdrop").classList.add("active");

  }

  function closeLightsModal() {

    document.getElementById("lightsConfigBackdrop").classList.remove("active");

  }

  async function saveCategoriesConfig() {

    var newCats = [

      {

        name: document.getElementById("catName1").value.trim(),

        color: document.getElementById("catColor1").value,

        keywords: document.getElementById("catKeywords1").value.trim()

      },

      {

        name: document.getElementById("catName2").value.trim(),

        color: document.getElementById("catColor2").value,

        keywords: document.getElementById("catKeywords2").value.trim()

      },

      {

        name: document.getElementById("catName3").value.trim(),

        color: document.getElementById("catColor3").value,

        keywords: document.getElementById("catKeywords3").value.trim()

      }

    ];

    try {

      var res = await fetch(BASE_API_URL + "other/save_categories.php", {

        method: "POST",

        headers: { "Content-Type": "application/json" },

        body: JSON.stringify(newCats)

      });

      var result = await res.json();

      if (result.success) {

        statusCategories = newCats;

        loadKanboard();

        closeLightsModal();

      } else {

        alert("Failed to save categories");

      }

    } catch (err) {

      console.error("Error saving categories:", err);

      alert("Error saving categories");

    }

  }

  // Draggable & Resizable Map Logic
  var isDraggingPopup = false, popupOffsetX = 0, popupOffsetY = 0;
  var isResizing = false, lastResizeX = 0, lastResizeY = 0;

  async function setInitialize() {
    const $mapPopup = $("#mapPopup");
    const $mapHeader = $("#mapPopupHeader");
    const $mapResizer = $("#mapResizer");

    $mapHeader.on("mousedown", function (e) {
      isDraggingPopup = true;
      popupOffsetX = e.clientX - $mapPopup.offset().left;
      popupOffsetY = e.clientY - $mapPopup.offset().top;
    });

    $(document).on("mouseup", function () {
      isDraggingPopup = false;
    });

    $(document).on("mousemove", function (e) {
      if (isDraggingPopup) {
        $mapPopup.css({
          left: (e.clientX - popupOffsetX) + "px",
          top: (e.clientY - popupOffsetY) + "px"
        });
      }
    });

    $mapResizer.on("mousedown", function (e) {
      e.stopPropagation();
      isResizing = true;
      lastResizeX = e.clientX;
      lastResizeY = e.clientY;
    });

    $(document).on("mouseup", function () {
      isResizing = false;
    });

    $(document).on("mousemove", function (e) {
      if (isResizing) {
        const dx = e.clientX - lastResizeX;
        const dy = e.clientY - lastResizeY;
        $mapPopup.css({
          width: ($mapPopup.width() + dx) + "px",
          height: ($mapPopup.height() + dy) + "px"
        });
        lastResizeX = e.clientX;
        lastResizeY = e.clientY;
      }
    });

    const now = new Date();
    const yyyy = now.getFullYear();
    const mm = String(now.getMonth() + 1).padStart(2, "0");
    const dd = String(now.getDate()).padStart(2, "0");
    $("#datePicker").val(yyyy + "-" + mm + "-" + dd);

    await refreshDriverSelect();
    try {
      const res = await $.ajax({
        url: BASE_API_URL + "other/get_categories",
        method: "GET"
      });

      if (Array.isArray(res) && res.length >= 3) {
        statusCategories = res.slice(0, 3);
      } else {
        statusCategories = [
          { id: 1, name: "Status 1", color: "#FFFFFF", keywords: "" },
          { id: 2, name: "Status 2", color: "#FFFFFF", keywords: "" },
          { id: 3, name: "Status 3", color: "#FFFFFF", keywords: "" }
        ];
      }
    } catch (err) {
      console.error("Error loading status categories:", err);
      statusCategories = [
        { id: 1, name: "Status 1", color: "#FFFFFF", keywords: "" },
        { id: 2, name: "Status 2", color: "#FFFFFF", keywords: "" },
        { id: 3, name: "Status 3", color: "#FFFFFF", keywords: "" }
      ];
    }

    await loadMappings();
    await loadKanboard();

    const $techSelectModal = $("#editJobTech");
    $.each(employeeMapById, function (id, e) {
      $techSelectModal.append($("<option>").val(id).text(e.name));
    });

    $("#closeEditModal").on("click", closeEditModal);
    $("#saveEditJobBtn").on("click", saveEditJobChanges);

    if (window.userRole === "admin") {
      $("#manageLightsBtn").show().on("click", openLightsModal);
    }

    $("#closeLightsModal").on("click", closeLightsModal);
    $("#saveCategoriesBtn").on("click", saveCategoriesConfig);
  }

</script>

<script src="<?php echo BASE_URL; ?>assets/js/popup_window.js" defer></script>

<script>

  // --- Cookie Utility Functions ---
  function setCookie(cname, cvalue, exdays) {
    var d = new Date();
    d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
    var expires = "expires=" + d.toUTCString();
    document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
  }

  function getCookie(cname) {

    var name = cname + "=";

    var ca = document.cookie.split(';');

    for (var i = 0; i < ca.length; i++) {

      var c = ca[i].trim();

      if (c.indexOf(name) === 0) return c.substring(name.length, c.length);

    }

    return "";

  }

  window.employeeMapById = {};


  // --- Fetch Employees Function ---

  async function fetchEmployees() {

    const url = BASE_API_URL + "employees/get?with=userProfile,tenantRoles";
    const r = await fetch(url);
    const data = await r.json();

    if (!r.ok) throw new Error("Failed to fetch employees => " + r.statusText);

    return data;

  }


</script>

<script>
  (function () {

    // Open the SMS modal and load templates every time.

    function openSmsModal(jobId, phoneNumber) {

      let smsModal = document.getElementById("smsModal");

      if (!smsModal) {

        smsModal = document.createElement("div");

        smsModal.id = "smsModal";

        smsModal.className = "modal-backdrop active";

        smsModal.innerHTML = `

        <div class="modal-content">

          <span class="modal-close" id="smsModalClose">&#10006;</span>

          <h3>Send SMS</h3>

          <div class="modal-row">

            <label for="smsTo">To:</label>

            <input type="text" id="smsTo" readonly />

          </div>

          <div class="modal-row">

            <label for="smsTemplate">Template:</label>

            <select id="smsTemplate"
            class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5"
            >

              <option value="">-- Select Template --</option>

            </select>

            <button id="loadTemplatesBtn" class="bg-cyan-500 px-4 py-1">Load Templates</button>

          </div>

          <div class="modal-row">

            <label for="smsBody">Message:</label>

            <textarea id="smsBody" rows="4" cols="40"></textarea>

          </div>

          <button id="sendSmsBtn" class="bg-cyan-500 px-4 py-1">Send SMS</button>

          <hr/>

          <div class="modal-row">

            <label for="newTemplate">New Template:</label>

            <input type="text" id="newTemplate" placeholder="Template text" />

            <button id="saveTemplateBtn" class="bg-cyan-500 px-4 py-1">Save Template</button>

          </div>

        </div>

      `;

        document.body.appendChild(smsModal);

        document.getElementById("smsModalClose").addEventListener("click", function () {

          smsModal.classList.remove("active");

        });

        document.getElementById("loadTemplatesBtn").addEventListener("click", loadSmsTemplates);

        document.getElementById("sendSmsBtn").addEventListener("click", function () {

          sendSms(jobId);

        });

        document.getElementById("saveTemplateBtn").addEventListener("click", saveSmsTemplate);

      }

      // Set the "To" field and always refresh the templates

      document.getElementById("smsTo").value = phoneNumber;

      loadSmsTemplates();

      smsModal.classList.add("active");

    }

    function openEstModal(job) {
      console.log(job)

      let $estModal = $('#estModal');
      $estModal.addClass('active')
      estTable.init(job);
    }

    // Fetch SMS templates from your API and populate the dropdown.

    async function loadSmsTemplates() {

      try {

        console.log("Loading SMS templates…");

        const response = await fetch(BASE_API_URL + "sms/get_sms_templates.php");

        if (!response.ok) {

          console.error("Failed to load SMS templates: " + response.statusText);

          return;

        }

        const templates = await response.json();

        console.log("Templates returned:", templates);

        const templateSelect = document.getElementById("smsTemplate");

        if (!templateSelect) {

          console.error("Element with id 'smsTemplate' not found in the DOM.");

          return;

        }

        // Clear existing options and add default.

        templateSelect.innerHTML = "<option value=''>-- Select Template --</option>";

        templates.forEach(t => {

          const opt = document.createElement("option");

          opt.value = t.template_text;

          opt.textContent = t.template_text;

          templateSelect.appendChild(opt);

        });

        // Attach the change listener only once.

        if (!templateSelect.dataset.listenerAttached) {

          templateSelect.addEventListener("change", function () {

            const smsBody = document.getElementById("smsBody");

            if (smsBody) {

              smsBody.value = templateSelect.value;

            } else {

              console.error("Element with id 'smsBody' not found.");

            }

          });

          templateSelect.dataset.listenerAttached = "true";

        }

      } catch (err) {

        console.error("Error loading SMS templates:", err);

      }

    }


    // Send the SMS by posting to send_sms.php

    async function sendSms(jobId) {

      const to = document.getElementById("smsTo").value;

      const body = document.getElementById("smsBody").value;

      if (!to || !body || !employeeId) {

        alert("Missing phone number, message, or employee ID.");

        return;

      }

      try {

        const res = await fetch(BASE_API_URL + "sms/send_sms.php", {

          method: "POST",

          headers: { "Content-Type": "application/json" },

          body: JSON.stringify({

            to: to,

            message: body,

            job_id: jobId,

            employee_id: employeeId

          })

        });

        const result = await res.json();

        if (result.success) {

          alert("SMS sent successfully!");

          document.getElementById("smsModal").classList.remove("active");

        } else {

          alert("Failed to send SMS: " + result.error);

        }

      } catch (err) {

        console.error("Error sending SMS:", err);

      }

    }


    // Save a new SMS template via save_sms_template.php

    async function saveSmsTemplate() {

      const newTemplate = document.getElementById("newTemplate").value;

      if (!newTemplate) {

        alert("Please enter a template text.");

        return;

      }

      try {

        const res = await fetch(BASE_API_URL + "sms/save_sms_template.php", {

          method: "POST",

          headers: { "Content-Type": "application/json" },

          body: JSON.stringify({ template_text: newTemplate })

        });

        const result = await res.json();

        if (result.success) {

          alert("Template saved successfully!");

          loadSmsTemplates();

          document.getElementById("newTemplate").value = "";

        } else {

          alert("Failed to save template: " + result.error);

        }

      } catch (err) {

        console.error("Error saving SMS template:", err);

      }

    }

    // Expose our functions to the global scope.
    window.openSmsModal = openSmsModal;
    window.loadSmsTemplates = loadSmsTemplates;
    window.sendSms = sendSms;
    window.saveSmsTemplate = saveSmsTemplate;
    window.openEstModal = openEstModal;
  })();

</script>

<script>

  // Helper: Look up a driver's color by name (case-insensitive)

  function getDriverColorByName(driverName) {

    if (!driverName) return "#eee";

    for (let id in employeeMapById) {

      const emp = employeeMapById[id];

      if (emp.name && emp.name.trim().toLowerCase() === driverName.trim().toLowerCase()) {

        return emp.color || "#eee";

      }

    }

    return "#eee";

  }


  // Helper: Load all inbound SMS messages using the new endpoint.
  // function loadInboundSmsMessages() {
  //   fetch(BASE_API_URL + "sms/get_inbound_sms.php")
  //     .then(res => res.json())
  //     .then(data => {
  //       const messagesDiv = document.getElementById("inboundSmsMessages");
  //       messagesDiv.innerHTML = "";
  //       if (Array.isArray(data) && data.length > 0) {
  //         data.forEach(msg => {
  //           const msgDiv = document.createElement("div");
  //           msgDiv.style.padding = "10px";
  //           msgDiv.style.marginBottom = "5px";
  //           // Look up the driver's color
  //           const driverName = msg.driver_name || "Unknown";
  //           const color = getDriverColorByName(driverName);
  //           msgDiv.style.backgroundColor = color;
  //           msgDiv.style.borderRadius = "3px";
  //           msgDiv.style.fontSize = "12px";
  //           // Format the timestamp
  //           const time = new Date(msg.timestamp).toLocaleString();
  //           msgDiv.innerHTML = `<strong>${driverName}</strong> <em>${time}</em><br>${msg.message}`;
  //           messagesDiv.appendChild(msgDiv);
  //         });
  //       } else {
  //         messagesDiv.textContent = "No inbound messages found.";
  //       }
  //     })
  //     .catch(err => {
  //       console.error("Error loading inbound SMS messages", err);
  //     });
  // }


  // Helper: Create and show the inbound SMS modal popup.
  // function showInboundSmsModal() {
  //   let modal = document.getElementById("inboundSmsModal");
  //   if (!modal) {
  //     // Create the modal container
  //     modal = document.createElement("div");
  //     modal.id = "inboundSmsModal";
  //     Object.assign(modal.style, {
  //       position: "fixed",
  //       top: "0",
  //       left: "0",
  //       width: "100%",
  //       height: "100%",
  //       backgroundColor: "rgba(0,0,0,0.5)",
  //       display: "flex",
  //       alignItems: "center",
  //       justifyContent: "center",
  //       zIndex: "47"
  //     });
  //     modal.innerHTML = `
  //     <div id="inboundSmsModalContent" style="background:#fff; padding:20px; border-radius:5px; width:500px; max-width:90%; max-height:80%; overflow-y:auto; box-shadow:0 0 10px rgba(0,0,0,0.3);">
  //       <div style="text-align:right;">
  //         <button id="closeInboundSmsModal" style="background:none; border:none; font-size:24px; cursor:pointer;" >&times;</button>
  //       </div>
  //       <h3>Inbound SMS Messages</h3>
  //       <div id="inboundSmsMessages" style="margin-top:10px;"></div>
  //     </div>
  //   `;
  //     document.body.appendChild(modal);
  //     // Close the modal when the X button is clicked.
  //     document.getElementById("closeInboundSmsModal").addEventListener("click", function () {
  //       modal.style.display = "none";
  //     });
  //   }
  //   // Load the messages and show the modal.
  //   loadInboundSmsMessages();
  //   modal.style.display = "flex";
  // }

</script>

<script>

  // Global variable to track the last notified timestamp for customer SMS logs.
  let lastCustomerSmsTimestamp = 0;

  // Function to display a toast notification with sound. It now accepts a second parameter: logId.
  function showToast(message, logId) {
    let container = document.getElementById("toastContainer");
    if (!container) {
      container = document.createElement("div");
      container.id = "toastContainer";
      document.body.appendChild(container);
    }

    const toast = document.createElement("div");
    toast.className = "toast";

    // Save the log ID in a data attribute (if available).
    if (logId) {
      toast.dataset.logId = logId;
    }

    // Create the close (×) button.
    const closeBtn = document.createElement("button");
    closeBtn.className = "close-btn";
    closeBtn.innerHTML = "&times;";
    closeBtn.addEventListener("click", function () {
      // If the toast has a log ID, send an update to mark it as Xed out.
      if (toast.dataset.logId) {
        fetch(BASE_API_URL + "other/mark_log_xed.php", {
          method: "POST",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({ log_id: toast.dataset.logId })
        })
          .then(response => response.json())
          .then(result => {
            console.log("Log marked as Xed out:", result);
          })
          .catch(err => {
            console.error("Error marking log as Xed out:", err);
          });
      }

      // Remove the toast from the DOM.
      container.removeChild(toast);
    });
    toast.appendChild(closeBtn);

    // Add the message content.
    const msgSpan = document.createElement("span");
    msgSpan.innerHTML = message;
    toast.appendChild(msgSpan);
    container.appendChild(toast);
    // Play a notification sound.
    const audio = new Audio("https://actions.google.com/sounds/v1/alarms/beep_short.ogg");
    audio.play().catch(err => {
      console.error("Error playing sound:", err);
    });
  }
</script>

<script>

  // Global pagination and search variables for the SMS bar.
  let smsPage = 1;
  const smsLimit = 5;
  let smsSearchEmployee = "";

  // Create the inbound SMS bar if it doesn't already exist.
  // Modal functions for sending a project SMS.
  // This function opens a modal that allows the user to compose and send an SMS.
  // If no phone number is provided, it falls back to the selected employee or the first available employee.
  function openProjectSmsModal(jobId, phoneNumber) {
    // If no phone number was provided, attempt to retrieve it.
    if (!phoneNumber) {
      if (!selectedEmpId && window.employeeMapById) {
        // Fallback: use the first employee in employeeMapById.
        const empIds = Object.keys(window.employeeMapById);
        if (empIds.length > 0) {
          selectedEmpId = empIds[0];
        }
      }
      if (selectedEmpId && window.employeeMapById && window.employeeMapById[selectedEmpId] && window.employeeMapById[selectedEmpId].phone) {
        phoneNumber = window.employeeMapById[selectedEmpId].phone;
      }
    }

    if (!phoneNumber) {
      alert("Missing phone number for the selected employee.");
      return;
    }

    let smsModal = document.getElementById("projectSmsModal");
    if (!smsModal) {
      smsModal = document.createElement("div");
      smsModal.id = "projectSmsModal";
      smsModal.className = "modal-backdrop active";
      smsModal.innerHTML = `
      <div class="modal-content">
        <span class="modal-close" id="projectSmsModalClose">&#10006;</span>
        <h3>Send Project SMS</h3>
        <div class="modal-row">
          <label for="projSmsTo">To:</label>
          <input type="text" id="projSmsTo" readonly style="width:100%; padding:5px; margin-top:5px;"/>
        </div>
        <div class="modal-row">
          <label for="projSmsBody">Message:</label>
          <textarea id="projSmsBody" rows="4" style="width:100%; padding:5px; margin-top:5px;"></textarea>
        </div>
        <button id="sendProjSmsBtn" style="padding:8px 12px;" class="bg-cyan-500 px-4 py-1">Send SMS</button>
      </div>
    `;

      document.body.appendChild(smsModal);
      document.getElementById("projectSmsModalClose").addEventListener("click", function () {
        smsModal.classList.remove("active");
      });

      document.getElementById("sendProjSmsBtn").addEventListener("click", function () {
        sendProjectSms(jobId);
      });
    }

    document.getElementById("projSmsTo").value = phoneNumber;
    document.getElementById("projSmsBody").value = "";
    smsModal.classList.add("active");
  }


  // Function to send the project SMS using your new endpoint.
  async function sendProjectSms(jobId) {
    const to = document.getElementById("projSmsTo").value;
    const body = document.getElementById("projSmsBody").value;

    if (!to || !body || !employeeId) {
      alert("Missing phone number, message, or employee ID.");
      return;
    }

    try {
      const res = await fetch(BASE_API_URL + "sms/send_sms_project.php", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          to: to,
          message: body,
          job_id: jobId, // Can be null if not applicable.
          employee_id: employeeId
        })
      });

      const result = await res.json();
      if (result.success) {
        alert("Project SMS sent successfully!");
        document.getElementById("projectSmsModal").classList.remove("active");
      } else {
        alert("Failed to send project SMS: " + result.error);
      }

    } catch (err) {
      console.error("Error sending project SMS:", err);
    }

  }
</script>

<?php include_once 'home/_gtag.php' ?>
<?php include_once 'home/_estimate.php' ?>
<?php include_once 'home/_edit_job.php' ?>
<?php include_once 'home/_edit_appointment_status.php' ?>
<?php include_once 'home/_dispatch_map.php' ?>
<?php include_once 'home/_drivers_on_map.php' ?>
<?php include_once 'home/_comment_dialog.php' ?>
<?php include_once 'home/_call_dialog.php' ?>
<?php include_once 'home/_new_job_dialog.php' ?>
<?php include_once 'home/_parking_garage_dialog.php' ?>
<?php include_once 'home/_parking_job_overlay.php' ?>

<script>
  $(document).ready(function () {
    $('body').on('click', "div.modal-content2 .modal-close", function (e) {
      $(this).closest('.modal-backdrop').removeClass("active");
    });
    $('body').on('click', "div.modal-content2 .modal-footer .btn-cancel", function (e) {
      $(this).closest('.modal-backdrop').removeClass("active");
    });
  });
</script>
