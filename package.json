{"name": "custom-umijs-antd-pro", "version": "1.0.0", "description": "Custom UmiJS with Ant Design Pro components for custom component development", "main": "index.js", "scripts": {"build": "umi build", "dev": "umi dev", "format": "prettier --write '**/*.{js,jsx,ts,tsx,css,md,json}'", "postinstall": "umi generate tmp", "prettier": "prettier -c --write '**/*.{js,jsx,ts,tsx,css,md,json}'", "start": "npm run dev", "test": "umi-test", "test:coverage": "umi-test --coverage"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,less,md,json}": ["prettier --write"], "*.ts?(x)": ["prettier --parser=typescript --write"]}, "dependencies": {"@ant-design/charts": "^1.4.2", "@ant-design/icons": "^4.8.0", "@ant-design/pro-components": "^2.4.4", "@ant-design/pro-descriptions": "^2.4.4", "@ant-design/pro-form": "^2.4.4", "@ant-design/pro-layout": "^7.8.3", "@ant-design/pro-list": "^2.4.4", "@ant-design/pro-table": "^3.4.4", "@umijs/preset-react": "^4.0.0", "antd": "^5.0.0", "classnames": "^2.3.2", "lodash": "^4.17.21", "moment": "^2.29.4", "react": "^18.2.0", "react-dom": "^18.2.0", "umi": "^4.0.0"}, "devDependencies": {"@types/lodash": "^4.14.191", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.10", "@umijs/lint": "^4.0.0", "lint-staged": "^13.1.0", "prettier": "^2.8.1", "typescript": "^4.9.4", "yorkie": "^2.0.0"}, "engines": {"node": ">=14.0.0"}}