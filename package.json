{"name": "custom-components-builder", "version": "1.0.0", "description": "Custom UmiJS with Ant Design Pro components for PHP injection", "main": "index.js", "scripts": {"build": "umi build", "build:components": "webpack --config webpack.components.js --mode production", "dev": "umi dev", "dev:components": "webpack serve --config webpack.components.js --mode development", "watch": "webpack --config webpack.components.js --mode development --watch", "analyze": "cross-env ANALYZE=1 umi build", "start": "npm run dev"}, "dependencies": {"@ant-design/charts": "^1.4.2", "@ant-design/icons": "^4.8.0", "@ant-design/pro-components": "^2.4.4", "@ant-design/pro-descriptions": "^2.4.4", "@ant-design/pro-form": "^2.4.4", "@ant-design/pro-layout": "^7.8.3", "@ant-design/pro-list": "^2.4.4", "@ant-design/pro-table": "^3.4.4", "@umijs/preset-react": "^4.0.0", "antd": "^5.0.0", "classnames": "^2.3.2", "lodash": "^4.17.21", "moment": "^2.29.4", "react": "^18.2.0", "react-dom": "^18.2.0", "umi": "^4.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/preset-react": "^7.18.0", "@babel/preset-typescript": "^7.18.0", "@types/lodash": "^4.14.191", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.10", "@umijs/lint": "^4.0.0", "babel-loader": "^9.1.0", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "css-loader": "^6.7.0", "html-webpack-plugin": "^5.5.0", "less": "^4.1.3", "less-loader": "^11.1.0", "mini-css-extract-plugin": "^2.7.0", "style-loader": "^3.3.0", "typescript": "^4.9.4", "webpack": "^5.75.0", "webpack-bundle-analyzer": "^4.7.0", "webpack-cli": "^5.0.0", "webpack-dev-server": "^4.11.0"}, "engines": {"node": ">=14.0.0"}}