{"name": "custom-components-builder", "version": "1.0.0", "description": "Custom UmiJS with Ant Design Pro components for PHP injection", "main": "index.js", "license": "Fast Response Dev Team", "scripts": {"build_": "umi build", "build": "webpack --config webpack.components.js --mode production", "build:components": "webpack --config webpack.components.js --mode production", "dev_": "umi dev", "dev": "webpack --watch --config webpack.components.js --mode development", "dev:components": "webpack serve --config webpack.components.js --mode development", "watch": "webpack --config webpack.components.js --mode development --watch", "analyze": "cross-env ANALYZE=1 umi build", "start": "npm run dev"}, "dependencies": {"@ant-design/icons": "^4.8.3", "@ant-design/pro-components": "^2.7.19", "@umijs/preset-react": "^2.1.7", "antd": "^5.21.2", "antd-style": "^3.7.0", "classnames": "^2.3.2", "copy-webpack-plugin": "^13.0.0", "lodash": "^4.17.21", "moment": "^2.29.4", "react": "^18.3.1", "react-dom": "^18.3.1", "umi": "^4.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/preset-react": "^7.18.0", "@babel/preset-typescript": "^7.18.0", "@types/lodash": "^4.14.191", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.10", "@umijs/lint": "^4.0.0", "babel-loader": "^9.1.0", "babel-plugin-import": "^1.13.8", "cross-env": "^7.0.3", "css-loader": "^6.7.0", "html-webpack-plugin": "^5.5.0", "less": "^4.1.3", "less-loader": "^11.1.0", "mini-css-extract-plugin": "^2.7.0", "style-loader": "^3.3.0", "typescript": "^4.9.4", "webpack": "^5.75.0", "webpack-bundle-analyzer": "^4.7.0", "webpack-cli": "^5.0.0", "webpack-dev-server": "^4.11.0"}, "engines": {"node": ">=14.0.0"}}