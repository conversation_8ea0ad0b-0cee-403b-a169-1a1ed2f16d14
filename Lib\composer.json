{"name": "dispatch-butter/webapp", "description": "Web application for Dispatchbatter", "version": "1.0.0", "keywords": ["php", "slim-micro-framework", "mysql", "elloquent", "mpdf", "estimate", "api"], "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "autoload": {"psr-4": {"Butler\\Config\\": "../Config/", "Butler\\Lib\\": "../Lib/", "Butler\\Helpers\\": "../Helpers/", "Butler\\Models\\": "../Models/", "Butler\\Services\\": "../Services/", "Butler\\Controllers\\": "../Controllers/", "Butler\\Views\\": "../Views/"}}, "require": {"google/auth": "^1.47", "twilio/sdk": "^8.6", "phpoffice/phpspreadsheet": "^4.3", "mpdf/mpdf": "^8.2", "illuminate/database": "^9.0", "illuminate/events": "^9.0", "staudenmeir/eloquent-eager-limit": "^1.7", "firebase/php-jwt": "^6.11", "vlucas/phpdotenv": "^5.6"}}