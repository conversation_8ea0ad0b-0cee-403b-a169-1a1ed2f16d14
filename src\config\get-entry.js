const fs = require('fs');
const Path = require('path');
const getPath = require("./get-path");

module.exports = function getEnty(path){
    let entry = {};
    getPath(path).map((i)=>{
        let item = i.substring(path.length + 1);
        const filename = Path.parse(item).name;
        let key = item;
        if (filename == 'index') {
            key = item.replace(`/index.tsx`, ``);
        }
        key = key.replace(".tsx", "");
        entry[`${key}`] = {
            import: `${path}/${item}`,
        };
    });
    return entry;
};
