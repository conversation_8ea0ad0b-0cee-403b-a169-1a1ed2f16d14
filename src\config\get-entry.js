const Path = require('path');
const getPath = require("./get-path");

module.exports = function getEnty(path){
    let entry = {};
    const list  = getPath(path);
    
    console.log();
    console.log(list);
    console.log();

    list.map((i)=>{
        let item = i.substring(path.length + 1);
        const filename = Path.parse(item).name;
        let key = item;
        if (filename == 'index') {
            key = item.replace(`/index.tsx`, ``);
        } else if (filename.endsWith('Page')) {
            // key = item;
        }
        key = key.replace(".tsx", "");

        const libName = key.slice(key.lastIndexOf("/") + 1);
        console.log('-> key: ', key, 'item: ', item, 'i:', i, 'LIB Name', libName);

        entry[`${key}`] = {
            import: `${path}/${item}`,
        };
    });
    return entry;
};
