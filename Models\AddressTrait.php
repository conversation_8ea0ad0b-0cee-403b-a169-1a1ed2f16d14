<?php

namespace Butler\Models;

trait AddressTrait
{
    public function getFullAddressAttribute() {
        $str = $this->street_number . ' ' . $this->street_name;
        if ($this->street_direction) {
            $str .= " {$this->street_direction}";
        }

        if ($this->unit_type) {
            $str .= ", {$this->unit_type}";
        }
        if ($this->unit_number) {
            $str .= " {$this->unit_number}";
        }
        if ($this->city) {
            $str .= ", {$this->city}";
        }
        if ($this->state) {
            $str .= ", {$this->state}";
        }
        if ($this->zip) {
            $str .= " {$this->zip}";
        }

        return $str;
    }
}