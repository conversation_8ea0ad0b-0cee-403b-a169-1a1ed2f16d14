const path = require('path'); const HtmlWebpackPlugin = require('html-webpack-plugin'); const MiniCssExtractPlugin = require('mini-css-extract-plugin'); const { CleanWebpackPlugin } = require('clean-webpack-plugin'); const CopyWebpackPlugin = require('copy-webpack-plugin');
const isDevelopment = process.env.NODE_ENV !== 'production';
module.exports = {
entry: {
// Main entry for all components
'components-bundle': './src/components-entry.tsx',
// Individual component entries for selective loading
'custom-card': './src/components/CustomCard/index.tsx',
'custom-chart': './src/components/CustomChart/index.tsx',
'custom-form': './src/components/CustomForm/index.tsx',
'custom-table': './src/components/CustomTable/index.tsx',
'custom-dashboard': './src/components/CustomDashboard/index.tsx',
},
output: {
path: path.resolve(__dirname, 'dist/components'),
filename: isDevelopment ? '[name].js' : '[name].[contenthash].js',
chunkFilename: isDevelopment ? '[name].chunk.js' : '[name].[contenthash].chunk.js',
publicPath: '/components/',
clean: true,
// Make components available globally
library: {
name: 'CustomComponents',
type: 'umd',
export: 'default',
},
globalObject: 'this',
},
resolve: {
extensions: ['.tsx', '.ts', '.js', '.jsx'],
alias: {
'@': path.resolve(__dirname, 'src'),
'@components': path.resolve(__dirname, 'src/components'),
'@utils': path.resolve(__dirname, 'src/utils'),
'@styles': path.resolve(__dirname, 'src/styles'),
},
},
module: {
rules: [
{
test: /\.(ts|tsx|js|jsx)$/,
exclude: /node_modules/,
use: {
loader: 'babel-loader',
options: {
presets: [
['@babel/preset-env', { targets: 'defaults' }],
['@babel/preset-react', { runtime: 'automatic' }],
'@babel/preset-typescript',
],
plugins: [
// Ant Design import optimization
['import', {
libraryName: 'antd',
libraryDirectory: 'es',
style: true,
}, 'antd'],
['import', {
libraryName: '@ant-design/pro-components',
libraryDirectory: 'es',
style: true,
}, 'pro-components'],
],
},
},
},
{
test: /\.css$/,
use: [
isDevelopment ? 'style-loader' : MiniCssExtractPlugin.loader,
'css-loader',
],
},
{
test: /\.less$/,
use: [
isDevelopment ? 'style-loader' : MiniCssExtractPlugin.loader,
'css-loader',
{
loader: 'less-loader',
options: {
lessOptions: {
modifyVars: {
// Ant Design theme customization
'@primary-color': '#1890ff',
'@border-radius-base': '6px',
'@font-size-base': '14px',
},
javascriptEnabled: true,
},
},
},
],
},
{
test: /\.(png|jpe?g|gif|svg)$/i,
type: 'asset/resource',
generator: {
filename: 'images/[name].[hash][ext]',
},
},
{
test: /\.(woff|woff2|eot|ttf|otf)$/i,
type: 'asset/resource',
generator: {
filename: 'fonts/[name].[hash][ext]',
},
},
],
},
plugins: [
new CleanWebpackPlugin(),
// Extract CSS
new MiniCssExtractPlugin({
    filename: isDevelopment ? '[name].css' : '[name].[contenthash].css',
    chunkFilename: isDevelopment ? '[name].chunk.css' : '[name].[contenthash].chunk.css',
  }),
  
  // Generate HTML for testing components
  new HtmlWebpackPlugin({
    template: './src/component-test.html',
    filename: 'index.html',
    chunks: ['components-bundle'],
    inject: 'body',
  }),
  
  // Copy static assets
  new CopyWebpackPlugin({
    patterns: [
      {
        from: 'src/assets',
        to: 'assets',
        noErrorOnMissing: true,
      },
    ],
  }),
],
// External dependencies (don't bundle these, expect them to be loaded separately)
externals: {
    react: {
    root: 'React',
    commonjs2: 'react',
    commonjs: 'react',
    amd: 'react',
    },
    'react-dom': {
    root: 'ReactDOM',
    commonjs2: 'react-dom',
    commonjs: 'react-dom',
    amd: 'react-dom',
    },
    },
    optimization: {
    splitChunks: {
    chunks: 'all',
    cacheGroups: {
    vendor: {
    test: /[\\/]node_modules[\\/]/,
    name: 'vendors',
    chunks: 'all',
    priority: 10,
    },
    antd: {
    test: /[\\/]node_modules\\/[\\/]/,
    name: 'antd',
    chunks: 'all',
    priority: 20,
    },
    common: {
    name: 'common',
    minChunks: 2,
    chunks: 'all',
    priority: 5,
    reuseExistingChunk: true,
    },
    },
    },
    },
    devServer: {
    static: {
    directory: path.join(__dirname, 'dist/components'),
    },
    compress: true,
    port: 3000,
    hot: true,
    open: true,
    headers: {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
    'Access-Control-Allow-Headers': 'X-Requested-With, content-type, Authorization',
    },
    },
    devtool: isDevelopment ? 'eval-source-map' : 'source-map',
    };