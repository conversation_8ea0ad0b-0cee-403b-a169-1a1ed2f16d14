<?php

namespace Butler\Controllers\api;


use Butler\Controllers\BaseControllerTraits;

class ApiBaseController
{
    use BaseControllerTraits;
    use BaseControllerTraits {
        __construct as __construct_traits;
    }

    public function __construct()
    {
        $this->__construct_traits();
    }

    public function json($data = [])
    {
        header('Content-Type: application/json');
        echo json_encode($data);
        exit;
    }

    /**
     * Return Error in JSON.
     *
     * @param \Exception $error
     * @param $errorCode
     * @param $errorMessage
     * @return void
     */
    public function returnJsonWithError(\Exception $error, $errorCode = 500, $errorMessage = null)
    {
        http_response_code($errorCode);
        $this->json(['success' => false, 'message' => $errorMessage ?? $error->getMessage()]);
    }
}