/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory(require("react"), require("react-dom"));
	else if(typeof define === 'function' && define.amd)
		define(["react", "react-dom"], factory);
	else if(typeof exports === 'object')
		exports["CustomCard/TestPage"] = factory(require("react"), require("react-dom"));
	else
		root["CustomCard/TestPage"] = factory(root["React"], root["ReactDOM"]);
})(this, (__WEBPACK_EXTERNAL_MODULE_react__, __WEBPACK_EXTERNAL_MODULE_react_dom__) => {
return /******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/pages/CustomCard/TestPage.tsx":
/*!*******************************************!*\
  !*** ./src/pages/CustomCard/TestPage.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var antd_es_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! antd/es/card */ \"./node_modules/antd/es/card/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"./node_modules/react/jsx-runtime.js\");\n\n\n\n\nconst CustomCard = ({\n  title = 'Custom Card',\n  description = 'This is a custom card component',\n  avatar,\n  cover,\n  actions = true,\n  hoverable = true,\n  status = 'default',\n  tags = [],\n  onClick\n}) => {\n  const cardActions = actions ? [] : undefined;\n  const statusColors = {\n    success: 'green',\n    processing: 'blue',\n    error: 'red',\n    warning: 'orange',\n    default: 'default'\n  };\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(antd_es_card__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n    title: title,\n    cover: cover && cover,\n    actions: cardActions,\n    hoverable: hoverable,\n    onClick: onClick,\n    style: {\n      borderRadius: 8\n    },\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"h3\", {\n      children: \"Ok, test 002\"\n    })\n  });\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CustomCard);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/CustomCard/TestPage.tsx\n");

/***/ }),

/***/ "react":
/*!**************************************************************************************!*\
  !*** external {"root":"React","commonjs2":"react","commonjs":"react","amd":"react"} ***!
  \**************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE_react__;

/***/ }),

/***/ "react-dom":
/*!*****************************************************************************************************!*\
  !*** external {"root":"ReactDOM","commonjs2":"react-dom","commonjs":"react-dom","amd":"react-dom"} ***!
  \*****************************************************************************************************/
/***/ ((module) => {

module.exports = __WEBPACK_EXTERNAL_MODULE_react_dom__;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			loaded: false,
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Flag the module as loaded
/******/ 		module.loaded = true;
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = __webpack_modules__;
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/chunk loaded */
/******/ 	(() => {
/******/ 		var deferred = [];
/******/ 		__webpack_require__.O = (result, chunkIds, fn, priority) => {
/******/ 			if(chunkIds) {
/******/ 				priority = priority || 0;
/******/ 				for(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];
/******/ 				deferred[i] = [chunkIds, fn, priority];
/******/ 				return;
/******/ 			}
/******/ 			var notFulfilled = Infinity;
/******/ 			for (var i = 0; i < deferred.length; i++) {
/******/ 				var [chunkIds, fn, priority] = deferred[i];
/******/ 				var fulfilled = true;
/******/ 				for (var j = 0; j < chunkIds.length; j++) {
/******/ 					if ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every((key) => (__webpack_require__.O[key](chunkIds[j])))) {
/******/ 						chunkIds.splice(j--, 1);
/******/ 					} else {
/******/ 						fulfilled = false;
/******/ 						if(priority < notFulfilled) notFulfilled = priority;
/******/ 					}
/******/ 				}
/******/ 				if(fulfilled) {
/******/ 					deferred.splice(i--, 1)
/******/ 					var r = fn();
/******/ 					if (r !== undefined) result = r;
/******/ 				}
/******/ 			}
/******/ 			return result;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/global */
/******/ 	(() => {
/******/ 		__webpack_require__.g = (function() {
/******/ 			if (typeof globalThis === 'object') return globalThis;
/******/ 			try {
/******/ 				return this || new Function('return this')();
/******/ 			} catch (e) {
/******/ 				if (typeof window === 'object') return window;
/******/ 			}
/******/ 		})();
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/harmony module decorator */
/******/ 	(() => {
/******/ 		__webpack_require__.hmd = (module) => {
/******/ 			module = Object.create(module);
/******/ 			if (!module.children) module.children = [];
/******/ 			Object.defineProperty(module, 'exports', {
/******/ 				enumerable: true,
/******/ 				set: () => {
/******/ 					throw new Error('ES Modules may not assign module.exports or exports.*, Use ESM export syntax, instead: ' + module.id);
/******/ 				}
/******/ 			});
/******/ 			return module;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/jsonp chunk loading */
/******/ 	(() => {
/******/ 		// no baseURI
/******/ 		
/******/ 		// object to store loaded and loading chunks
/******/ 		// undefined = chunk not loaded, null = chunk preloaded/prefetched
/******/ 		// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded
/******/ 		var installedChunks = {
/******/ 			"CustomCard/TestPage": 0
/******/ 		};
/******/ 		
/******/ 		// no chunk on demand loading
/******/ 		
/******/ 		// no prefetching
/******/ 		
/******/ 		// no preloaded
/******/ 		
/******/ 		// no HMR
/******/ 		
/******/ 		// no HMR manifest
/******/ 		
/******/ 		__webpack_require__.O.j = (chunkId) => (installedChunks[chunkId] === 0);
/******/ 		
/******/ 		// install a JSONP callback for chunk loading
/******/ 		var webpackJsonpCallback = (parentChunkLoadingFunction, data) => {
/******/ 			var [chunkIds, moreModules, runtime] = data;
/******/ 			// add "moreModules" to the modules object,
/******/ 			// then flag all "chunkIds" as loaded and fire callback
/******/ 			var moduleId, chunkId, i = 0;
/******/ 			if(chunkIds.some((id) => (installedChunks[id] !== 0))) {
/******/ 				for(moduleId in moreModules) {
/******/ 					if(__webpack_require__.o(moreModules, moduleId)) {
/******/ 						__webpack_require__.m[moduleId] = moreModules[moduleId];
/******/ 					}
/******/ 				}
/******/ 				if(runtime) var result = runtime(__webpack_require__);
/******/ 			}
/******/ 			if(parentChunkLoadingFunction) parentChunkLoadingFunction(data);
/******/ 			for(;i < chunkIds.length; i++) {
/******/ 				chunkId = chunkIds[i];
/******/ 				if(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {
/******/ 					installedChunks[chunkId][0]();
/******/ 				}
/******/ 				installedChunks[chunkId] = 0;
/******/ 			}
/******/ 			return __webpack_require__.O(result);
/******/ 		}
/******/ 		
/******/ 		var chunkLoadingGlobal = this["webpackChunkcustom_components_builder"] = this["webpackChunkcustom_components_builder"] || [];
/******/ 		chunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));
/******/ 		chunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));
/******/ 	})();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module depends on other loaded chunks and execution need to be delayed
/******/ 	var __webpack_exports__ = __webpack_require__.O(undefined, ["vendors"], () => (__webpack_require__("./src/pages/CustomCard/TestPage.tsx")))
/******/ 	__webpack_exports__ = __webpack_require__.O(__webpack_exports__);
/******/ 	__webpack_exports__ = __webpack_exports__["default"];
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});