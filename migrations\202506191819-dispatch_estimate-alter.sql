CREATE TABLE `sequence_purchase_no`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    PRIMARY KEY (`id`)
);
CREATE TABLE `sequence_invoice_no`
(
    `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
    PRIMARY KEY (`id`)
);

ALTER TABLE `dispatch_estimates`
    CHANGE `estimate_no` `estimate_no` INT(10) UNSIGNED NOT NULL COMMENT 'UQ: Estimation No',
    ADD COLUMN `purchase_no`     VARCHAR(50) NULL COMMENT 'UQ: PurchaseNo' AFTER `estimate_no`,
    ADD COLUMN `issue_date`      DATE        NULL COMMENT 'Issue Date' AFTER `status`,
    ADD COLUMN `work_start_date` DATE        NULL COMMENT 'Work Start Date' AFTER `issue_date`,
    CHANGE `tax_amount` `tax_amount` DECIMAL(20, 4) DEFAULT 0.0000 NOT NULL COMMENT 'Total Tax amount',
    CHANGE `discount_amount` `discount_amount` DECIMAL(20, 4) DEFAULT 0.0000 NOT NULL COMMENT 'Discount amount',
    CHANGE `payment_terms` `payment_terms` VARCHAR(255) CHARSET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'Payment terms',
    ADD UNIQUE INDEX `UQ_dispatch_estimates_purchase_no` (`purchase_no`);


ALTER TABLE `dispatch_estimates`
    DROP COLUMN `estimate_no`,
    DROP INDEX `UQ_dispatch_estimates_estimate_no`;


ALTER TABLE `dispatch_estimates`
    ADD COLUMN `tax_percent` DECIMAL(6, 2) DEFAULT 0 NOT NULL COMMENT 'Tax percentage' AFTER `subtotal`;


ALTER TABLE `dispatch_estimate_items`
    ADD COLUMN `price_total` DECIMAL(20, 4) DEFAULT 0.0000 NULL AFTER `price`,
    CHANGE `total` `total` DECIMAL(20, 4) DEFAULT 0.0000 NOT NULL AFTER `tax_amount`;


ALTER TABLE `pb_service`
    CHANGE `taxable` `taxable` TINYINT(1) DEFAULT 0 NULL COMMENT 'Taxable status';



CREATE TABLE `tenant_addresses`
(
    `id`               int(10) unsigned NOT NULL AUTO_INCREMENT,
    `tenant_id`        int(10) unsigned NOT NULL,
    `address_type`     varchar(31)      NOT NULL default 'billing_address' comment 'Addresses. e.g. billing_address/address',
    `is_active`        tinyint          NOT NULL DEFAULT 1 COMMENT 'active?',
    `company`          varchar(255)              DEFAULT NULL,
    `name`             varchar(255)              DEFAULT NULL,
    `email`            varchar(255)              DEFAULT NULL,
    `phone`            varchar(255)              DEFAULT NULL,
    `city`             varchar(100)              DEFAULT NULL,
    `state`            varchar(100)              DEFAULT NULL,
    `street_direction` varchar(100)              DEFAULT NULL,
    `street_name`      varchar(100)              DEFAULT NULL,
    `street_number`    varchar(100)              DEFAULT NULL,
    `unit_number`      varchar(100)              DEFAULT NULL,
    `unit_type`        varchar(100)              DEFAULT NULL,
    `zip`              varchar(100)              DEFAULT NULL,
    `zip_four`         varchar(100)              DEFAULT NULL,
    `created_by`       int(10) unsigned NOT NULL,
    `created_at`       datetime         NOT NULL DEFAULT current_timestamp(),
    `updated_by`       int(10) unsigned NOT NULL,
    `updated_at`       timestamp        NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    PRIMARY KEY (`id`),
    KEY `FK_tenant_addresses_tenant_id` (`tenant_id`),
    KEY `FK_tenant_addresses_address_type` (`address_type`),
    CONSTRAINT `FK_tenant_addresses_tenant_id` FOREIGN KEY (`tenant_id`) REFERENCES `tenants` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_general_ci;