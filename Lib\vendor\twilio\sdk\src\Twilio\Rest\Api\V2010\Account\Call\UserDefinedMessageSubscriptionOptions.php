<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Api\V2010\Account\Call;

use Twilio\Options;
use Twilio\Values;

abstract class UserDefinedMessageSubscriptionOptions
{
    /**
     * @param string $idempotencyKey A unique string value to identify API call. This should be a unique string value per API call and can be a randomly generated.
     * @param string $method The HTTP method T<PERSON><PERSON> will use when requesting the above `Url`. Either `GET` or `POST`. Default is `POST`.
     * @return CreateUserDefinedMessageSubscriptionOptions Options builder
     */
    public static function create(
        
        string $idempotencyKey = Values::NONE,
        string $method = Values::NONE

    ): CreateUserDefinedMessageSubscriptionOptions
    {
        return new CreateUserDefinedMessageSubscriptionOptions(
            $idempotencyKey,
            $method
        );
    }


}

class CreateUserDefinedMessageSubscriptionOptions extends Options
    {
    /**
     * @param string $idempotencyKey A unique string value to identify API call. This should be a unique string value per API call and can be a randomly generated.
     * @param string $method The HTTP method Twilio will use when requesting the above `Url`. Either `GET` or `POST`. Default is `POST`.
     */
    public function __construct(
        
        string $idempotencyKey = Values::NONE,
        string $method = Values::NONE

    ) {
        $this->options['idempotencyKey'] = $idempotencyKey;
        $this->options['method'] = $method;
    }

    /**
     * A unique string value to identify API call. This should be a unique string value per API call and can be a randomly generated.
     *
     * @param string $idempotencyKey A unique string value to identify API call. This should be a unique string value per API call and can be a randomly generated.
     * @return $this Fluent Builder
     */
    public function setIdempotencyKey(string $idempotencyKey): self
    {
        $this->options['idempotencyKey'] = $idempotencyKey;
        return $this;
    }

    /**
     * The HTTP method Twilio will use when requesting the above `Url`. Either `GET` or `POST`. Default is `POST`.
     *
     * @param string $method The HTTP method Twilio will use when requesting the above `Url`. Either `GET` or `POST`. Default is `POST`.
     * @return $this Fluent Builder
     */
    public function setMethod(string $method): self
    {
        $this->options['method'] = $method;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Api.V2010.CreateUserDefinedMessageSubscriptionOptions ' . $options . ']';
    }
}


