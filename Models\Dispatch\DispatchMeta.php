<?php

namespace Butler\Models\Dispatch;

use <PERSON>\Models\BaseModel;


class DispatchMeta extends BaseModel {
    // Job is on Hold - we need to park the job in the garage for a few days
    public const PARKING_GARAGE = 'parking_garage'; // Job is on Hold

    // Senior Technician Needed - a junior technician requesting help fixing a complicated problem
    public const PARKING_TECH_SR_NEED = 'parking_tech_sr_need';
    
    // Parts home warranty - we need to get or order parts with home warranty 
    public const PARKING_PARTS_HOME_WARRANTY = 'parking_parts_home_warranty';

    // Parts fast response - we are providing the parts 
    public const PARKING_PARTS_FAST_RESPONSE = 'parking_parts_fast_response';

    // Home Warranty
    // AUTH REQUIRED - submit to warranty company 
    public const PARKING_AUTH_REQUIRED = 'parking_auth_required';

    // DENIAL - the warranty company denied the clam
    public const PARKING_AUTH_DENIAL = 'parking_auth_denied_clam';

    // Unpaid NCC - there is an outstanding payment needed
    public const PARKING_NCC_UNPAID_NCC = 'parking_unpaid_ncc'; 

    // DO NOT SERVICE - this is a problem customer we will not service
    public const PARKING_SERVICE_DO_NOT_SERVICE = 'parking_service_do_not_service'; 

    // Customer missed appointment - if the technician went to the house and no one answered the door
    public const PARKING_APPOINTMENT_CUSTOMER_MISSED = 'parking_appointment_customer_missed'; // Customer Missed Appointment


    protected $table = 'dispatch_meta';

    /**
     * @var array
     */
    protected $fillable = ['dispatch_id', 'key', 'value'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function dispatch() {
        return $this->belongsTo(Dispatch::class, 'dispatch_id');
    }

}