import React from 'react';
import { Card, Avatar, Space, Tag } from 'antd';
interface CustomCardProps {
    title?: string;
    description?: string;
    avatar?: string;
    cover?: string;
    actions?: boolean;
    hoverable?: boolean;
    status?: 'success' | 'processing' | 'error' | 'warning' | 'default';
    tags?: string[];
    onClick?: () => void;
}
const CustomCard: React.FC<CustomCardProps> = ({
    title = 'Custom Card',
    description = 'This is a custom card component',
    avatar,
    cover,
    actions = true,
    hoverable = true,
    status = 'default',
    tags = [],
    onClick,
}) => {
    const cardActions = actions ? [] : undefined;
    const statusColors = {
        success: 'green',
        processing: 'blue',
        error: 'red',
        warning: 'orange',
        default: 'default',
    };
    return (
        <Card
            title={title}
            cover={cover && cover}
            actions={cardActions}
            hoverable={hoverable}
            onClick={onClick}
            style={{ borderRadius: 8 }}
        >
            <h3>Ok, test 002</h3>
        </Card>

    );
};
export default CustomCard;