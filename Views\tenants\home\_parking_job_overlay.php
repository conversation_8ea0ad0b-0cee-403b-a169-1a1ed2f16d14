<!-- Parking Garage Overlay -->
<div id="garageOverlay"
  class="hidden fixed inset-0 w-full h-full bg-white z-[10] overflow-y-auto p-5">
  <div class="flex justify-between items-center mb-3">
    <h2 id="garageTitle" class="text-xl font-bold">🚗 Parking Garage</h2>
    <button onclick="closeGarageOverlay()" class="text-xl bg-cyan-500 hover:bg-cyan-600 text-white px-4 py-1 rounded">✖</button>
  </div>

  <label class="block text-sm font-medium text-gray-700 mb-1">Select Garage Category:</label>
  <select id="garageTypeSelector" onchange="loadGarageView()"
    class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 mb-5">
    <option value="">-- Choose --</option>
    <option value="all">All</option>
    <option value="tech_sr_need">Senior Technician Needed</option>
    <option value="parts_home_warranty">Parts Home Warranty</option>
    <option value="parts_fast_response">Parts Fast Response</option>
    <option value="auth_required">Auth Required</option>
    <option value="auth_denied_clam">Auth Denial</option>
    <option value="unpaid_ncc">Unpaid NCC</option>
    <option value="service_do_not_service">Do Not Service</option>
    <option value="appointment_customer_missed">Customer Missed Appointment</option>
  </select>

  <div id="garageCalendarContainer" class="mt-5"></div>
</div>

<script>
function showGarageOverlay() {    
  $('#garageOverlay').removeClass('hidden');
  loadGarageView();
}

function closeGarageOverlay() {  
  $('#garageOverlay').addClass('hidden');
}

async function loadGarageView() {
  const type = $('#garageTypeSelector').val();
  const $container = $('#garageCalendarContainer');
  $container.empty();
  
  if (!type) {
    $container.html('<p class="p-3">Please select a garage category</p>');
    return;
  }
  const typeLabel = $('#garageTypeSelector option:selected').text();
  
  const jobs = await fetchParkedGarageJobs();
  
  // Filter jobs based on selected reason
  let filtered = jobs;
  if (type != "all") {
    filtered = jobs.filter(job => {
      if (!job.dispatch_meta || !Array.isArray(job.dispatch_meta)) return false;
    
      // Check if job has the selected parking reason
      return job.dispatch_meta.some(meta => 
        meta.key === `parking_${type}` && 
        (meta.value === 1 || meta.value === '1' || meta.value === true || meta.value === 'true')
      );
    });
  }
  

  if (filtered.length === 0) {
    $container.html(`<p class="p-3">📭 No jobs found with reason: <strong>${typeLabel}</strong></p>`);
    return;
  }

  // Group jobs by appointment date
  const byDate = {};
  filtered.forEach(job => {
    let appt = job.dispatch_appointment?.appointment_date || "";
    if (!appt) appt = "nodate";
    if (!byDate[appt]) byDate[appt] = [];
    byDate[appt].push(job);
  });

  // Rest of the calendar rendering code remains the same
  const today = new Date();
  const year = today.getFullYear();
  const month = today.getMonth();
  const firstDay = new Date(year, month, 1);
  const lastDay = new Date(year, month + 1, 0);
  const startDate = new Date(firstDay);
  startDate.setDate(startDate.getDate() - startDate.getDay());
  const endDate = new Date(lastDay);
  endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));
  const loopDays = [];
  const loopDate = new Date(startDate);

  while (loopDate <= endDate) {
    loopDays.push(new Date(loopDate));
    loopDate.setDate(loopDate.getDate() + 1);
  }

  const $wrapper = $('<div>')
    .addClass('border-2 border-gray-300 p-4 bg-white');
    
  const $title = $('<h3>')
    .text(`Garage: ${typeLabel} — ${firstDay.toLocaleString('default', { month: 'long' })} ${year}`)
    .addClass('text-center mb-3 font-bold');
    
  $wrapper.append($title);
  
  const $calendar = $('<div>')
    .addClass('grid grid-cols-7 gap-2');

  const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
  dayNames.forEach(day => {
    $('<div>')
      .text(day)
      .addClass('bg-gray-200 font-bold text-center p-1.5')
      .appendTo($calendar);
  });

  loopDays.forEach(dateObj => {
    const dateStr = dateObj.toISOString().split("T")[0];
    const jobsForDay = byDate[dateStr] || [];
    
    const $cell = $('<div>')
      .addClass('border border-gray-300 min-h-[150px] max-h-[300px] overflow-y-auto bg-gray-50 p-1.5');
      
    const $header = $('<div>')
      .text(dateObj.getDate())
      .addClass('font-bold mb-1.5');
      
    $cell.append($header);

    const $content = $('<div>')
      .addClass('overflow-x-auto bg-gray-50 p-1.5');

    jobsForDay.forEach(job => {
      const $card = $(createJobCard(job))
        .addClass('mb-1.5 text-xs p-1.5 bg-white border border-gray-400 rounded');
      $content.append($card);
    });

    $cell.append($content);
    $calendar.append($cell);
  });

  $wrapper.append($calendar);

  if (byDate["nodate"]) {
    const $noDateSection = $('<div>')
      .addClass('mt-5 border-t-2 border-dashed border-gray-400 pt-3');
      
    const $label = $('<h4>')
      .text('🛑 No Appointment Date')
      .addClass('mb-2 font-semibold');
      
    $noDateSection.append($label);
    
    byDate["nodate"].forEach(job => {
      const $card = $(createJobCard(job))
        .addClass('mb-1.5 text-xs p-1.5 bg-white border border-gray-400 rounded');
      $noDateSection.append($card);
    });
    
    $wrapper.append($noDateSection);
  }
  
  $container.append($wrapper);
}
</script>
