<?php

namespace Butler\Controllers\tenants\Estimate;

use Butler\Controllers\tenants\TenantBaseController;
use Butler\Helpers\Func;
use <PERSON>\Helpers\FuncModel;
use Butler\Models\ButlerDB;
use Butler\Models\Dispatch\DispatchEstimate;
use Butler\Models\Dispatch\DispatchEstimateItem;
use Butler\Models\PriceBook\PbService;
use Butler\Models\Sequence\SequencePurchaseNo;
use Illuminate\Database\Eloquent\Builder;

class EstimateController extends TenantBaseController
{
    public function __construct()
    {
        parent::__construct();

        $this->controllerBaseUrl = BASE_URL . '/tenant/estimate';
        $this->templatePath = $this->templatePath . DS . 'estimate';
        $this->menu = 'estimate';
    }

    /**
     * Perform Update or Create
     *
     * @return void
     */
    public function createOrUpdate()
    {
        $data = $this->data;
        $id = $data['id'] ?? null;

        /** @var DispatchEstimate|null $row */
        if (!$id) {
            $data['purchase_no'] = SequencePurchaseNo::generateNo();
            $dataMain = DispatchEstimate::getBoundData($data, true, true);
            $row = new DispatchEstimate($dataMain);
        } else {
            /** @var DispatchEstimate $row */
            $row = DispatchEstimate::findOrFail($id);
        }

        $db = ButlerDB::getDb();
        try {
            $db->beginTransaction();

            $row->fill($data);
            $row->save();

            if (key_exists('items', $data)) {
                $row->items()->delete();

                if ($data['items'] ?? null) {
                    $ind = 1;
                    $items = [];
                    foreach ($data['items'] as $x) {
                        if ($x['service_id']) {
                            /** @var PbService $service */
                            $service = PbService::find($x['service_id']);

                            $item = new DispatchEstimateItem($x);
                            $item->dispatch_estimate_id = $row->id;
                            $item->name = $service->name;
                            $item->cost = $service->cost;
                            $item->price_total = $item->price * $item->qty;

                            if ($item->taxable) {
                                $item->tax_percent = floatval($row->tax_percent);
                            } else {
                                $item->tax_percent = 0;
                            }
                            $item->tax_amount = $item->tax_percent * $item->price_total / 100;
                            $item->total = $item->price_total + $item->tax_amount;
                            $item->sort_order = $ind ++;
                            $items[] = $item;
                        }
                    }

                    // Bulk saving
                    $row->items()->saveMany($items);

                    // Aggregation column updates
                    $row->subtotal = 0;
                    $row->tax_amount = 0;
                    foreach ($row->items as &$item) {
                        $row->subtotal += $item->price_total;
                        $row->tax_amount += $item->tax_amount;
                    }
                    $row->total_item_count = count($row->items);
                    $row->grand_total  = $row->subtotal + $row->tax_amount - $row->discount_amount;
                }
            }

            $db->commit();
        } catch (\Exception $exception) {
            $db->rollBack();
            $this->jsonWithError($exception);
        }

        $this->json($row);;
    }

    /**
     * @param $params
     * @return Builder
     */
    protected function getQueryBuilder($params)
    {
        $qb = DispatchEstimate::query();
        $qb->tenantOnly($this->tenant_id);

        if ($params) {
            $jobId = $params['dispatch_id'] ?? null;
            if ($jobId) {
                $qb->where('dispatch_id', $jobId);
            }

            if ($params['in_status'] ?? null) {
                $qb->whereIn('status', Func::csvToArr($params['in_status']));
            }

            if (Func::keyExistsInWithParam($params, 'dispatch')) {
                $qb->with('dispatch');
            }
            if (Func::keyExistsInWithParam($params, 'customer')) {
                $qb->with('customer');
            }
            if (Func::keyExistsInWithParam($params, 'items')) {
                $qb->with('items');
            }
            if (Func::keyExistsInWithParam($params, 'logs')) {
                $qb->with('logs');
            }
        }
        return $qb;
    }

    public function getOne()
    {
        $params = $this->data;
        $qb = $this->getQueryBuilder($params);
        $row = $qb->first();

        try {
            if (($params['creation_mode'] ?? false) && !$row) {
                $newRow = $params;
                $newRow['purchase_no'] = SequencePurchaseNo::generateNo();
                $newRow = DispatchEstimate::getBoundData($newRow, true, true);
                $row = DispatchEstimate::create($newRow);
            }
        } catch (\Exception $exception) {
            $this->renderWithError($exception);
        }

        $this->render(null, $row);
    }

    public function getAll()
    {
        $this->title = 'Estimates List';

        $params = $this->data;
        $params['with'] = ($params['with'] ?? '') . ',dispatch,customer';
        $qb = $this->getQueryBuilder($params);

        // Pagination
        $page = (int)($params['page'] ?? 1);
        $limit = 10; // Items per page
        $total_records = $qb->count();

        $result = FuncModel::getResultsWithPagination($qb, $page, $limit, $total_records);

        $this->render('estimate_list', $result);
    }
}