<?php

namespace Butler\Controllers\tenants\Estimate;

use Butler\Controllers\tenants\TenantBaseController;
use Butler\Helpers\Func;
use Butler\Helpers\FuncModel;
use Butler\Models\Dispatch\DispatchEstimate;
use Butler\Models\Dispatch\DispatchEstimateItem;
use Butler\Models\PriceBook\PbService;

class EstimateController extends TenantBaseController
{
    public function __construct()
    {
        parent::__construct();

        $this->controllerBaseUrl = BASE_URL . '/tenant/estimate';
        $this->templatePath = $this->templatePath . DS . 'estimate';
        $this->menu = 'estimate';
    }

    public function create()
    {
        $data = $this->data;

        $maxNo = intval(DispatchEstimate::query()->max('estimate_no') ?? 0) + 1;
        $data['estimate_no'] = $maxNo;

        $dataMain = DispatchEstimate::getBoundData($data, true, true);
        $dataMain['created_by'] = $this->tenant_id;
        $dataMain['updated_by'] = $this->tenant_id;

        /** @var DispatchEstimate $row */
        $row = DispatchEstimate::create($dataMain);

        if (key_exists('items', $data)) {
            $row->items()->delete();

            if ($data['items'] ?? null) {
                $ind = 1;
                foreach ($data['items'] as $x) {
                    if ($x['service_id']) {
                        /** @var PbService $service */
                        $service = PbService::find($x['service_id']);

                        $dbRow = DispatchEstimateItem::getBoundData($x);
                        $x['dispatch_estimate_id'] = $row->id;
                        $x['name'] = $service->name;
                        $x['cost'] = $service->cost;
                        $x['sort_order'] = $ind ++;

                        DispatchEstimateItem::create($x);
                    }
                }
            }
        }

        $this->json($row);;
    }

    protected function getQueryBuilder($params)
    {
        $qb = DispatchEstimate::query();
        $qb->tenantOnly($this->tenant_id);

        if ($params) {
            $jobId = $params['jobId'] ?? null;
            if ($jobId) {
                $qb->where('dispatch_id', $jobId);
            }

            if ($params['in_status'] ?? null) {
                $qb->whereIn('status', Func::csvToArr($params['in_status']));
            }
        }
    }

    public function getAll()
    {
        $params = $this->data;

        $jobId = $params['jobId'] ?? null;

        $qb = $this->getQueryBuilder($params);


        // $qb->orderByDesc('created_on');

        $qb->with('dispatch');
        $qb->with('customer');

        // Pagination
        $page = (int)($params['page'] ?? 1);
        $limit = 10; // Items per page
        $total_records = $qb->count();

        $result = FuncModel::getResultsWithPagination($qb, $page, $limit, $total_records);

        if ('json' === ($params['format'] ?? 'null')) {
            $this->json($result);
        } else {
            $this->title = 'Estimates';
            $this->render('estimate_list', $result);
        }
    }
}