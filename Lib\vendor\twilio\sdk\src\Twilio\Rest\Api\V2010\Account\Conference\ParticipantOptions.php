<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Api\V2010\Account\Conference;

use Twilio\Options;
use Twilio\Values;

abstract class ParticipantOptions
{
    /**
     * @param string $statusCallback The URL we should call using the `status_callback_method` to send status information to your application.
     * @param string $statusCallbackMethod The HTTP method we should use to call `status_callback`. Can be: `GET` and `POST` and defaults to `POST`.
     * @param string[] $statusCallbackEvent The conference state changes that should generate a call to `status_callback`. Can be: `initiated`, `ringing`, `answered`, and `completed`. Separate multiple values with a space. The default value is `completed`.
     * @param string $label A label for this participant. If one is supplied, it may subsequently be used to fetch, update or delete the participant.
     * @param int $timeout The number of seconds that we should allow the phone to ring before assuming there is no answer. Can be an integer between `5` and `600`, inclusive. The default value is `60`. We always add a 5-second timeout buffer to outgoing calls, so  value of 10 would result in an actual timeout that was closer to 15 seconds.
     * @param bool $record Whether to record the participant and their conferences, including the time between conferences. Can be `true` or `false` and the default is `false`.
     * @param bool $muted Whether the agent is muted in the conference. Can be `true` or `false` and the default is `false`.
     * @param string $beep Whether to play a notification beep to the conference when the participant joins. Can be: `true`, `false`, `onEnter`, or `onExit`. The default value is `true`.
     * @param bool $startConferenceOnEnter Whether to start the conference when the participant joins, if it has not already started. Can be: `true` or `false` and the default is `true`. If `false` and the conference has not started, the participant is muted and hears background music until another participant starts the conference.
     * @param bool $endConferenceOnExit Whether to end the conference when the participant leaves. Can be: `true` or `false` and defaults to `false`.
     * @param string $waitUrl The URL that Twilio calls using the `wait_method` before the conference has started. The URL may return an MP3 file, a WAV file, or a TwiML document. The default value is the URL of our standard hold music. If you do not want anything to play while waiting for the conference to start, specify an empty string by setting `wait_url` to `''`. For more details on the allowable verbs within the `waitUrl`, see the `waitUrl` attribute in the [<Conference> TwiML instruction](https://www.twilio.com/docs/voice/twiml/conference#attributes-waiturl).
     * @param string $waitMethod The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
     * @param bool $earlyMedia Whether to allow an agent to hear the state of the outbound call, including ringing or disconnect messages. Can be: `true` or `false` and defaults to `true`.
     * @param int $maxParticipants The maximum number of participants in the conference. Can be a positive integer from `2` to `250`. The default value is `250`.
     * @param string $conferenceRecord Whether to record the conference the participant is joining. Can be: `true`, `false`, `record-from-start`, and `do-not-record`. The default value is `false`.
     * @param string $conferenceTrim Whether to trim leading and trailing silence from the conference recording. Can be: `trim-silence` or `do-not-trim` and defaults to `trim-silence`.
     * @param string $conferenceStatusCallback The URL we should call using the `conference_status_callback_method` when the conference events in `conference_status_callback_event` occur. Only the value set by the first participant to join the conference is used. Subsequent `conference_status_callback` values are ignored.
     * @param string $conferenceStatusCallbackMethod The HTTP method we should use to call `conference_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     * @param string[] $conferenceStatusCallbackEvent The conference state changes that should generate a call to `conference_status_callback`. Can be: `start`, `end`, `join`, `leave`, `mute`, `hold`, `modify`, `speaker`, and `announcement`. Separate multiple values with a space. Defaults to `start end`.
     * @param string $recordingChannels The recording channels for the final recording. Can be: `mono` or `dual` and the default is `mono`.
     * @param string $recordingStatusCallback The URL that we should call using the `recording_status_callback_method` when the recording status changes.
     * @param string $recordingStatusCallbackMethod The HTTP method we should use when we call `recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     * @param string $sipAuthUsername The SIP username used for authentication.
     * @param string $sipAuthPassword The SIP password for authentication.
     * @param string $region The [region](https://support.twilio.com/hc/en-us/articles/*********-How-global-low-latency-routing-and-region-selection-work-for-conferences-and-Client-calls) where we should mix the recorded audio. Can be:`us1`, `us2`, `ie1`, `de1`, `sg1`, `br1`, `au1`, or `jp1`.
     * @param string $conferenceRecordingStatusCallback The URL we should call using the `conference_recording_status_callback_method` when the conference recording is available.
     * @param string $conferenceRecordingStatusCallbackMethod The HTTP method we should use to call `conference_recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     * @param string[] $recordingStatusCallbackEvent The recording state changes that should generate a call to `recording_status_callback`. Can be: `started`, `in-progress`, `paused`, `resumed`, `stopped`, `completed`, `failed`, and `absent`. Separate multiple values with a space, ex: `'in-progress completed failed'`.
     * @param string[] $conferenceRecordingStatusCallbackEvent The conference recording state changes that generate a call to `conference_recording_status_callback`. Can be: `in-progress`, `completed`, `failed`, and `absent`. Separate multiple values with a space, ex: `'in-progress completed failed'`
     * @param bool $coaching Whether the participant is coaching another call. Can be: `true` or `false`. If not present, defaults to `false` unless `call_sid_to_coach` is defined. If `true`, `call_sid_to_coach` must be defined.
     * @param string $callSidToCoach The SID of the participant who is being `coached`. The participant being coached is the only participant who can hear the participant who is `coaching`.
     * @param string $jitterBufferSize Jitter buffer size for the connecting participant. Twilio will use this setting to apply Jitter Buffer before participant's audio is mixed into the conference. Can be: `off`, `small`, `medium`, and `large`. Default to `large`.
     * @param string $byoc The SID of a BYOC (Bring Your Own Carrier) trunk to route this call with. Note that `byoc` is only meaningful when `to` is a phone number; it will otherwise be ignored. (Beta)
     * @param string $callerId The phone number, Client identifier, or username portion of SIP address that made this call. Phone numbers are in [E.164](https://www.twilio.com/docs/glossary/what-e164) format (e.g., +***********). Client identifiers are formatted `client:name`. If using a phone number, it must be a Twilio number or a Verified [outgoing caller id](https://www.twilio.com/docs/voice/api/outgoing-caller-ids) for your account. If the `to` parameter is a phone number, `callerId` must also be a phone number. If `to` is sip address, this value of `callerId` should be a username portion to be used to populate the From header that is passed to the SIP endpoint.
     * @param string $callReason The Reason for the outgoing call. Use it to specify the purpose of the call that is presented on the called party's phone. (Branded Calls Beta)
     * @param string $recordingTrack The audio track to record for the call. Can be: `inbound`, `outbound` or `both`. The default is `both`. `inbound` records the audio that is received by Twilio. `outbound` records the audio that is sent from Twilio. `both` records the audio that is received and sent by Twilio.
     * @param int $timeLimit The maximum duration of the call in seconds. Constraints depend on account and configuration.
     * @param string $machineDetection Whether to detect if a human, answering machine, or fax has picked up the call. Can be: `Enable` or `DetectMessageEnd`. Use `Enable` if you would like us to return `AnsweredBy` as soon as the called party is identified. Use `DetectMessageEnd`, if you would like to leave a message on an answering machine. For more information, see [Answering Machine Detection](https://www.twilio.com/docs/voice/answering-machine-detection).
     * @param int $machineDetectionTimeout The number of seconds that we should attempt to detect an answering machine before timing out and sending a voice request with `AnsweredBy` of `unknown`. The default timeout is 30 seconds.
     * @param int $machineDetectionSpeechThreshold The number of milliseconds that is used as the measuring stick for the length of the speech activity, where durations lower than this value will be interpreted as a human and longer than this value as a machine. Possible Values: 1000-6000. Default: 2400.
     * @param int $machineDetectionSpeechEndThreshold The number of milliseconds of silence after speech activity at which point the speech activity is considered complete. Possible Values: 500-5000. Default: 1200.
     * @param int $machineDetectionSilenceTimeout The number of milliseconds of initial silence after which an `unknown` AnsweredBy result will be returned. Possible Values: 2000-10000. Default: 5000.
     * @param string $amdStatusCallback The URL that we should call using the `amd_status_callback_method` to notify customer application whether the call was answered by human, machine or fax.
     * @param string $amdStatusCallbackMethod The HTTP method we should use when calling the `amd_status_callback` URL. Can be: `GET` or `POST` and the default is `POST`.
     * @param string $trim Whether to trim any leading and trailing silence from the participant recording. Can be: `trim-silence` or `do-not-trim` and the default is `trim-silence`.
     * @param string $callToken A token string needed to invoke a forwarded call. A call_token is generated when an incoming call is received on a Twilio number. Pass an incoming call's call_token value to a forwarded call via the call_token parameter when creating a new call. A forwarded call should bear the same CallerID of the original incoming call.
     * @return CreateParticipantOptions Options builder
     */
    public static function create(
        
        string $statusCallback = Values::NONE,
        string $statusCallbackMethod = Values::NONE,
        array $statusCallbackEvent = Values::ARRAY_NONE,
        string $label = Values::NONE,
        int $timeout = Values::INT_NONE,
        bool $record = Values::BOOL_NONE,
        bool $muted = Values::BOOL_NONE,
        string $beep = Values::NONE,
        bool $startConferenceOnEnter = Values::BOOL_NONE,
        bool $endConferenceOnExit = Values::BOOL_NONE,
        string $waitUrl = Values::NONE,
        string $waitMethod = Values::NONE,
        bool $earlyMedia = Values::BOOL_NONE,
        int $maxParticipants = Values::INT_NONE,
        string $conferenceRecord = Values::NONE,
        string $conferenceTrim = Values::NONE,
        string $conferenceStatusCallback = Values::NONE,
        string $conferenceStatusCallbackMethod = Values::NONE,
        array $conferenceStatusCallbackEvent = Values::ARRAY_NONE,
        string $recordingChannels = Values::NONE,
        string $recordingStatusCallback = Values::NONE,
        string $recordingStatusCallbackMethod = Values::NONE,
        string $sipAuthUsername = Values::NONE,
        string $sipAuthPassword = Values::NONE,
        string $region = Values::NONE,
        string $conferenceRecordingStatusCallback = Values::NONE,
        string $conferenceRecordingStatusCallbackMethod = Values::NONE,
        array $recordingStatusCallbackEvent = Values::ARRAY_NONE,
        array $conferenceRecordingStatusCallbackEvent = Values::ARRAY_NONE,
        bool $coaching = Values::BOOL_NONE,
        string $callSidToCoach = Values::NONE,
        string $jitterBufferSize = Values::NONE,
        string $byoc = Values::NONE,
        string $callerId = Values::NONE,
        string $callReason = Values::NONE,
        string $recordingTrack = Values::NONE,
        int $timeLimit = Values::INT_NONE,
        string $machineDetection = Values::NONE,
        int $machineDetectionTimeout = Values::INT_NONE,
        int $machineDetectionSpeechThreshold = Values::INT_NONE,
        int $machineDetectionSpeechEndThreshold = Values::INT_NONE,
        int $machineDetectionSilenceTimeout = Values::INT_NONE,
        string $amdStatusCallback = Values::NONE,
        string $amdStatusCallbackMethod = Values::NONE,
        string $trim = Values::NONE,
        string $callToken = Values::NONE

    ): CreateParticipantOptions
    {
        return new CreateParticipantOptions(
            $statusCallback,
            $statusCallbackMethod,
            $statusCallbackEvent,
            $label,
            $timeout,
            $record,
            $muted,
            $beep,
            $startConferenceOnEnter,
            $endConferenceOnExit,
            $waitUrl,
            $waitMethod,
            $earlyMedia,
            $maxParticipants,
            $conferenceRecord,
            $conferenceTrim,
            $conferenceStatusCallback,
            $conferenceStatusCallbackMethod,
            $conferenceStatusCallbackEvent,
            $recordingChannels,
            $recordingStatusCallback,
            $recordingStatusCallbackMethod,
            $sipAuthUsername,
            $sipAuthPassword,
            $region,
            $conferenceRecordingStatusCallback,
            $conferenceRecordingStatusCallbackMethod,
            $recordingStatusCallbackEvent,
            $conferenceRecordingStatusCallbackEvent,
            $coaching,
            $callSidToCoach,
            $jitterBufferSize,
            $byoc,
            $callerId,
            $callReason,
            $recordingTrack,
            $timeLimit,
            $machineDetection,
            $machineDetectionTimeout,
            $machineDetectionSpeechThreshold,
            $machineDetectionSpeechEndThreshold,
            $machineDetectionSilenceTimeout,
            $amdStatusCallback,
            $amdStatusCallbackMethod,
            $trim,
            $callToken
        );
    }



    /**
     * @param bool $muted Whether to return only participants that are muted. Can be: `true` or `false`.
     * @param bool $hold Whether to return only participants that are on hold. Can be: `true` or `false`.
     * @param bool $coaching Whether to return only participants who are coaching another call. Can be: `true` or `false`.
     * @return ReadParticipantOptions Options builder
     */
    public static function read(
        
        bool $muted = Values::BOOL_NONE,
        bool $hold = Values::BOOL_NONE,
        bool $coaching = Values::BOOL_NONE

    ): ReadParticipantOptions
    {
        return new ReadParticipantOptions(
            $muted,
            $hold,
            $coaching
        );
    }

    /**
     * @param bool $muted Whether the participant should be muted. Can be `true` or `false`. `true` will mute the participant, and `false` will un-mute them. Anything value other than `true` or `false` is interpreted as `false`.
     * @param bool $hold Whether the participant should be on hold. Can be: `true` or `false`. `true` puts the participant on hold, and `false` lets them rejoin the conference.
     * @param string $holdUrl The URL we call using the `hold_method` for music that plays when the participant is on hold. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
     * @param string $holdMethod The HTTP method we should use to call `hold_url`. Can be: `GET` or `POST` and the default is `GET`.
     * @param string $announceUrl The URL we call using the `announce_method` for an announcement to the participant. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
     * @param string $announceMethod The HTTP method we should use to call `announce_url`. Can be: `GET` or `POST` and defaults to `POST`.
     * @param string $waitUrl The URL that Twilio calls using the `wait_method` before the conference has started. The URL may return an MP3 file, a WAV file, or a TwiML document. The default value is the URL of our standard hold music. If you do not want anything to play while waiting for the conference to start, specify an empty string by setting `wait_url` to `''`. For more details on the allowable verbs within the `waitUrl`, see the `waitUrl` attribute in the [<Conference> TwiML instruction](https://www.twilio.com/docs/voice/twiml/conference#attributes-waiturl).
     * @param string $waitMethod The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
     * @param bool $beepOnExit Whether to play a notification beep to the conference when the participant exits. Can be: `true` or `false`.
     * @param bool $endConferenceOnExit Whether to end the conference when the participant leaves. Can be: `true` or `false` and defaults to `false`.
     * @param bool $coaching Whether the participant is coaching another call. Can be: `true` or `false`. If not present, defaults to `false` unless `call_sid_to_coach` is defined. If `true`, `call_sid_to_coach` must be defined.
     * @param string $callSidToCoach The SID of the participant who is being `coached`. The participant being coached is the only participant who can hear the participant who is `coaching`.
     * @return UpdateParticipantOptions Options builder
     */
    public static function update(
        
        bool $muted = Values::BOOL_NONE,
        bool $hold = Values::BOOL_NONE,
        string $holdUrl = Values::NONE,
        string $holdMethod = Values::NONE,
        string $announceUrl = Values::NONE,
        string $announceMethod = Values::NONE,
        string $waitUrl = Values::NONE,
        string $waitMethod = Values::NONE,
        bool $beepOnExit = Values::BOOL_NONE,
        bool $endConferenceOnExit = Values::BOOL_NONE,
        bool $coaching = Values::BOOL_NONE,
        string $callSidToCoach = Values::NONE

    ): UpdateParticipantOptions
    {
        return new UpdateParticipantOptions(
            $muted,
            $hold,
            $holdUrl,
            $holdMethod,
            $announceUrl,
            $announceMethod,
            $waitUrl,
            $waitMethod,
            $beepOnExit,
            $endConferenceOnExit,
            $coaching,
            $callSidToCoach
        );
    }

}

class CreateParticipantOptions extends Options
    {
    /**
     * @param string $statusCallback The URL we should call using the `status_callback_method` to send status information to your application.
     * @param string $statusCallbackMethod The HTTP method we should use to call `status_callback`. Can be: `GET` and `POST` and defaults to `POST`.
     * @param string[] $statusCallbackEvent The conference state changes that should generate a call to `status_callback`. Can be: `initiated`, `ringing`, `answered`, and `completed`. Separate multiple values with a space. The default value is `completed`.
     * @param string $label A label for this participant. If one is supplied, it may subsequently be used to fetch, update or delete the participant.
     * @param int $timeout The number of seconds that we should allow the phone to ring before assuming there is no answer. Can be an integer between `5` and `600`, inclusive. The default value is `60`. We always add a 5-second timeout buffer to outgoing calls, so  value of 10 would result in an actual timeout that was closer to 15 seconds.
     * @param bool $record Whether to record the participant and their conferences, including the time between conferences. Can be `true` or `false` and the default is `false`.
     * @param bool $muted Whether the agent is muted in the conference. Can be `true` or `false` and the default is `false`.
     * @param string $beep Whether to play a notification beep to the conference when the participant joins. Can be: `true`, `false`, `onEnter`, or `onExit`. The default value is `true`.
     * @param bool $startConferenceOnEnter Whether to start the conference when the participant joins, if it has not already started. Can be: `true` or `false` and the default is `true`. If `false` and the conference has not started, the participant is muted and hears background music until another participant starts the conference.
     * @param bool $endConferenceOnExit Whether to end the conference when the participant leaves. Can be: `true` or `false` and defaults to `false`.
     * @param string $waitUrl The URL that Twilio calls using the `wait_method` before the conference has started. The URL may return an MP3 file, a WAV file, or a TwiML document. The default value is the URL of our standard hold music. If you do not want anything to play while waiting for the conference to start, specify an empty string by setting `wait_url` to `''`. For more details on the allowable verbs within the `waitUrl`, see the `waitUrl` attribute in the [<Conference> TwiML instruction](https://www.twilio.com/docs/voice/twiml/conference#attributes-waiturl).
     * @param string $waitMethod The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
     * @param bool $earlyMedia Whether to allow an agent to hear the state of the outbound call, including ringing or disconnect messages. Can be: `true` or `false` and defaults to `true`.
     * @param int $maxParticipants The maximum number of participants in the conference. Can be a positive integer from `2` to `250`. The default value is `250`.
     * @param string $conferenceRecord Whether to record the conference the participant is joining. Can be: `true`, `false`, `record-from-start`, and `do-not-record`. The default value is `false`.
     * @param string $conferenceTrim Whether to trim leading and trailing silence from the conference recording. Can be: `trim-silence` or `do-not-trim` and defaults to `trim-silence`.
     * @param string $conferenceStatusCallback The URL we should call using the `conference_status_callback_method` when the conference events in `conference_status_callback_event` occur. Only the value set by the first participant to join the conference is used. Subsequent `conference_status_callback` values are ignored.
     * @param string $conferenceStatusCallbackMethod The HTTP method we should use to call `conference_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     * @param string[] $conferenceStatusCallbackEvent The conference state changes that should generate a call to `conference_status_callback`. Can be: `start`, `end`, `join`, `leave`, `mute`, `hold`, `modify`, `speaker`, and `announcement`. Separate multiple values with a space. Defaults to `start end`.
     * @param string $recordingChannels The recording channels for the final recording. Can be: `mono` or `dual` and the default is `mono`.
     * @param string $recordingStatusCallback The URL that we should call using the `recording_status_callback_method` when the recording status changes.
     * @param string $recordingStatusCallbackMethod The HTTP method we should use when we call `recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     * @param string $sipAuthUsername The SIP username used for authentication.
     * @param string $sipAuthPassword The SIP password for authentication.
     * @param string $region The [region](https://support.twilio.com/hc/en-us/articles/*********-How-global-low-latency-routing-and-region-selection-work-for-conferences-and-Client-calls) where we should mix the recorded audio. Can be:`us1`, `us2`, `ie1`, `de1`, `sg1`, `br1`, `au1`, or `jp1`.
     * @param string $conferenceRecordingStatusCallback The URL we should call using the `conference_recording_status_callback_method` when the conference recording is available.
     * @param string $conferenceRecordingStatusCallbackMethod The HTTP method we should use to call `conference_recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     * @param string[] $recordingStatusCallbackEvent The recording state changes that should generate a call to `recording_status_callback`. Can be: `started`, `in-progress`, `paused`, `resumed`, `stopped`, `completed`, `failed`, and `absent`. Separate multiple values with a space, ex: `'in-progress completed failed'`.
     * @param string[] $conferenceRecordingStatusCallbackEvent The conference recording state changes that generate a call to `conference_recording_status_callback`. Can be: `in-progress`, `completed`, `failed`, and `absent`. Separate multiple values with a space, ex: `'in-progress completed failed'`
     * @param bool $coaching Whether the participant is coaching another call. Can be: `true` or `false`. If not present, defaults to `false` unless `call_sid_to_coach` is defined. If `true`, `call_sid_to_coach` must be defined.
     * @param string $callSidToCoach The SID of the participant who is being `coached`. The participant being coached is the only participant who can hear the participant who is `coaching`.
     * @param string $jitterBufferSize Jitter buffer size for the connecting participant. Twilio will use this setting to apply Jitter Buffer before participant's audio is mixed into the conference. Can be: `off`, `small`, `medium`, and `large`. Default to `large`.
     * @param string $byoc The SID of a BYOC (Bring Your Own Carrier) trunk to route this call with. Note that `byoc` is only meaningful when `to` is a phone number; it will otherwise be ignored. (Beta)
     * @param string $callerId The phone number, Client identifier, or username portion of SIP address that made this call. Phone numbers are in [E.164](https://www.twilio.com/docs/glossary/what-e164) format (e.g., +***********). Client identifiers are formatted `client:name`. If using a phone number, it must be a Twilio number or a Verified [outgoing caller id](https://www.twilio.com/docs/voice/api/outgoing-caller-ids) for your account. If the `to` parameter is a phone number, `callerId` must also be a phone number. If `to` is sip address, this value of `callerId` should be a username portion to be used to populate the From header that is passed to the SIP endpoint.
     * @param string $callReason The Reason for the outgoing call. Use it to specify the purpose of the call that is presented on the called party's phone. (Branded Calls Beta)
     * @param string $recordingTrack The audio track to record for the call. Can be: `inbound`, `outbound` or `both`. The default is `both`. `inbound` records the audio that is received by Twilio. `outbound` records the audio that is sent from Twilio. `both` records the audio that is received and sent by Twilio.
     * @param int $timeLimit The maximum duration of the call in seconds. Constraints depend on account and configuration.
     * @param string $machineDetection Whether to detect if a human, answering machine, or fax has picked up the call. Can be: `Enable` or `DetectMessageEnd`. Use `Enable` if you would like us to return `AnsweredBy` as soon as the called party is identified. Use `DetectMessageEnd`, if you would like to leave a message on an answering machine. For more information, see [Answering Machine Detection](https://www.twilio.com/docs/voice/answering-machine-detection).
     * @param int $machineDetectionTimeout The number of seconds that we should attempt to detect an answering machine before timing out and sending a voice request with `AnsweredBy` of `unknown`. The default timeout is 30 seconds.
     * @param int $machineDetectionSpeechThreshold The number of milliseconds that is used as the measuring stick for the length of the speech activity, where durations lower than this value will be interpreted as a human and longer than this value as a machine. Possible Values: 1000-6000. Default: 2400.
     * @param int $machineDetectionSpeechEndThreshold The number of milliseconds of silence after speech activity at which point the speech activity is considered complete. Possible Values: 500-5000. Default: 1200.
     * @param int $machineDetectionSilenceTimeout The number of milliseconds of initial silence after which an `unknown` AnsweredBy result will be returned. Possible Values: 2000-10000. Default: 5000.
     * @param string $amdStatusCallback The URL that we should call using the `amd_status_callback_method` to notify customer application whether the call was answered by human, machine or fax.
     * @param string $amdStatusCallbackMethod The HTTP method we should use when calling the `amd_status_callback` URL. Can be: `GET` or `POST` and the default is `POST`.
     * @param string $trim Whether to trim any leading and trailing silence from the participant recording. Can be: `trim-silence` or `do-not-trim` and the default is `trim-silence`.
     * @param string $callToken A token string needed to invoke a forwarded call. A call_token is generated when an incoming call is received on a Twilio number. Pass an incoming call's call_token value to a forwarded call via the call_token parameter when creating a new call. A forwarded call should bear the same CallerID of the original incoming call.
     */
    public function __construct(
        
        string $statusCallback = Values::NONE,
        string $statusCallbackMethod = Values::NONE,
        array $statusCallbackEvent = Values::ARRAY_NONE,
        string $label = Values::NONE,
        int $timeout = Values::INT_NONE,
        bool $record = Values::BOOL_NONE,
        bool $muted = Values::BOOL_NONE,
        string $beep = Values::NONE,
        bool $startConferenceOnEnter = Values::BOOL_NONE,
        bool $endConferenceOnExit = Values::BOOL_NONE,
        string $waitUrl = Values::NONE,
        string $waitMethod = Values::NONE,
        bool $earlyMedia = Values::BOOL_NONE,
        int $maxParticipants = Values::INT_NONE,
        string $conferenceRecord = Values::NONE,
        string $conferenceTrim = Values::NONE,
        string $conferenceStatusCallback = Values::NONE,
        string $conferenceStatusCallbackMethod = Values::NONE,
        array $conferenceStatusCallbackEvent = Values::ARRAY_NONE,
        string $recordingChannels = Values::NONE,
        string $recordingStatusCallback = Values::NONE,
        string $recordingStatusCallbackMethod = Values::NONE,
        string $sipAuthUsername = Values::NONE,
        string $sipAuthPassword = Values::NONE,
        string $region = Values::NONE,
        string $conferenceRecordingStatusCallback = Values::NONE,
        string $conferenceRecordingStatusCallbackMethod = Values::NONE,
        array $recordingStatusCallbackEvent = Values::ARRAY_NONE,
        array $conferenceRecordingStatusCallbackEvent = Values::ARRAY_NONE,
        bool $coaching = Values::BOOL_NONE,
        string $callSidToCoach = Values::NONE,
        string $jitterBufferSize = Values::NONE,
        string $byoc = Values::NONE,
        string $callerId = Values::NONE,
        string $callReason = Values::NONE,
        string $recordingTrack = Values::NONE,
        int $timeLimit = Values::INT_NONE,
        string $machineDetection = Values::NONE,
        int $machineDetectionTimeout = Values::INT_NONE,
        int $machineDetectionSpeechThreshold = Values::INT_NONE,
        int $machineDetectionSpeechEndThreshold = Values::INT_NONE,
        int $machineDetectionSilenceTimeout = Values::INT_NONE,
        string $amdStatusCallback = Values::NONE,
        string $amdStatusCallbackMethod = Values::NONE,
        string $trim = Values::NONE,
        string $callToken = Values::NONE

    ) {
        $this->options['statusCallback'] = $statusCallback;
        $this->options['statusCallbackMethod'] = $statusCallbackMethod;
        $this->options['statusCallbackEvent'] = $statusCallbackEvent;
        $this->options['label'] = $label;
        $this->options['timeout'] = $timeout;
        $this->options['record'] = $record;
        $this->options['muted'] = $muted;
        $this->options['beep'] = $beep;
        $this->options['startConferenceOnEnter'] = $startConferenceOnEnter;
        $this->options['endConferenceOnExit'] = $endConferenceOnExit;
        $this->options['waitUrl'] = $waitUrl;
        $this->options['waitMethod'] = $waitMethod;
        $this->options['earlyMedia'] = $earlyMedia;
        $this->options['maxParticipants'] = $maxParticipants;
        $this->options['conferenceRecord'] = $conferenceRecord;
        $this->options['conferenceTrim'] = $conferenceTrim;
        $this->options['conferenceStatusCallback'] = $conferenceStatusCallback;
        $this->options['conferenceStatusCallbackMethod'] = $conferenceStatusCallbackMethod;
        $this->options['conferenceStatusCallbackEvent'] = $conferenceStatusCallbackEvent;
        $this->options['recordingChannels'] = $recordingChannels;
        $this->options['recordingStatusCallback'] = $recordingStatusCallback;
        $this->options['recordingStatusCallbackMethod'] = $recordingStatusCallbackMethod;
        $this->options['sipAuthUsername'] = $sipAuthUsername;
        $this->options['sipAuthPassword'] = $sipAuthPassword;
        $this->options['region'] = $region;
        $this->options['conferenceRecordingStatusCallback'] = $conferenceRecordingStatusCallback;
        $this->options['conferenceRecordingStatusCallbackMethod'] = $conferenceRecordingStatusCallbackMethod;
        $this->options['recordingStatusCallbackEvent'] = $recordingStatusCallbackEvent;
        $this->options['conferenceRecordingStatusCallbackEvent'] = $conferenceRecordingStatusCallbackEvent;
        $this->options['coaching'] = $coaching;
        $this->options['callSidToCoach'] = $callSidToCoach;
        $this->options['jitterBufferSize'] = $jitterBufferSize;
        $this->options['byoc'] = $byoc;
        $this->options['callerId'] = $callerId;
        $this->options['callReason'] = $callReason;
        $this->options['recordingTrack'] = $recordingTrack;
        $this->options['timeLimit'] = $timeLimit;
        $this->options['machineDetection'] = $machineDetection;
        $this->options['machineDetectionTimeout'] = $machineDetectionTimeout;
        $this->options['machineDetectionSpeechThreshold'] = $machineDetectionSpeechThreshold;
        $this->options['machineDetectionSpeechEndThreshold'] = $machineDetectionSpeechEndThreshold;
        $this->options['machineDetectionSilenceTimeout'] = $machineDetectionSilenceTimeout;
        $this->options['amdStatusCallback'] = $amdStatusCallback;
        $this->options['amdStatusCallbackMethod'] = $amdStatusCallbackMethod;
        $this->options['trim'] = $trim;
        $this->options['callToken'] = $callToken;
    }

    /**
     * The URL we should call using the `status_callback_method` to send status information to your application.
     *
     * @param string $statusCallback The URL we should call using the `status_callback_method` to send status information to your application.
     * @return $this Fluent Builder
     */
    public function setStatusCallback(string $statusCallback): self
    {
        $this->options['statusCallback'] = $statusCallback;
        return $this;
    }

    /**
     * The HTTP method we should use to call `status_callback`. Can be: `GET` and `POST` and defaults to `POST`.
     *
     * @param string $statusCallbackMethod The HTTP method we should use to call `status_callback`. Can be: `GET` and `POST` and defaults to `POST`.
     * @return $this Fluent Builder
     */
    public function setStatusCallbackMethod(string $statusCallbackMethod): self
    {
        $this->options['statusCallbackMethod'] = $statusCallbackMethod;
        return $this;
    }

    /**
     * The conference state changes that should generate a call to `status_callback`. Can be: `initiated`, `ringing`, `answered`, and `completed`. Separate multiple values with a space. The default value is `completed`.
     *
     * @param string[] $statusCallbackEvent The conference state changes that should generate a call to `status_callback`. Can be: `initiated`, `ringing`, `answered`, and `completed`. Separate multiple values with a space. The default value is `completed`.
     * @return $this Fluent Builder
     */
    public function setStatusCallbackEvent(array $statusCallbackEvent): self
    {
        $this->options['statusCallbackEvent'] = $statusCallbackEvent;
        return $this;
    }

    /**
     * A label for this participant. If one is supplied, it may subsequently be used to fetch, update or delete the participant.
     *
     * @param string $label A label for this participant. If one is supplied, it may subsequently be used to fetch, update or delete the participant.
     * @return $this Fluent Builder
     */
    public function setLabel(string $label): self
    {
        $this->options['label'] = $label;
        return $this;
    }

    /**
     * The number of seconds that we should allow the phone to ring before assuming there is no answer. Can be an integer between `5` and `600`, inclusive. The default value is `60`. We always add a 5-second timeout buffer to outgoing calls, so  value of 10 would result in an actual timeout that was closer to 15 seconds.
     *
     * @param int $timeout The number of seconds that we should allow the phone to ring before assuming there is no answer. Can be an integer between `5` and `600`, inclusive. The default value is `60`. We always add a 5-second timeout buffer to outgoing calls, so  value of 10 would result in an actual timeout that was closer to 15 seconds.
     * @return $this Fluent Builder
     */
    public function setTimeout(int $timeout): self
    {
        $this->options['timeout'] = $timeout;
        return $this;
    }

    /**
     * Whether to record the participant and their conferences, including the time between conferences. Can be `true` or `false` and the default is `false`.
     *
     * @param bool $record Whether to record the participant and their conferences, including the time between conferences. Can be `true` or `false` and the default is `false`.
     * @return $this Fluent Builder
     */
    public function setRecord(bool $record): self
    {
        $this->options['record'] = $record;
        return $this;
    }

    /**
     * Whether the agent is muted in the conference. Can be `true` or `false` and the default is `false`.
     *
     * @param bool $muted Whether the agent is muted in the conference. Can be `true` or `false` and the default is `false`.
     * @return $this Fluent Builder
     */
    public function setMuted(bool $muted): self
    {
        $this->options['muted'] = $muted;
        return $this;
    }

    /**
     * Whether to play a notification beep to the conference when the participant joins. Can be: `true`, `false`, `onEnter`, or `onExit`. The default value is `true`.
     *
     * @param string $beep Whether to play a notification beep to the conference when the participant joins. Can be: `true`, `false`, `onEnter`, or `onExit`. The default value is `true`.
     * @return $this Fluent Builder
     */
    public function setBeep(string $beep): self
    {
        $this->options['beep'] = $beep;
        return $this;
    }

    /**
     * Whether to start the conference when the participant joins, if it has not already started. Can be: `true` or `false` and the default is `true`. If `false` and the conference has not started, the participant is muted and hears background music until another participant starts the conference.
     *
     * @param bool $startConferenceOnEnter Whether to start the conference when the participant joins, if it has not already started. Can be: `true` or `false` and the default is `true`. If `false` and the conference has not started, the participant is muted and hears background music until another participant starts the conference.
     * @return $this Fluent Builder
     */
    public function setStartConferenceOnEnter(bool $startConferenceOnEnter): self
    {
        $this->options['startConferenceOnEnter'] = $startConferenceOnEnter;
        return $this;
    }

    /**
     * Whether to end the conference when the participant leaves. Can be: `true` or `false` and defaults to `false`.
     *
     * @param bool $endConferenceOnExit Whether to end the conference when the participant leaves. Can be: `true` or `false` and defaults to `false`.
     * @return $this Fluent Builder
     */
    public function setEndConferenceOnExit(bool $endConferenceOnExit): self
    {
        $this->options['endConferenceOnExit'] = $endConferenceOnExit;
        return $this;
    }

    /**
     * The URL that Twilio calls using the `wait_method` before the conference has started. The URL may return an MP3 file, a WAV file, or a TwiML document. The default value is the URL of our standard hold music. If you do not want anything to play while waiting for the conference to start, specify an empty string by setting `wait_url` to `''`. For more details on the allowable verbs within the `waitUrl`, see the `waitUrl` attribute in the [<Conference> TwiML instruction](https://www.twilio.com/docs/voice/twiml/conference#attributes-waiturl).
     *
     * @param string $waitUrl The URL that Twilio calls using the `wait_method` before the conference has started. The URL may return an MP3 file, a WAV file, or a TwiML document. The default value is the URL of our standard hold music. If you do not want anything to play while waiting for the conference to start, specify an empty string by setting `wait_url` to `''`. For more details on the allowable verbs within the `waitUrl`, see the `waitUrl` attribute in the [<Conference> TwiML instruction](https://www.twilio.com/docs/voice/twiml/conference#attributes-waiturl).
     * @return $this Fluent Builder
     */
    public function setWaitUrl(string $waitUrl): self
    {
        $this->options['waitUrl'] = $waitUrl;
        return $this;
    }

    /**
     * The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
     *
     * @param string $waitMethod The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
     * @return $this Fluent Builder
     */
    public function setWaitMethod(string $waitMethod): self
    {
        $this->options['waitMethod'] = $waitMethod;
        return $this;
    }

    /**
     * Whether to allow an agent to hear the state of the outbound call, including ringing or disconnect messages. Can be: `true` or `false` and defaults to `true`.
     *
     * @param bool $earlyMedia Whether to allow an agent to hear the state of the outbound call, including ringing or disconnect messages. Can be: `true` or `false` and defaults to `true`.
     * @return $this Fluent Builder
     */
    public function setEarlyMedia(bool $earlyMedia): self
    {
        $this->options['earlyMedia'] = $earlyMedia;
        return $this;
    }

    /**
     * The maximum number of participants in the conference. Can be a positive integer from `2` to `250`. The default value is `250`.
     *
     * @param int $maxParticipants The maximum number of participants in the conference. Can be a positive integer from `2` to `250`. The default value is `250`.
     * @return $this Fluent Builder
     */
    public function setMaxParticipants(int $maxParticipants): self
    {
        $this->options['maxParticipants'] = $maxParticipants;
        return $this;
    }

    /**
     * Whether to record the conference the participant is joining. Can be: `true`, `false`, `record-from-start`, and `do-not-record`. The default value is `false`.
     *
     * @param string $conferenceRecord Whether to record the conference the participant is joining. Can be: `true`, `false`, `record-from-start`, and `do-not-record`. The default value is `false`.
     * @return $this Fluent Builder
     */
    public function setConferenceRecord(string $conferenceRecord): self
    {
        $this->options['conferenceRecord'] = $conferenceRecord;
        return $this;
    }

    /**
     * Whether to trim leading and trailing silence from the conference recording. Can be: `trim-silence` or `do-not-trim` and defaults to `trim-silence`.
     *
     * @param string $conferenceTrim Whether to trim leading and trailing silence from the conference recording. Can be: `trim-silence` or `do-not-trim` and defaults to `trim-silence`.
     * @return $this Fluent Builder
     */
    public function setConferenceTrim(string $conferenceTrim): self
    {
        $this->options['conferenceTrim'] = $conferenceTrim;
        return $this;
    }

    /**
     * The URL we should call using the `conference_status_callback_method` when the conference events in `conference_status_callback_event` occur. Only the value set by the first participant to join the conference is used. Subsequent `conference_status_callback` values are ignored.
     *
     * @param string $conferenceStatusCallback The URL we should call using the `conference_status_callback_method` when the conference events in `conference_status_callback_event` occur. Only the value set by the first participant to join the conference is used. Subsequent `conference_status_callback` values are ignored.
     * @return $this Fluent Builder
     */
    public function setConferenceStatusCallback(string $conferenceStatusCallback): self
    {
        $this->options['conferenceStatusCallback'] = $conferenceStatusCallback;
        return $this;
    }

    /**
     * The HTTP method we should use to call `conference_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     *
     * @param string $conferenceStatusCallbackMethod The HTTP method we should use to call `conference_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     * @return $this Fluent Builder
     */
    public function setConferenceStatusCallbackMethod(string $conferenceStatusCallbackMethod): self
    {
        $this->options['conferenceStatusCallbackMethod'] = $conferenceStatusCallbackMethod;
        return $this;
    }

    /**
     * The conference state changes that should generate a call to `conference_status_callback`. Can be: `start`, `end`, `join`, `leave`, `mute`, `hold`, `modify`, `speaker`, and `announcement`. Separate multiple values with a space. Defaults to `start end`.
     *
     * @param string[] $conferenceStatusCallbackEvent The conference state changes that should generate a call to `conference_status_callback`. Can be: `start`, `end`, `join`, `leave`, `mute`, `hold`, `modify`, `speaker`, and `announcement`. Separate multiple values with a space. Defaults to `start end`.
     * @return $this Fluent Builder
     */
    public function setConferenceStatusCallbackEvent(array $conferenceStatusCallbackEvent): self
    {
        $this->options['conferenceStatusCallbackEvent'] = $conferenceStatusCallbackEvent;
        return $this;
    }

    /**
     * The recording channels for the final recording. Can be: `mono` or `dual` and the default is `mono`.
     *
     * @param string $recordingChannels The recording channels for the final recording. Can be: `mono` or `dual` and the default is `mono`.
     * @return $this Fluent Builder
     */
    public function setRecordingChannels(string $recordingChannels): self
    {
        $this->options['recordingChannels'] = $recordingChannels;
        return $this;
    }

    /**
     * The URL that we should call using the `recording_status_callback_method` when the recording status changes.
     *
     * @param string $recordingStatusCallback The URL that we should call using the `recording_status_callback_method` when the recording status changes.
     * @return $this Fluent Builder
     */
    public function setRecordingStatusCallback(string $recordingStatusCallback): self
    {
        $this->options['recordingStatusCallback'] = $recordingStatusCallback;
        return $this;
    }

    /**
     * The HTTP method we should use when we call `recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     *
     * @param string $recordingStatusCallbackMethod The HTTP method we should use when we call `recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     * @return $this Fluent Builder
     */
    public function setRecordingStatusCallbackMethod(string $recordingStatusCallbackMethod): self
    {
        $this->options['recordingStatusCallbackMethod'] = $recordingStatusCallbackMethod;
        return $this;
    }

    /**
     * The SIP username used for authentication.
     *
     * @param string $sipAuthUsername The SIP username used for authentication.
     * @return $this Fluent Builder
     */
    public function setSipAuthUsername(string $sipAuthUsername): self
    {
        $this->options['sipAuthUsername'] = $sipAuthUsername;
        return $this;
    }

    /**
     * The SIP password for authentication.
     *
     * @param string $sipAuthPassword The SIP password for authentication.
     * @return $this Fluent Builder
     */
    public function setSipAuthPassword(string $sipAuthPassword): self
    {
        $this->options['sipAuthPassword'] = $sipAuthPassword;
        return $this;
    }

    /**
     * The [region](https://support.twilio.com/hc/en-us/articles/*********-How-global-low-latency-routing-and-region-selection-work-for-conferences-and-Client-calls) where we should mix the recorded audio. Can be:`us1`, `us2`, `ie1`, `de1`, `sg1`, `br1`, `au1`, or `jp1`.
     *
     * @param string $region The [region](https://support.twilio.com/hc/en-us/articles/*********-How-global-low-latency-routing-and-region-selection-work-for-conferences-and-Client-calls) where we should mix the recorded audio. Can be:`us1`, `us2`, `ie1`, `de1`, `sg1`, `br1`, `au1`, or `jp1`.
     * @return $this Fluent Builder
     */
    public function setRegion(string $region): self
    {
        $this->options['region'] = $region;
        return $this;
    }

    /**
     * The URL we should call using the `conference_recording_status_callback_method` when the conference recording is available.
     *
     * @param string $conferenceRecordingStatusCallback The URL we should call using the `conference_recording_status_callback_method` when the conference recording is available.
     * @return $this Fluent Builder
     */
    public function setConferenceRecordingStatusCallback(string $conferenceRecordingStatusCallback): self
    {
        $this->options['conferenceRecordingStatusCallback'] = $conferenceRecordingStatusCallback;
        return $this;
    }

    /**
     * The HTTP method we should use to call `conference_recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     *
     * @param string $conferenceRecordingStatusCallbackMethod The HTTP method we should use to call `conference_recording_status_callback`. Can be: `GET` or `POST` and defaults to `POST`.
     * @return $this Fluent Builder
     */
    public function setConferenceRecordingStatusCallbackMethod(string $conferenceRecordingStatusCallbackMethod): self
    {
        $this->options['conferenceRecordingStatusCallbackMethod'] = $conferenceRecordingStatusCallbackMethod;
        return $this;
    }

    /**
     * The recording state changes that should generate a call to `recording_status_callback`. Can be: `started`, `in-progress`, `paused`, `resumed`, `stopped`, `completed`, `failed`, and `absent`. Separate multiple values with a space, ex: `'in-progress completed failed'`.
     *
     * @param string[] $recordingStatusCallbackEvent The recording state changes that should generate a call to `recording_status_callback`. Can be: `started`, `in-progress`, `paused`, `resumed`, `stopped`, `completed`, `failed`, and `absent`. Separate multiple values with a space, ex: `'in-progress completed failed'`.
     * @return $this Fluent Builder
     */
    public function setRecordingStatusCallbackEvent(array $recordingStatusCallbackEvent): self
    {
        $this->options['recordingStatusCallbackEvent'] = $recordingStatusCallbackEvent;
        return $this;
    }

    /**
     * The conference recording state changes that generate a call to `conference_recording_status_callback`. Can be: `in-progress`, `completed`, `failed`, and `absent`. Separate multiple values with a space, ex: `'in-progress completed failed'`
     *
     * @param string[] $conferenceRecordingStatusCallbackEvent The conference recording state changes that generate a call to `conference_recording_status_callback`. Can be: `in-progress`, `completed`, `failed`, and `absent`. Separate multiple values with a space, ex: `'in-progress completed failed'`
     * @return $this Fluent Builder
     */
    public function setConferenceRecordingStatusCallbackEvent(array $conferenceRecordingStatusCallbackEvent): self
    {
        $this->options['conferenceRecordingStatusCallbackEvent'] = $conferenceRecordingStatusCallbackEvent;
        return $this;
    }

    /**
     * Whether the participant is coaching another call. Can be: `true` or `false`. If not present, defaults to `false` unless `call_sid_to_coach` is defined. If `true`, `call_sid_to_coach` must be defined.
     *
     * @param bool $coaching Whether the participant is coaching another call. Can be: `true` or `false`. If not present, defaults to `false` unless `call_sid_to_coach` is defined. If `true`, `call_sid_to_coach` must be defined.
     * @return $this Fluent Builder
     */
    public function setCoaching(bool $coaching): self
    {
        $this->options['coaching'] = $coaching;
        return $this;
    }

    /**
     * The SID of the participant who is being `coached`. The participant being coached is the only participant who can hear the participant who is `coaching`.
     *
     * @param string $callSidToCoach The SID of the participant who is being `coached`. The participant being coached is the only participant who can hear the participant who is `coaching`.
     * @return $this Fluent Builder
     */
    public function setCallSidToCoach(string $callSidToCoach): self
    {
        $this->options['callSidToCoach'] = $callSidToCoach;
        return $this;
    }

    /**
     * Jitter buffer size for the connecting participant. Twilio will use this setting to apply Jitter Buffer before participant's audio is mixed into the conference. Can be: `off`, `small`, `medium`, and `large`. Default to `large`.
     *
     * @param string $jitterBufferSize Jitter buffer size for the connecting participant. Twilio will use this setting to apply Jitter Buffer before participant's audio is mixed into the conference. Can be: `off`, `small`, `medium`, and `large`. Default to `large`.
     * @return $this Fluent Builder
     */
    public function setJitterBufferSize(string $jitterBufferSize): self
    {
        $this->options['jitterBufferSize'] = $jitterBufferSize;
        return $this;
    }

    /**
     * The SID of a BYOC (Bring Your Own Carrier) trunk to route this call with. Note that `byoc` is only meaningful when `to` is a phone number; it will otherwise be ignored. (Beta)
     *
     * @param string $byoc The SID of a BYOC (Bring Your Own Carrier) trunk to route this call with. Note that `byoc` is only meaningful when `to` is a phone number; it will otherwise be ignored. (Beta)
     * @return $this Fluent Builder
     */
    public function setByoc(string $byoc): self
    {
        $this->options['byoc'] = $byoc;
        return $this;
    }

    /**
     * The phone number, Client identifier, or username portion of SIP address that made this call. Phone numbers are in [E.164](https://www.twilio.com/docs/glossary/what-e164) format (e.g., +***********). Client identifiers are formatted `client:name`. If using a phone number, it must be a Twilio number or a Verified [outgoing caller id](https://www.twilio.com/docs/voice/api/outgoing-caller-ids) for your account. If the `to` parameter is a phone number, `callerId` must also be a phone number. If `to` is sip address, this value of `callerId` should be a username portion to be used to populate the From header that is passed to the SIP endpoint.
     *
     * @param string $callerId The phone number, Client identifier, or username portion of SIP address that made this call. Phone numbers are in [E.164](https://www.twilio.com/docs/glossary/what-e164) format (e.g., +***********). Client identifiers are formatted `client:name`. If using a phone number, it must be a Twilio number or a Verified [outgoing caller id](https://www.twilio.com/docs/voice/api/outgoing-caller-ids) for your account. If the `to` parameter is a phone number, `callerId` must also be a phone number. If `to` is sip address, this value of `callerId` should be a username portion to be used to populate the From header that is passed to the SIP endpoint.
     * @return $this Fluent Builder
     */
    public function setCallerId(string $callerId): self
    {
        $this->options['callerId'] = $callerId;
        return $this;
    }

    /**
     * The Reason for the outgoing call. Use it to specify the purpose of the call that is presented on the called party's phone. (Branded Calls Beta)
     *
     * @param string $callReason The Reason for the outgoing call. Use it to specify the purpose of the call that is presented on the called party's phone. (Branded Calls Beta)
     * @return $this Fluent Builder
     */
    public function setCallReason(string $callReason): self
    {
        $this->options['callReason'] = $callReason;
        return $this;
    }

    /**
     * The audio track to record for the call. Can be: `inbound`, `outbound` or `both`. The default is `both`. `inbound` records the audio that is received by Twilio. `outbound` records the audio that is sent from Twilio. `both` records the audio that is received and sent by Twilio.
     *
     * @param string $recordingTrack The audio track to record for the call. Can be: `inbound`, `outbound` or `both`. The default is `both`. `inbound` records the audio that is received by Twilio. `outbound` records the audio that is sent from Twilio. `both` records the audio that is received and sent by Twilio.
     * @return $this Fluent Builder
     */
    public function setRecordingTrack(string $recordingTrack): self
    {
        $this->options['recordingTrack'] = $recordingTrack;
        return $this;
    }

    /**
     * The maximum duration of the call in seconds. Constraints depend on account and configuration.
     *
     * @param int $timeLimit The maximum duration of the call in seconds. Constraints depend on account and configuration.
     * @return $this Fluent Builder
     */
    public function setTimeLimit(int $timeLimit): self
    {
        $this->options['timeLimit'] = $timeLimit;
        return $this;
    }

    /**
     * Whether to detect if a human, answering machine, or fax has picked up the call. Can be: `Enable` or `DetectMessageEnd`. Use `Enable` if you would like us to return `AnsweredBy` as soon as the called party is identified. Use `DetectMessageEnd`, if you would like to leave a message on an answering machine. For more information, see [Answering Machine Detection](https://www.twilio.com/docs/voice/answering-machine-detection).
     *
     * @param string $machineDetection Whether to detect if a human, answering machine, or fax has picked up the call. Can be: `Enable` or `DetectMessageEnd`. Use `Enable` if you would like us to return `AnsweredBy` as soon as the called party is identified. Use `DetectMessageEnd`, if you would like to leave a message on an answering machine. For more information, see [Answering Machine Detection](https://www.twilio.com/docs/voice/answering-machine-detection).
     * @return $this Fluent Builder
     */
    public function setMachineDetection(string $machineDetection): self
    {
        $this->options['machineDetection'] = $machineDetection;
        return $this;
    }

    /**
     * The number of seconds that we should attempt to detect an answering machine before timing out and sending a voice request with `AnsweredBy` of `unknown`. The default timeout is 30 seconds.
     *
     * @param int $machineDetectionTimeout The number of seconds that we should attempt to detect an answering machine before timing out and sending a voice request with `AnsweredBy` of `unknown`. The default timeout is 30 seconds.
     * @return $this Fluent Builder
     */
    public function setMachineDetectionTimeout(int $machineDetectionTimeout): self
    {
        $this->options['machineDetectionTimeout'] = $machineDetectionTimeout;
        return $this;
    }

    /**
     * The number of milliseconds that is used as the measuring stick for the length of the speech activity, where durations lower than this value will be interpreted as a human and longer than this value as a machine. Possible Values: 1000-6000. Default: 2400.
     *
     * @param int $machineDetectionSpeechThreshold The number of milliseconds that is used as the measuring stick for the length of the speech activity, where durations lower than this value will be interpreted as a human and longer than this value as a machine. Possible Values: 1000-6000. Default: 2400.
     * @return $this Fluent Builder
     */
    public function setMachineDetectionSpeechThreshold(int $machineDetectionSpeechThreshold): self
    {
        $this->options['machineDetectionSpeechThreshold'] = $machineDetectionSpeechThreshold;
        return $this;
    }

    /**
     * The number of milliseconds of silence after speech activity at which point the speech activity is considered complete. Possible Values: 500-5000. Default: 1200.
     *
     * @param int $machineDetectionSpeechEndThreshold The number of milliseconds of silence after speech activity at which point the speech activity is considered complete. Possible Values: 500-5000. Default: 1200.
     * @return $this Fluent Builder
     */
    public function setMachineDetectionSpeechEndThreshold(int $machineDetectionSpeechEndThreshold): self
    {
        $this->options['machineDetectionSpeechEndThreshold'] = $machineDetectionSpeechEndThreshold;
        return $this;
    }

    /**
     * The number of milliseconds of initial silence after which an `unknown` AnsweredBy result will be returned. Possible Values: 2000-10000. Default: 5000.
     *
     * @param int $machineDetectionSilenceTimeout The number of milliseconds of initial silence after which an `unknown` AnsweredBy result will be returned. Possible Values: 2000-10000. Default: 5000.
     * @return $this Fluent Builder
     */
    public function setMachineDetectionSilenceTimeout(int $machineDetectionSilenceTimeout): self
    {
        $this->options['machineDetectionSilenceTimeout'] = $machineDetectionSilenceTimeout;
        return $this;
    }

    /**
     * The URL that we should call using the `amd_status_callback_method` to notify customer application whether the call was answered by human, machine or fax.
     *
     * @param string $amdStatusCallback The URL that we should call using the `amd_status_callback_method` to notify customer application whether the call was answered by human, machine or fax.
     * @return $this Fluent Builder
     */
    public function setAmdStatusCallback(string $amdStatusCallback): self
    {
        $this->options['amdStatusCallback'] = $amdStatusCallback;
        return $this;
    }

    /**
     * The HTTP method we should use when calling the `amd_status_callback` URL. Can be: `GET` or `POST` and the default is `POST`.
     *
     * @param string $amdStatusCallbackMethod The HTTP method we should use when calling the `amd_status_callback` URL. Can be: `GET` or `POST` and the default is `POST`.
     * @return $this Fluent Builder
     */
    public function setAmdStatusCallbackMethod(string $amdStatusCallbackMethod): self
    {
        $this->options['amdStatusCallbackMethod'] = $amdStatusCallbackMethod;
        return $this;
    }

    /**
     * Whether to trim any leading and trailing silence from the participant recording. Can be: `trim-silence` or `do-not-trim` and the default is `trim-silence`.
     *
     * @param string $trim Whether to trim any leading and trailing silence from the participant recording. Can be: `trim-silence` or `do-not-trim` and the default is `trim-silence`.
     * @return $this Fluent Builder
     */
    public function setTrim(string $trim): self
    {
        $this->options['trim'] = $trim;
        return $this;
    }

    /**
     * A token string needed to invoke a forwarded call. A call_token is generated when an incoming call is received on a Twilio number. Pass an incoming call's call_token value to a forwarded call via the call_token parameter when creating a new call. A forwarded call should bear the same CallerID of the original incoming call.
     *
     * @param string $callToken A token string needed to invoke a forwarded call. A call_token is generated when an incoming call is received on a Twilio number. Pass an incoming call's call_token value to a forwarded call via the call_token parameter when creating a new call. A forwarded call should bear the same CallerID of the original incoming call.
     * @return $this Fluent Builder
     */
    public function setCallToken(string $callToken): self
    {
        $this->options['callToken'] = $callToken;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Api.V2010.CreateParticipantOptions ' . $options . ']';
    }
}



class ReadParticipantOptions extends Options
    {
    /**
     * @param bool $muted Whether to return only participants that are muted. Can be: `true` or `false`.
     * @param bool $hold Whether to return only participants that are on hold. Can be: `true` or `false`.
     * @param bool $coaching Whether to return only participants who are coaching another call. Can be: `true` or `false`.
     */
    public function __construct(
        
        bool $muted = Values::BOOL_NONE,
        bool $hold = Values::BOOL_NONE,
        bool $coaching = Values::BOOL_NONE

    ) {
        $this->options['muted'] = $muted;
        $this->options['hold'] = $hold;
        $this->options['coaching'] = $coaching;
    }

    /**
     * Whether to return only participants that are muted. Can be: `true` or `false`.
     *
     * @param bool $muted Whether to return only participants that are muted. Can be: `true` or `false`.
     * @return $this Fluent Builder
     */
    public function setMuted(bool $muted): self
    {
        $this->options['muted'] = $muted;
        return $this;
    }

    /**
     * Whether to return only participants that are on hold. Can be: `true` or `false`.
     *
     * @param bool $hold Whether to return only participants that are on hold. Can be: `true` or `false`.
     * @return $this Fluent Builder
     */
    public function setHold(bool $hold): self
    {
        $this->options['hold'] = $hold;
        return $this;
    }

    /**
     * Whether to return only participants who are coaching another call. Can be: `true` or `false`.
     *
     * @param bool $coaching Whether to return only participants who are coaching another call. Can be: `true` or `false`.
     * @return $this Fluent Builder
     */
    public function setCoaching(bool $coaching): self
    {
        $this->options['coaching'] = $coaching;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Api.V2010.ReadParticipantOptions ' . $options . ']';
    }
}

class UpdateParticipantOptions extends Options
    {
    /**
     * @param bool $muted Whether the participant should be muted. Can be `true` or `false`. `true` will mute the participant, and `false` will un-mute them. Anything value other than `true` or `false` is interpreted as `false`.
     * @param bool $hold Whether the participant should be on hold. Can be: `true` or `false`. `true` puts the participant on hold, and `false` lets them rejoin the conference.
     * @param string $holdUrl The URL we call using the `hold_method` for music that plays when the participant is on hold. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
     * @param string $holdMethod The HTTP method we should use to call `hold_url`. Can be: `GET` or `POST` and the default is `GET`.
     * @param string $announceUrl The URL we call using the `announce_method` for an announcement to the participant. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
     * @param string $announceMethod The HTTP method we should use to call `announce_url`. Can be: `GET` or `POST` and defaults to `POST`.
     * @param string $waitUrl The URL that Twilio calls using the `wait_method` before the conference has started. The URL may return an MP3 file, a WAV file, or a TwiML document. The default value is the URL of our standard hold music. If you do not want anything to play while waiting for the conference to start, specify an empty string by setting `wait_url` to `''`. For more details on the allowable verbs within the `waitUrl`, see the `waitUrl` attribute in the [<Conference> TwiML instruction](https://www.twilio.com/docs/voice/twiml/conference#attributes-waiturl).
     * @param string $waitMethod The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
     * @param bool $beepOnExit Whether to play a notification beep to the conference when the participant exits. Can be: `true` or `false`.
     * @param bool $endConferenceOnExit Whether to end the conference when the participant leaves. Can be: `true` or `false` and defaults to `false`.
     * @param bool $coaching Whether the participant is coaching another call. Can be: `true` or `false`. If not present, defaults to `false` unless `call_sid_to_coach` is defined. If `true`, `call_sid_to_coach` must be defined.
     * @param string $callSidToCoach The SID of the participant who is being `coached`. The participant being coached is the only participant who can hear the participant who is `coaching`.
     */
    public function __construct(
        
        bool $muted = Values::BOOL_NONE,
        bool $hold = Values::BOOL_NONE,
        string $holdUrl = Values::NONE,
        string $holdMethod = Values::NONE,
        string $announceUrl = Values::NONE,
        string $announceMethod = Values::NONE,
        string $waitUrl = Values::NONE,
        string $waitMethod = Values::NONE,
        bool $beepOnExit = Values::BOOL_NONE,
        bool $endConferenceOnExit = Values::BOOL_NONE,
        bool $coaching = Values::BOOL_NONE,
        string $callSidToCoach = Values::NONE

    ) {
        $this->options['muted'] = $muted;
        $this->options['hold'] = $hold;
        $this->options['holdUrl'] = $holdUrl;
        $this->options['holdMethod'] = $holdMethod;
        $this->options['announceUrl'] = $announceUrl;
        $this->options['announceMethod'] = $announceMethod;
        $this->options['waitUrl'] = $waitUrl;
        $this->options['waitMethod'] = $waitMethod;
        $this->options['beepOnExit'] = $beepOnExit;
        $this->options['endConferenceOnExit'] = $endConferenceOnExit;
        $this->options['coaching'] = $coaching;
        $this->options['callSidToCoach'] = $callSidToCoach;
    }

    /**
     * Whether the participant should be muted. Can be `true` or `false`. `true` will mute the participant, and `false` will un-mute them. Anything value other than `true` or `false` is interpreted as `false`.
     *
     * @param bool $muted Whether the participant should be muted. Can be `true` or `false`. `true` will mute the participant, and `false` will un-mute them. Anything value other than `true` or `false` is interpreted as `false`.
     * @return $this Fluent Builder
     */
    public function setMuted(bool $muted): self
    {
        $this->options['muted'] = $muted;
        return $this;
    }

    /**
     * Whether the participant should be on hold. Can be: `true` or `false`. `true` puts the participant on hold, and `false` lets them rejoin the conference.
     *
     * @param bool $hold Whether the participant should be on hold. Can be: `true` or `false`. `true` puts the participant on hold, and `false` lets them rejoin the conference.
     * @return $this Fluent Builder
     */
    public function setHold(bool $hold): self
    {
        $this->options['hold'] = $hold;
        return $this;
    }

    /**
     * The URL we call using the `hold_method` for music that plays when the participant is on hold. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
     *
     * @param string $holdUrl The URL we call using the `hold_method` for music that plays when the participant is on hold. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
     * @return $this Fluent Builder
     */
    public function setHoldUrl(string $holdUrl): self
    {
        $this->options['holdUrl'] = $holdUrl;
        return $this;
    }

    /**
     * The HTTP method we should use to call `hold_url`. Can be: `GET` or `POST` and the default is `GET`.
     *
     * @param string $holdMethod The HTTP method we should use to call `hold_url`. Can be: `GET` or `POST` and the default is `GET`.
     * @return $this Fluent Builder
     */
    public function setHoldMethod(string $holdMethod): self
    {
        $this->options['holdMethod'] = $holdMethod;
        return $this;
    }

    /**
     * The URL we call using the `announce_method` for an announcement to the participant. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
     *
     * @param string $announceUrl The URL we call using the `announce_method` for an announcement to the participant. The URL may return an MP3 file, a WAV file, or a TwiML document that contains `<Play>`, `<Say>`, `<Pause>`, or `<Redirect>` verbs.
     * @return $this Fluent Builder
     */
    public function setAnnounceUrl(string $announceUrl): self
    {
        $this->options['announceUrl'] = $announceUrl;
        return $this;
    }

    /**
     * The HTTP method we should use to call `announce_url`. Can be: `GET` or `POST` and defaults to `POST`.
     *
     * @param string $announceMethod The HTTP method we should use to call `announce_url`. Can be: `GET` or `POST` and defaults to `POST`.
     * @return $this Fluent Builder
     */
    public function setAnnounceMethod(string $announceMethod): self
    {
        $this->options['announceMethod'] = $announceMethod;
        return $this;
    }

    /**
     * The URL that Twilio calls using the `wait_method` before the conference has started. The URL may return an MP3 file, a WAV file, or a TwiML document. The default value is the URL of our standard hold music. If you do not want anything to play while waiting for the conference to start, specify an empty string by setting `wait_url` to `''`. For more details on the allowable verbs within the `waitUrl`, see the `waitUrl` attribute in the [<Conference> TwiML instruction](https://www.twilio.com/docs/voice/twiml/conference#attributes-waiturl).
     *
     * @param string $waitUrl The URL that Twilio calls using the `wait_method` before the conference has started. The URL may return an MP3 file, a WAV file, or a TwiML document. The default value is the URL of our standard hold music. If you do not want anything to play while waiting for the conference to start, specify an empty string by setting `wait_url` to `''`. For more details on the allowable verbs within the `waitUrl`, see the `waitUrl` attribute in the [<Conference> TwiML instruction](https://www.twilio.com/docs/voice/twiml/conference#attributes-waiturl).
     * @return $this Fluent Builder
     */
    public function setWaitUrl(string $waitUrl): self
    {
        $this->options['waitUrl'] = $waitUrl;
        return $this;
    }

    /**
     * The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
     *
     * @param string $waitMethod The HTTP method we should use to call `wait_url`. Can be `GET` or `POST` and the default is `POST`. When using a static audio file, this should be `GET` so that we can cache the file.
     * @return $this Fluent Builder
     */
    public function setWaitMethod(string $waitMethod): self
    {
        $this->options['waitMethod'] = $waitMethod;
        return $this;
    }

    /**
     * Whether to play a notification beep to the conference when the participant exits. Can be: `true` or `false`.
     *
     * @param bool $beepOnExit Whether to play a notification beep to the conference when the participant exits. Can be: `true` or `false`.
     * @return $this Fluent Builder
     */
    public function setBeepOnExit(bool $beepOnExit): self
    {
        $this->options['beepOnExit'] = $beepOnExit;
        return $this;
    }

    /**
     * Whether to end the conference when the participant leaves. Can be: `true` or `false` and defaults to `false`.
     *
     * @param bool $endConferenceOnExit Whether to end the conference when the participant leaves. Can be: `true` or `false` and defaults to `false`.
     * @return $this Fluent Builder
     */
    public function setEndConferenceOnExit(bool $endConferenceOnExit): self
    {
        $this->options['endConferenceOnExit'] = $endConferenceOnExit;
        return $this;
    }

    /**
     * Whether the participant is coaching another call. Can be: `true` or `false`. If not present, defaults to `false` unless `call_sid_to_coach` is defined. If `true`, `call_sid_to_coach` must be defined.
     *
     * @param bool $coaching Whether the participant is coaching another call. Can be: `true` or `false`. If not present, defaults to `false` unless `call_sid_to_coach` is defined. If `true`, `call_sid_to_coach` must be defined.
     * @return $this Fluent Builder
     */
    public function setCoaching(bool $coaching): self
    {
        $this->options['coaching'] = $coaching;
        return $this;
    }

    /**
     * The SID of the participant who is being `coached`. The participant being coached is the only participant who can hear the participant who is `coaching`.
     *
     * @param string $callSidToCoach The SID of the participant who is being `coached`. The participant being coached is the only participant who can hear the participant who is `coaching`.
     * @return $this Fluent Builder
     */
    public function setCallSidToCoach(string $callSidToCoach): self
    {
        $this->options['callSidToCoach'] = $callSidToCoach;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Api.V2010.UpdateParticipantOptions ' . $options . ']';
    }
}

