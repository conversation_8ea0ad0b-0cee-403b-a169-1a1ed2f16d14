<?php
?>

<div id="estModal" class="modal-backdrop">
    <div class="modal-content2" style="width: 1400px">
        <div class="modal-header">
            <span class="modal-close estModalClose">&#10006;</span>
            <h3>Estimate Job #<span id="jobId">{jobId}</span></h3>
        </div>
        <div class="modal-body">
            <div class="flex -mx-4">
                <div class="w-3/4 mx-4">
                    <h4 class="mb-3 font-bold">Estimate Detail</h4>
                    <div class="estimateDetail p-4 border-gray-200 border-1 rounded-md">
                        <div class="modal-row">
                            <label for="estNo" style="width: 150px">Estimate No:</label>
                            <input type="text" id="estNo" name="estNo"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                        </div>

                        <div class="modal-row">
                            <label for="estNo" style="width: 150px">PO#:</label>
                            <input type="text" id="estNo" name="estNo"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                        </div>

                        <div class="modal-row">
                            <label for="issueDate" style="width: 150px">Issue Date:</label>
                            <input type="date" id="issueDate"
                                   class="date-picker bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"
                            />
                        </div>
                        <div class="modal-row">
                            <label for="workStartDate" style="width: 150px">Work Start Date:</label>
                            <input type="date" id="workStartDate" name="workStartDate"
                                   class="date-picker bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                        </div>

                        <div class="modal-row">
                            <label for="estNo" style="width: 150px">Payment Terms:</label>
                            <input type="text" id="estNo" name="estNo"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                        </div>
                        <div class="modal-row">
                            <label for="estNo" style="width: 150px">Billing Address:</label>
                            <input type="text" id="estNo" name="estNo"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                            <input type="text" id="estNo" name="estNo"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                        </div>

                        <div class="modal-row">
                            <label for="billingPhone" style="width: 150px">Phone No:</label>
                            <input type="text" id="billingPhone" name="billingPhone"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                        </div>

                        <div class="modal-row">
                            <label for="billingEmail" style="width: 150px">Bill from Email:</label>
                            <input type="email" id="billingEmail" name="billingEmail"
                                   class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                        </div>

                        <hr class="my-4 text-gray-300"/>
                        <h4 class="mt-4 font-bold">Line Items</h4>
                        <table class="line-items w-full" cellspacing="0" cellpadding="0">
                            <thead>
                            <tr>
                                <th>
                                    <div style="width: 420px;text-align: left">Item</div>
                                </th>
                                <th>
                                    <div style="width: 100px;text-align: right">Quantity</div>
                                </th>
                                <th>
                                    <div style="width: 120px;text-align: right">Price</div>
                                </th>
                                <th>
                                    <div style="width: 60px;text-align: center">Taxable</div>
                                </th>
                                <th>
                                    <div style="width: 130px;text-align: right">Total</div>
                                </th>
                                <th>
                                    <div style="width: 40px;">&nbsp;</div>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        <button type="button" class="add-row py-1 px-4 my-2 bg-[#00796b] hover:bg-[#004d40] text-white rounded w-auto">Add a line
                            item
                        </button>

                        <hr class="my-3 text-gray-300"/>
                        <table class="table-total ml-auto border-collapse">
                            <tbody>
                            <tr>
                                <th class="uppercase text-left" width="120">Subtotal</th>
                                <td class="text-right" width="180">
                                    <div class="subtotal-wrap">0.00</div>
                                </td>
                            </tr>
                            <tr>
                                <th class="uppercase text-left" width="120">Discount</th>
                                <td class="text-right" width="180">
                                    <div class="discount-wrap">
                                        <input class="w-20 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th class="uppercase text-left pb-2" width="120">Tax</th>
                                <td class="text-right pb-2" width="180">
                                    <div class="tax-total-wrap">
                                        0.00
                                    </div>
                                </td>
                            </tr>
                            <tr class="border-t-1 border-gray-300">
                                <th class="uppercase text-left pt-2" width="120">Total</th>
                                <td class="text-right pt-2" width="180">
                                    <div class="grand-total-wrap">
                                        0.00
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="w-1/4 mx-4">
                    <h4 class="my-3 font-bold">Customer Detail</h4>
                    <div class="customerDetail p-4 border-gray-200 border-1 rounded-md">
                        <h5 class="name font-bold"></h5>
                        <div class="address my-3"></div>
                        <div class="phone my-3"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer flex justify-end items-start">
            <button type="button" class="btn-ok py-1 px-4 bg-[#00796b] hover:bg-[#004d40] text-white rounded w-auto mx-1">OK</button>
            <button type="button" class="btn-send py-1 px-4 bg-[#00796b] hover:bg-[#004d40] text-white rounded w-auto mx-1">Send</button>
            <button type="button" class="btn-cancel py-1 px-4 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded w-auto mx-1">Cancel</button>
        </div>
    </div>
</div>

<div style="display: none">
    <div id="est-trow-tpl">
        <table>
            <tbody>
            <tr>
                <td>
                    <div class="name-wrap" style="width: 420px;">
                        <input
                                class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"
                                name="name" type="text"/>
                    </div>
                </td>
                <td>
                    <div class="qty-wrap" style="width: 100px">
                        <input
                                class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"
                                name="qty" type="number"/>
                    </div>
                </td>
                <td>
                    <div class="price-wrap" style="width: 120px">
                        <input
                                class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"
                                name="price" type="number"/>
                    </div>
                </td>
                <td>
                    <div class="taxable-wrap" style="width: 60px">
                        <input class="w-full bg-gray-50 text-gray-900 text-sm rounded-sm h-6 align-middle"
                               name="taxable"
                               type="checkbox"/>
                    </div>
                </td>
                <td>
                    <div class="total-wrap text-right" style="width: 130px"></div>
                </td>
                <td class="text-center">
                    <div style="width: 40px"><span class="delete-row text-4 align-middle text-black hover:text-gray-900 cursor-pointer"
                                                   data-uid="uid_xx">&#10006;</span></div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</div>

<script>
  var Est = function () {
    return {
      init: function () {
        $('body').on('click', "div#estModal .modal-content2 .modal-footer .btn-ok", function (e) {
          const lineItems = estTable.getEstimateLineItems();
          console.log(lineItems);

          const customerId = estTable.job?.dispatch_customers?.[0]?.customer?.id;
          if (estTable.job) {
            Est.saveEstimateByJobId(estTable.jobId, {customer_id: customerId, dispatch_id: estTable.job.id, items: lineItems})
          }
        });


        $('body').on('click', 'div#estModal .line-items tbody td .delete-row', function (e) {
          console.log('[Delete Row]', e.currentTarget);
          estTable.deleteRow($(e.currentTarget).parents('tr').data('uid'));
        });
        $('body').on('click', 'div#estModal .add-row', function (e) {
          estTable.addRow();
        });

        // Here.
        // Autocomplete for service name inputs
        $('body').on('focus', 'div#estModal .line-items tbody td .name-wrap input', function () {
          $(this).autocomplete({
            source: function (request, response) {
              App.ajax(`${baseApiUrl}/tenant/pricebook/service/search`, {keyWords: request.term}, (data) => {
                response(data.rows.map(item => ({
                  ...item,
                  label: item.name,
                  value: item.name,
                  price: item.price,
                  taxable: item.taxable,
                })));
              })
            },
            minLength: 2,
            select: function (event, ui) {
              const $row = $(this).closest('tr');

              // Auto-fill price and taxable fields
              $row.attr('data-id', ui.item.id);
              $row.attr('data-service_id', ui.item.id);
              $row.find('.price-wrap input').val(sn(ui.item.price).toFixed(2));
              $row.find('.taxable-wrap input').prop('checked', ui.item.taxable);

              // Update total
              const qty = parseFloat($row.find('.qty-wrap input').val()) || 0;
              const price = parseFloat(ui.item.price) || 0;
              $row.find('.total-wrap').text((qty * price).toFixed(2));

              // Update data object
              const rowData = estTable.data.find(x => x.uid === $row.attr('data-uid'));
              if (rowData) {
                rowData.uid = ui.item.id;
                rowData.service_id = ui.item.id;
                rowData.name = ui.item.value;
                rowData.price = ui.item.price;
                rowData.taxable = ui.item.taxable;
                rowData.total = (qty * price).toFixed(2);
              }

              estTable.calculateTotalData();
            }
          });
        });

        // Update total when quantity or price changes
        $('body').on('change', 'div#estModal .line-items tbody td .qty-wrap input, div#estModal .line-items tbody td .price-wrap input', function () {
          const $row = $(this).closest('tr');
          const qty = parseFloat($row.find('.qty-wrap input').val()) || 0;
          const price = parseFloat($row.find('.price-wrap input').val()) || 0;
          $row.find('.total-wrap').text((qty * price).toFixed(2));

          estTable.calculateTotalData();
        });

        $('body').on('change', 'div#estModal .discount-wrap input, div#estModal .line-items tbody td .taxable-wrap input', function () {
          estTable.calculateTotalData();
        });
      },
      getFullAddress: function (customerAddr) {
        if (!customerAddr?.zip) return '';

        let str = `${customerAddr.streetNumber} ${customerAddr.streetName}`;
        if (customerAddr.streetDirection) {
          str += ` ${customerAddr.streetDirection}`;
        }

        if (customerAddr.unitType) {
          str += `, ${customerAddr.unitType}`;
        }
        if (customerAddr.unitNumber) {
          str += ` ${customerAddr.unitNumber}`;
        }
        if (customerAddr.city) {
          str += `, ${customerAddr.city}`;
        }
        if (customerAddr.state) {
          str += `, ${customerAddr.state}`;
        }
        if (customerAddr.zip) {
          str += ` ${customerAddr.zip}`;
        }

        return str;
      },

      loadEstimatesByJobId: function (jobId) {
        return App.ajaxPostOk(`${baseUrl}/tenant/estimates/getAll?jobId=${jobId}`, {format: 'json'}, (res) => {
          return res;
        }, {blockEle: estTable.estModal}, (err) => {
          console.error(err);
        });
      },
      saveEstimateByJobId: function (jobId, data) {
        App.ajaxPost(`${baseUrl}/tenant/estimates/create`, data, (res) => {
          App.success("Saved estimation successfully!");
        }, (err) => {
          console.error(err);
        })
      }
    }
  }();

  var estTable = function () {
    return {
      estList: [],
      data: [],
      estModal: null,
      tbody: null,
      customerId: null,
      job: null,

      init: async function (job) {
        this.job = job;
        this.estModal = $('#estModal');
        this.tbody = $('#estModal table.line-items tbody');
        this.tbody.html('');
        this.addRow();

        const customer = job.dispatch_customers?.[0]?.customer || {};
        const addr = customer.customer_properties?.[0] || {};
        const phone = customer.customer_phones?.[0] || {};

        this.estModal.find('#jobId').html(job.id);
        this.estModal.find(".customerDetail .name").html(customer.name);
        this.estModal.find(".customerDetail .address").html(Est.getFullAddress(addr));
        this.estModal.find(".customerDetail .phone").html(phone.phone);

        const res = await Est.loadEstimatesByJobId(job.id);
        this.estList = res.rows;
      },
      getEstimateLineItems: function () {
        const lineItems = [];

        // Iterate through each row in the table
        $('div#estModal .line-items tbody tr').each(function () {
          const $row = $(this);
          const uid = $row.attr('data-uid');
          const id = $row.attr('data-id');
          const serviceId = $row.attr('data-service_id');

          // Get values from all inputs in this row
          const name = $row.find('.name-wrap input').val();
          const qty = parseFloat($row.find('.qty-wrap input').val()) || 0;
          const price = parseFloat($row.find('.price-wrap input').val()) || 0;
          const taxable = $row.find('.taxable-wrap input').prop('checked');


          // Calculate total
          const total = qty * price;

          // Add to array
          const x = {
            uid: uid,
            id: id ? sn(id) : null,
            service_id: serviceId ? sn(serviceId) : null,
            name: name,
            qty: qty,
            price: price,
            taxable: taxable,
            total: total
          };

          lineItems.push(x);
        });

        return lineItems;
      },
      calculateTotalData: function () {
        const taxPercentage = 7;
        let subTotal = 0;
        let taxTotal = 0;
        $('div#estModal .line-items tbody tr').each(function () {
          const $row = $(this);
          const qty = parseFloat($row.find('.qty-wrap input').val()) || 0;
          const price = parseFloat($row.find('.price-wrap input').val()) || 0;
          const taxable = $row.find('.taxable-wrap input').prop('checked');
          if (taxable) {
            taxTotal += (qty * price * taxPercentage / 100);
          }
          subTotal += qty * price;
        });

        const discountTotal = parseFloat($('div#estModal .discount-wrap input').val()) || 0;

        let total = subTotal + taxTotal - discountTotal;

        $('div#estModal .subtotal-wrap').text(sn(subTotal).toFixed(2));
        $('div#estModal .tax-total-wrap').text(taxTotal.toFixed(2));
        $('div#estModal .grand-total-wrap').text(sn(total).toFixed(2));
      },
      addRow: function () {
        const newRow = {
          uid: `uid_${Date.now()}`,
          qty: 1,
        };
        this.data.push(newRow);
        this.tbody.append(this.genRowEle(newRow));
      },
      deleteRow: function (uid) {
        console.log(uid);
        this.data = this.data.filter(x => x.uid != uid);
        this.tbody.find(`tr[data-uid="${uid}"]`).remove();
        this.calculateTotalData();
      },
      genRowEle: function (x) {
        const $tr = $('<tr />');
        $tr.html($('#est-trow-tpl tr').html());

        $tr.attr('data-uid', x.uid || `uid_${Date.now()}`);
        $tr.attr('data-id', x.id || '');
        $tr.attr('data-service_id', x.service_id || '');

        $tr.find('td .name-wrap input').val(x.name || '');
        $tr.find('td .qty-wrap input').val(x.qty || 1);
        $tr.find('td .price-wrap input').val(sn(x.price).toFixed(2));
        $tr.find('td .taxable-wrap input').prop('checked', !!x.taxable);

        const qty = parseFloat(x.qty) || 0;
        const price = parseFloat(x.price) || 0;
        $tr.find('td .total-wrap').text((qty * price).toFixed(2));

        return $tr;
      },

      renderTableBody: function () {
        this.init();

        this.tbody.html('');
        this.data.forEach(x => {
          const $tr = this.genRowEle(x);
          this.tbody.append($tr);
        });
      }
    }
  }();

  $(document).ready(function () {
    Est.init();
  });
</script>
