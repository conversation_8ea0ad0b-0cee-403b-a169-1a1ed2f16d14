<?php
?>
<style>
    #estModal .billingDetail .modal-row {
        display: flex;
        align-items: center;
    }

    #estModal .billingDetail .modal-row.full input {
        flex: 1;
    }

    #estModal .modal-row label {
        display: inline-block;
        width: 120px;
        font-weight: normal;
    }
</style>
<div id="estModal" class="modal-backdrop">
    <div class="modal-content2" style="width: 1400px">
        <div class="modal-header">
            <span class="modal-close estModalClose">&#10006;</span>
            <h3>Estimate Job #<span id="jobId">{jobId}</span></h3>
        </div>
        <div class="modal-body">
            <div class="flex -mx-4">
                <div class="w-3/4 mx-4">
                    <h4 class="mb-3 font-bold">Estimate Detail</h4>
                    <div class="estimateDetail p-4 border-gray-200 border-1 rounded-md">
                        <div class="flex -mx-8">
                            <div class="modal-row px-8 w-1/2">
                                <label for="id">Estimate #:</label>
                                <input type="text" id="id" name="id" disabled
                                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                            </div>
                            <div class="modal-row  px-8 w-1/2">
                                <label for="issue_date">Issue Date:</label>
                                <input type="date" id="issue_date" name="issue_date"
                                       class="date-picker bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"
                                />
                            </div>
                        </div>
                        <div class="flex -mx-8">
                            <div class="modal-row px-8 w-1/2">
                                <label for="purchase_no">PO#:</label>
                                <input type="text" id="purchase_no" name="purchase_no" disabled
                                       class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                            </div>
                            <div class="modal-row  px-8 w-1/2">
                                <label for="work_start_date">Work Start Date:</label>
                                <input type="date" id="work_start_date" name="work_start_date"
                                       class="date-picker bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                            </div>
                        </div>
                        <div class="flex -mx-8">
                            <div class="modal-row w-1/2 px-8">
                                <label for="payment_terms">Payment Terms:</label>
                                <input type="text" id="payment_terms" name="payment_terms"
                                       class="w-60 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                            </div>
                            <div class="modal-row w-1/2 px-8">
                                <label for="tax_percent">Tax %:</label>
                                <input type="number" id="tax_percent" name="tax_percent"
                                       class="w-25 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                            </div>
                        </div>


                        <hr class="my-4 text-gray-300"/>
                        <h4 class="my-4 font-bold">Billing Detail</h4>
                        <div class="flex -mx-8 billingDetail">
                            <div class="w-1/2 px-8">
                                <div class="modal-row full">
                                    <label class="">Street:</label>
                                    <input type="text" name="billing_street_name" id="billing_street_name"
                                           class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                                </div>

                                <div class="modal-row">
                                    <label class="">City:</label>
                                    <input type="text" name="billing_city" id="billing_city"
                                           class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                                </div>
                                <div class="modal-row">
                                    <label class="">State:</label>
                                    <input type="text" name="billing_state" id="billing_state"
                                           class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                                </div>
                                <div class="modal-row">
                                    <label class="">Zip:</label>
                                    <input type="text" name="billing_zip" id="billing_zip"
                                           class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                                </div>
                            </div>
                            <div class="w-1/2 px-8">
                                <div class="modal-row full">
                                    <label for="billing_company" class="">Company:</label>
                                    <input type="text" name="billing_company" id="billing_company"
                                           class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                                </div>
                                <div class="modal-row">
                                    <label for="billing_name" class="">Name:</label>
                                    <input type="text" name="billing_name" id="billing_name"
                                           class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                                </div>
                                <div class="modal-row">
                                    <label for="billing_phone" class="">Phone:</label>
                                    <input type="text" name="billing_phone" id="billing_phone"
                                           class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                                </div>

                                <div class="modal-row full">
                                    <label for="billing_email" class="">Email:</label>
                                    <input type="text" name="billing_email" id="billing_email"
                                           class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                                </div>
                            </div>
                        </div>

                        <hr class="my-4 text-gray-300"/>
                        <h4 class="mt-4 font-bold">Line Items</h4>
                        <table class="line-items w-full" cellspacing="0" cellpadding="0">
                            <thead>
                            <tr>
                                <th>
                                    <div style="width: 420px;text-align: left">Item</div>
                                </th>
                                <th>
                                    <div style="width: 100px;text-align: right">Quantity</div>
                                </th>
                                <th>
                                    <div style="width: 120px;text-align: right">Price</div>
                                </th>
                                <th>
                                    <div style="width: 60px;text-align: center">Taxable</div>
                                </th>
                                <th>
                                    <div style="width: 130px;text-align: right">Total</div>
                                </th>
                                <th>
                                    <div style="width: 40px;">&nbsp;</div>
                                </th>
                            </tr>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                        <button type="button" class="add-row py-1 px-4 my-2 bg-[#00796b] hover:bg-[#004d40] text-white rounded w-auto">Add a line
                            item
                        </button>

                        <hr class="my-3 text-gray-300"/>
                        <table class="table-total ml-auto border-collapse">
                            <tbody>
                            <tr>
                                <th class="uppercase text-left" width="120">Subtotal</th>
                                <td class="text-right" width="180">
                                    <div class="subtotal-wrap">0.00</div>
                                </td>
                            </tr>
                            <tr>
                                <th class="uppercase text-left" width="120">Discount</th>
                                <td class="text-right" width="180">
                                    <div class="discount-wrap">
                                        <input type="number" id="discount_amount" name="discount_amount" value='0.00'
                                               class="w-20 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"/>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <th class="uppercase text-left pb-2" width="120">Tax</th>
                                <td class="text-right pb-2" width="180">
                                    <div class="tax-total-wrap">
                                        0.00
                                    </div>
                                </td>
                            </tr>
                            <tr class="border-t-1 border-gray-300">
                                <th class="uppercase text-left pt-2" width="120">Total</th>
                                <td class="text-right pt-2" width="180">
                                    <div class="grand-total-wrap">
                                        0.00
                                    </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="w-1/4 mx-4">
                    <h4 class="my-3 font-bold">Customer Detail</h4>
                    <div class="customerDetail p-4 border-gray-200 border-1 rounded-md">
                        <h5 class="name font-bold"></h5>
                        <div class="address my-3"></div>
                        <div class="phone my-3"></div>
                    </div>

                    <h4 class="my-3 font-bold">Notes</h4>
                    <textarea id="notes" name="notes"
                              class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"
                              rows="12"
                    ></textarea>
                </div>
            </div>
        </div>
        <div class="modal-footer flex justify-end items-start">
            <button type="button" class="btn-ok py-1 px-4 bg-[#00796b] hover:bg-[#004d40] text-white rounded w-auto mx-1">OK</button>
            <button type="button" class="btn-send py-1 px-4 bg-[#00796b] hover:bg-[#004d40] text-white rounded w-auto mx-1">PDF / Send</button>
            <button type="button" class="btn-cancel py-1 px-4 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded w-auto mx-1">Cancel</button>
        </div>
    </div>
</div>

<div style="display: none">
    <div id="est-trow-tpl">
        <table>
            <tbody>
            <tr>
                <td>
                    <div class="name-wrap" style="width: 420px;">
                        <input
                                class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"
                                name="name" type="text"/>
                    </div>
                </td>
                <td>
                    <div class="qty-wrap" style="width: 100px">
                        <input
                                class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"
                                name="qty" type="number"/>
                    </div>
                </td>
                <td>
                    <div class="price-wrap" style="width: 120px">
                        <input
                                class="w-full bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-sm focus:ring-blue-500 focus:border-blue-500 py-1 px-2"
                                name="price" type="number"/>
                    </div>
                </td>
                <td>
                    <div class="taxable-wrap" style="width: 60px">
                        <input class="w-full bg-gray-50 text-gray-900 text-sm rounded-sm h-6 align-middle"
                               name="taxable"
                               type="checkbox"/>
                    </div>
                </td>
                <td>
                    <div class="total-wrap text-right" style="width: 130px"></div>
                </td>
                <td class="text-center">
                    <div style="width: 40px"><span class="delete-row text-4 align-middle text-black hover:text-gray-900 cursor-pointer"
                                                   data-uid="uid_xx">&#10006;</span></div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</div>

<script>
  var Est = function () {
    return {
      tenantInfo: null,
      init: function () {
        // Get tenant info
        App.ajax(`${baseUrl}/tenant/getCurrentTenant`, {with: 'billingAddress'}, (res) => {
          this.tenantInfo = res.data;
          return res;
        });

        $('body').on('click', "div#estModal .modal-content2 .modal-footer .btn-ok", function (e) {
          const estFormValues = App.getFormData(estTable.estModal);
          estFormValues.items = estTable.getEstimateLineItems();
          console.log('form values', estFormValues, estFormValues.items);

          const customerId = estTable.job?.customer?.id;
          const addressId = estTable.job?.addresses?.[0]?.id;
          if (estTable.job) {
            return Est.saveEstimateByJobId(estTable.jobId, {
              ...estFormValues
              , customer_id: customerId
              , address_id: addressId
            });
          }
        });

        $('body').on('click', "div#estModal .modal-content2 .modal-footer .btn-send", function (e) {
          const estFormValues = App.getFormData(estTable.estModal);
          estFormValues.items = estTable.getEstimateLineItems();
          console.log('[PDF] Form values', estFormValues, estFormValues.items);

          const customerId = estTable.job?.customer?.id;
          const addressId = estTable.job?.addresses?.[0]?.id;
          if (estTable.job) {
            const params = {
              ...estFormValues
              , customer_id: customerId
              , address_id: addressId
              , dispatch_id: estTable.jobId
              , format: 'pdf'
            }
            App.ajaxPostOk(`${baseUrl}/tenant/estimates/createOrUpdate`, params, (res) => {
              App.success("Saved estimation successfully!");
              window.open(`${baseUrl}/${res.data.url}`, '_blank');
              return res;
            }, {blockEle: estTable.estModal}, (err) => {
              console.error(err);
            });
          }
        });

        $('body').on('click', 'div#estModal .line-items tbody td .delete-row', function (e) {
          estTable.deleteRow($(e.currentTarget).parents('tr').data('uid'));
        });
        $('body').on('click', 'div#estModal .add-row', function (e) {
          estTable.addRow();
        });

        // Autocomplete for service name inputs
        $('body').on('focus', 'div#estModal .line-items tbody td .name-wrap input', function () {
          $(this).autocomplete({
            source: function (request, response) {
              App.ajax(`${baseApiUrl}/tenant/pricebook/service/search`, {keyWords: request.term}, (data) => {
                response(data.rows.map(item => ({
                  ...item,
                  label: item.name,
                  value: item.name,
                  price: item.price,
                  taxable: item.taxable,
                })));
              })
            },
            minLength: 2,
            select: function (event, ui) {
              const $row = $(this).closest('tr');

              // Auto-fill price and taxable fields
              $row.attr('data-id', ui.item.id);
              $row.attr('data-service_id', ui.item.id);
              $row.find('.price-wrap input').val(sn(ui.item.price).toFixed(2));
              $row.find('.taxable-wrap input').prop('checked', ui.item.taxable);

              // Update total
              const qty = parseFloat($row.find('.qty-wrap input').val()) || 0;
              const price = parseFloat(ui.item.price) || 0;
              $row.find('.total-wrap').text((qty * price).toFixed(2));

              // Update data object
              const rowData = estTable.lineItems.find(x => x.uid === $row.attr('data-uid'));
              if (rowData) {
                rowData.uid = ui.item.id;
                rowData.service_id = ui.item.id;
                rowData.name = ui.item.value;
                rowData.price = ui.item.price;
                rowData.taxable = ui.item.taxable;
                rowData.total = (qty * price).toFixed(2);
              }

              estTable.calculateTotalData();
            }
          });
        });

        // Update total when quantity or price changes
        $('body').on('change', 'div#estModal .line-items tbody td .qty-wrap input, div#estModal .line-items tbody td .price-wrap input, div#estModal #tax_percent', function () {
          estTable.calculateTotalData();
        });

        $('body').on('change', 'div#estModal .discount-wrap input, div#estModal .line-items tbody td .taxable-wrap input', function () {
          estTable.calculateTotalData();
        });
        $('body').on('blur', 'div#estModal #notes', function () {
          if ($(this).val() != estTable.est.notes) {
            estTable.est.notes = $(this).val();
            Est.saveEstimateByJobId(estTable.job.id, {id: estTable.est.id, notes: estTable.est.notes}, {blockEle: $(this)})
          }
        });

      },
      getFullAddress: function (customerAddr) {
        if (!customerAddr?.zip) return '';

        let str = `${customerAddr.streetNumber} ${customerAddr.streetName}`;
        if (customerAddr.streetDirection) {
          str += ` ${customerAddr.streetDirection}`;
        }

        if (customerAddr.unitType) {
          str += `, ${customerAddr.unitType}`;
        }
        if (customerAddr.unitNumber) {
          str += ` ${customerAddr.unitNumber}`;
        }
        if (customerAddr.city) {
          str += `, ${customerAddr.city}`;
        }
        if (customerAddr.state) {
          str += `, ${customerAddr.state}`;
        }
        if (customerAddr.zip) {
          str += ` ${customerAddr.zip}`;
        }

        return str;
      },

      getDraftEstimate: function (jobId, defaultParams, opts) {
        const params = {
          ...defaultParams,
          in_status: [EstimateStatus.DRAFT],
          creation_mode: 1,
          with: 'items',
          format: 'json'
        };
        return App.ajaxGetOk(`${baseUrl}/tenant/estimates/getOne?dispatch_id=${jobId}`, params, (res) => {
          return res;
        }, {blockEle: estTable.estModal, ...opts}, (err) => {
          console.error(err);
        });
      },
      getEstimatesByJobId: function (jobId, params, opts) {
        return App.ajaxPostOk(`${baseUrl}/tenant/estimates/getAll?dispatch_id=${jobId}`, {...params, format: 'json'}, (res) => {
          return res;
        }, {blockEle: estTable.estModal, ...opts}, (err) => {
          console.error(err);
        });
      },
      saveEstimateByJobId: function (jobId, data, opts) {
        return App.ajaxPostOk(`${baseUrl}/tenant/estimates/createOrUpdate`, {...data, dispatch_id: jobId}, (res) => {
          App.success("Saved estimation successfully!");
          return res;
        }, {blockEle: estTable.estModal, ...opts}, (err) => {
          console.error(err);
        });
      }
    }
  }();

  var estTable = function () {
    return {
      estList: [],
      est: null,
      lineItems: [],
      estModal: null,
      tbody: null,
      customerId: null,
      job: null,

      init: async function (job) {
        this.job = job;
        this.estModal = $('#estModal');
        this.tbody = $('#estModal table.line-items tbody');
        this.tbody.html('');

        // Data setting
        const customer = job.customer || {};
        const addr = job.addresses?.[0] || {};
        const phone = customer.customer_phones?.[0] || {};

        this.estModal.find('#jobId').html(job.id);
        this.estModal.find(".customerDetail .name").html(customer.name);
        this.estModal.find(".customerDetail .address").html(Est.getFullAddress(addr));
        this.estModal.find(".customerDetail .phone").html(phone.phone);

        // Load Initial data
        this.est = {};
        const res = await Est.getDraftEstimate(job.id);
        this.est = res;
        this.est.discount_amount = this.est.discount_amount ? parseFloat(this.est.discount_amount).toFixed(2) : '';
        this.lineItems = this.est.items || [{}];
        console.log(this.est, this.lineItems);

        // billing address
        if (!this.est.billing_street_name) {
          const billingInfo = Est.tenantInfo?.billing_address ?? {};
          console.log('Billing Info', billingInfo);
          this.est = {
            ...this.est, ...{
              billing_company: billingInfo.company,
              billing_name: billingInfo.name,
              billing_email: billingInfo.email,
              billing_phone: billingInfo.phone,
              billing_street_name: billingInfo.street_name,
              billing_city: billingInfo.city,
              billing_state: billingInfo.state,
              billing_zip: billingInfo.zip,
            }
          };
        }
        console.log('Final Data', this.est);
        this.renderFull();
      },
      getEstimateLineItems: function () {
        const lineItems = [];

        // Iterate through each row in the table
        $('div#estModal .line-items tbody tr').each(function () {
          const $row = $(this);
          const uid = $row.attr('data-uid');
          const id = $row.attr('data-id');
          const serviceId = $row.attr('data-service_id');

          // Get values from all inputs in this row
          const name = $row.find('.name-wrap input').val();
          const qty = parseFloat($row.find('.qty-wrap input').val()) || 0;
          const price = parseFloat($row.find('.price-wrap input').val()) || 0;
          const taxable = $row.find('.taxable-wrap input').prop('checked');

          // Calculate total
          const total = qty * price;

          // Add to array
          const x = {
            uid: uid,
            id: id ? sn(id) : null,
            service_id: serviceId ? sn(serviceId) : null,
            name: name,
            qty: qty,
            price: price,
            taxable: taxable ? 1 : 0,
            total: total
          };

          lineItems.push(x);
        });

        return lineItems;
      },
      calculateTotalData: function () {
        const taxPercentage = $('#tax_percent').val();
        let subTotal = 0;
        let taxTotal = 0;
        $('div#estModal .line-items tbody tr').each(function () {
          const $row = $(this);
          const qty = parseFloat($row.find('.qty-wrap input').val()) || 0;
          const price = parseFloat($row.find('.price-wrap input').val()) || 0;
          const taxable = $row.find('.taxable-wrap input').prop('checked');
          if (taxable) {
            taxTotal += (qty * price * taxPercentage / 100);
          }
          subTotal += qty * price;
        });

        const discountTotal = parseFloat($('div#estModal .discount-wrap input').val()) || 0;

        let total = subTotal + taxTotal - discountTotal;

        $('div#estModal .subtotal-wrap').text(sn(subTotal).toFixed(2));
        $('div#estModal .tax-total-wrap').text(taxTotal.toFixed(2));
        $('div#estModal .grand-total-wrap').text(sn(total).toFixed(2));
      },
      addRow: function () {
        const newRow = {
          uid: `uid_${Date.now()}`,
          qty: 1,
        };
        this.lineItems.push(newRow);
        this.tbody.append(this.genRowEle(newRow));
      },
      deleteRow: function (uid) {
        this.lineItems = this.lineItems.filter(x => x.uid != uid);
        this.tbody.find(`tr[data-uid="${uid}"]`).remove();
        this.calculateTotalData();
      },
      genRowEle: function (x) {
        const $tr = $('<tr />');
        $tr.html($('#est-trow-tpl tr').html());

        $tr.attr('data-uid', x.uid || `uid_${Date.now()}`);
        $tr.attr('data-id', x.id || '');
        $tr.attr('data-service_id', x.service_id || '');

        $tr.find('td .name-wrap input').val(x.name || '');
        $tr.find('td .qty-wrap input').val(x.qty || 1);
        $tr.find('td .price-wrap input').val(sn(x.price).toFixed(2));
        $tr.find('td .taxable-wrap input').prop('checked', x.taxable ? 1 : 0);

        const qty = parseFloat(x.qty) || 0;
        const price = parseFloat(x.price) || 0;
        $tr.find('td .total-wrap').text((qty * price).toFixed(2));

        return $tr;
      },
      renderFull: function () {
        App.setFormData(this.estModal, this.est);
        this.renderTableBody();
      },
      renderTableBody: function () {
        this.tbody.html('');
        this.lineItems.forEach(x => {
          const $tr = this.genRowEle(x);
          this.tbody.append($tr);
        });

        if (this.lineItems.length) {
          this.calculateTotalData();
        }
      }
    }
  }();

  $(document).ready(function () {
    Est.init();
  });
</script>
