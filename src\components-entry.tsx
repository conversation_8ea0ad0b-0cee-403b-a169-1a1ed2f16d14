import React from 'react'; 
import ReactDOM from 'react-dom/client';
// Import all custom components
import CustomCard from './components/CustomCard';
import CustomForm from './components/CustomForm';
// Import Ant Design styles
import 'antd/dist/reset.css';
// Component registry for PHP injection
interface ComponentConfig {
    name: string;
    component: React.ComponentType;
    defaultProps?: Record<string, any>;
}
class ComponentInjector {
    private components: Map<string, ComponentConfig> = new Map();
    constructor() {
        this.registerDefaultComponents();
    }
    private registerDefaultComponents() {
        this.register({
            name: 'CustomCard',
            component: CustomCard,
            defaultProps: {
                title: 'Default Card',
                bordered: true,
                hoverable: true,
            },
        });

        this.register({
            name: 'CustomForm',
            component: CustomForm,
            defaultProps: {
                layout: 'vertical',
                title: 'Form',
            },
        });
    }
    register(config: ComponentConfig) {
        this.components.set(config.name, config);
    }
    render(componentName: string, containerId: string, props: Record<string, any> = {}) {
        const config = this.components.get(componentName);
        if (!config) {
            console.error(`Component "${componentName}" not found`);
            return;
        }

        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`Container "${containerId}" not found`);
            return;
        }

        const Component = config.component;
        const mergedProps = { ...config.defaultProps, ...props };

        const root = ReactDOM.createRoot(container);
        root.render(React.createElement(Component, mergedProps));

    }
    renderMultiple(components: Array<{ name: string; containerId: string; props?: Record<string, any> }>) {
        components.forEach(({ name, containerId, props = {} }) => {
            this.render(name, containerId, props);
        });
    }
    getAvailableComponents(): string[] {
        return Array.from(this.components.keys());
    }
}
// Create global instance
const componentInjector = new ComponentInjector();
// Make it available globally for PHP to use
(window as any).CustomComponents = {
    injector: componentInjector,
    render: componentInjector.render.bind(componentInjector),
    renderMultiple: componentInjector.renderMultiple.bind(componentInjector),
    getAvailableComponents: componentInjector.getAvailableComponents.bind(componentInjector),
    // Direct component access
    CustomCard,
    CustomForm,
    // CustomTable,
    // CustomDashboard,
    // React and ReactDOM for external use
    React,
    ReactDOM,
};


// Auto-render components with data attributes
document.addEventListener('DOMContentLoaded', () => {
    const elements = document.querySelectorAll('[data-custom-component]');
    elements.forEach((element) => {
        const componentName = element.getAttribute('data-custom-component');
        const propsData = element.getAttribute('data-props');

        if (componentName && element.id) {
            try {
                const props = propsData ? JSON.parse(propsData) : {};
                componentInjector.render(componentName, element.id, props);
            } catch (error) {
                console.error('Error parsing component props:', error);
            }
        }
    });
});
export default componentInjector;
