const safeNumber = (valParam, decimals) => {
  if (!valParam) return 0;
  const val = Number(valParam);
  if (isNaN(val)) return 0;
  else return typeof decimals !== 'undefined' ? round(val, decimals) : val;
};
const sn = (valParam, decimals) => {
  return safeNumber(valParam, decimals);
};

// Check if marked is loaded, if not, provide a fallback
if (typeof marked === 'undefined') {
  window.marked = {
    parse: function(text) {
      return text || '';
    }
  };
}

function renderMarkdown(text) {
  if (!text) return '';
  try {
    return marked.parse(text);
  } catch (e) {
    return text || '';
  }
}

function wait_icon($ele, title) {
  if (typeof $ele === "undefined") {
    $ele = $('body');
  }
  const option = {
    effect: 'facebook',   //bounce, rotateplane, stretch, orbit, roundBounce, win8, win8_linear, ios, facebook, rotation, pulse, progressBar, bouncePulse, img
    text: title || 'Please wait...',
    textPos: 'vertical',
    bg: 'rgba(255,255,255,0.7)',
    color: '#000',
    waitTime: -1,
  };

  if ($ele.hasClass('modal') && $ele.find('.modal-content').length > 0) {
    $ele.find('.modal-content').waitMe(option);
  } else {
    $ele.waitMe(option);
  }
}

function hide_wait_icon($ele) {
  if (typeof $ele === "undefined") {
    $ele = $('body');
  }
  if ($ele.length > 0 && $ele.hasClass('modal') && $ele.find('.modal-content').length > 0) {
    $ele.find('.modal-content').waitMe('hide');
  } else {
    $ele.waitMe('hide');
  }
}

var App = function () {
  return {
    success: function (msg, auto_close) {
      $.toast({
        heading: 'Success',
        text: msg,
        showHideTransition: 'slide',
        position: 'bottom-right',
        icon: 'success',
        allowToastClose: true,
        hideAfter: (auto_close == undefined || true) ? 6000 : auto_close,
      });
    },
    error: function (msg) {
      $.toast({
        heading: 'Error',
        text: msg,
        showHideTransition: 'slide',
        position: 'bottom-right',
        allowToastClose: true,
        hideAfter: 15000,
        icon: 'error'
      });
    },
    info: function (msg) {
      $.toast({
        heading: 'Note',
        text: msg,
        showHideTransition: 'slide',
        position: 'bottom-right',
        hideAfter: 10000,
        icon: 'info'
      });
    },
    setFormData(form, data) {
      for (const [key, value] of Object.entries(data)) {
        if (!Array.isArray(value)) {
          form.find(`[name="${key}"]`)?.val(value ?? null);
        }
      }
    },
    getFormData(form) {
      let data = {};
      form.find('select, input[type=text], input[type=date], input[type=number], input[type=email], input[type=checkbox]:checked, input[type=radio]:checked, input[type=hidden]').each(function (ind, ele) {
        const key = $(ele).attr('name');
        if (key) {
          if (key.endsWith("[]")) {
            if (key in data) {
              data[key].push($(ele).val());
            } else {
              data[key] = [$(ele).val()];
            }
          } else {
            data[key] = $(ele).val();
          }
        }
      });
      return data;
    },
    showMessages(res, auto_close) {
      // Show single message first.
      if (res.msg != null && res.msg.length > 0) {
        if (res.error) {
          this.error(res.msg);
        } else {
          this.success(res.msg, auto_close);
        }
      }

      // Show multiple messages, then.
      let list = {};
      if ('msg_list' in res && res.msg_list != null) {
        list = res.msg_list;
      } else if (res.data != null && typeof res.data == 'object' && 'msg_list' in res.data && res.data.msg_list != null) {
        list = res.data.msg_list;
      }
      for (let type in list) {
        let msg_list = list[type];
        msg_list.forEach((msg) => {
          if (msg.length > 5) {
            if (type == 's') {
              this.success(msg, auto_close);
            } else if (type == 'i') {
              this.info(msg);
            } else if (type == 'e') {
              this.error(msg);
            }
          }
        });
      }
    },
    ajax(url, data, success, error, options) {
      return $.ajax({
        url: url,
        data: data,
        dataType: 'json',
        type: options?.type ?? 'get',
        processData: options?.processData ?? true,
        contentType: options?.contentType,
        //cache: false,
        success: function (res) {
          if (res.message && typeof res.message === 'string') {
            App.success(res.message);
          }
          if (success !== undefined)
            success(res);
        },
        error: function (res, status, errorStr) {
          console.log(" ===> err", res, status, errorStr);
          App.error(status + " : " + errorStr + " <br />Details: " + res.responseText);
          if (error !== undefined)
            error(res, status, errorStr + res.responseText);
        },
        complete: function (data) {
          options?.complete?.(data);
        }
      });
    },
    ajaxPost(url, data, success, error) {
      return App.ajax(url, data, success, error, {type: 'post'})
    },
    ajaxPostOk(url, data, success, opts, error) {
      let block_ele = null;
      let title = null;
      if (opts && opts.blockEle) {
        if (opts.blockEle) {
          block_ele = opts.blockEle;
        }
        if (opts.blockEleTitle) {
          title = opts.blockEleTitle;
        }
      } else {
        block_ele = opts;
      }

      if (block_ele) wait_icon(block_ele, title);
      return App.ajax(url, data, function (res) {
        if (block_ele) hide_wait_icon(block_ele);
        if (success !== undefined)
          success(res);
      }, function (res, status, errorStr) {
        if (block_ele) hide_wait_icon(block_ele);
        if (error !== undefined)
          error(res, status, errorStr + res.responseText);
      }, {...opts, type: opts?.type ?? 'post'});
    },
    ajaxGetOk(url, data, success, opts, error) {
      return this.ajaxPostOk(url, data, success, {...opts, type: 'get'}, error)
    },
    ajaxPostFiles(url, data, success, opts, error) {
      let block_ele = null;
      let title = null;
      if (opts && opts.blockEle) {
        if (opts.blockEle) {
          block_ele = opts.blockEle;
        }
        if (opts.blockEleTitle) {
          title = opts.blockEleTitle;
        }
      } else {
        block_ele = opts;
      }

      if (block_ele) wait_icon(block_ele, title);
      return this.ajax_post(url, data, function (res) {
        if (block_ele) hide_wait_icon(block_ele);
        if (success !== undefined)
          success(res);
      }, function (res, status, errorStr) {
        if (block_ele) hide_wait_icon(block_ele);
        if (error !== undefined)
          error(res, status, errorStr + res.responseText);
      });
    },
    /*showDialog($dlg_ele, title, body, options, size) {
      if (typeof title != 'undefined' && title != null)
        setDlgTitle($dlg_ele, title);

      if (typeof title != 'undefined' && body != null)
        setDlgBody($dlg_ele, body);
      //setDlgBody($dlg_ele, loading_body);

      $dlg_ele.find(".modal-dialog").removeClass('modal-xxl');
      if (typeof size != 'undefined' && size == 'full') {
        $dlg_ele.find(".modal-dialog").addClass('modal-xxl');
      }

      let default_options = {
        'backdrop': 'static',
        'show': true
      };
      $dlg_ele.modal(typeof options == 'undefined' ? default_options : $.extend(true, default_options, options));
    },*/
    isJQueryReady() {
      return (document.readyState === 'complete');
    },
    playBeep() {
      if ($('#sound').length < 1) {
        $body.append('<div id="sound"></div>');
      }
      const filename = base_url + "/assets/music/beep1";
      const mp3Source = '<source src="' + filename + '.mp3" type="audio/mpeg">';
      const oggSource = '<source src="' + filename + '.ogg" type="audio/ogg">';
      const embedSource = '<embed hidden="true" autostart="true" loop="false" src="' + filename + '.mp3">';
      $("#sound").html('<audio autoplay="autoplay">' + mp3Source + oggSource + embedSource + '</audio>');
    },
    getParams: function (locationObj) {
      locationObj = _.isUndefined(locationObj) ? window.location : locationObj;
      var params = {};
      var query = locationObj.search.substring(1);
      if (query.length <= 2) return params;
      var vars = query.split('&');
      for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split('=');
        params[pair[0]] = decodeURIComponent(pair[1]);
      }
      return params;
    },
    getUrlByParams: function (params) {
      const baseUrl = location.protocol + '//' + location.host + (location.port == 80 ? '' : ':' + location.port) + location.pathname;
      let urlParams = _.assign(App.getParams(), params);
      return baseUrl + App.getUrlParamStr(urlParams);
    },
    getUrlParamStr: function (params) {
      var str = "";
      for (var key in params) {
        if (str != "") {
          str += "&";
        } else {
          str += "?";
        }
        str += key + "=" + encodeURIComponent(params[key]);
      }
      return str;
    },

    /**
     * @param e
     * @param cb_update : callback func(new_val, $td, after_update_callback)
     *                      after_update_callback: func(updated_val, updated_val_html)
     *
     * @param get_editable_value    : function to get editing field value.
     */
    init_td_inline_editing: function (e, cb_update, get_editable_value, opts) {
      const $self = $(e.target);
      if (_.isUndefined(opts)) {
        opts = {
          align: 'text-right',
        };
      }

      // Entering edit mode.
      const isEditing = !$self.hasClass('isEditing');
      const $td = $self.closest(opts.parentSelector || 'td');
      const hideSaveIcon = _.get(opts, 'hideSaveIcon', null) == 1;

      let $val_wrap = $td.find('div.val-wrap');
      let $edit_wrap = $td.find('div.edit-wrap');
      let val = $self.attr('data-val');

      // event func to be invoked
      const after_update = (updated_val, updated_val_html) => {
        $self.attr('data-val', updated_val);
        $val_wrap.find('span').html(updated_val_html);
        finish_edit();
      };
      const update_event = () => {
        // Saving logic...
        const new_val = $edit_wrap.find('input').val();
        cb_update(new_val, $td, after_update);
      };
      const finish_edit = () => {
        $val_wrap.show();
        $edit_wrap.hide();
        $self.removeClass('isEditing');
      };
      if ($edit_wrap.length == 0) {
        $edit_wrap = $("<div class='edit-wrap'><input class='input-edit " + (opts.align || '') + "' type='text' />" + (hideSaveIcon ? '' : EDIT_SAVE) + "</div>");

        // Key event binding
        $edit_wrap.find('input').on('keydown', evt => {
          if (is_tab_pressed(evt)) {
            update_event();
          } else if (evt.key == 'Escape') {
            finish_edit();
          }
          evt.stopPropagation();
        });
        // Click event
        if (!hideSaveIcon)
          $edit_wrap.find('i.oi-edit-save').on('click', e => update_event());
        $edit_wrap.find('input').width($td.width() - (hideSaveIcon ? 0 : 25));
        $edit_wrap.width($val_wrap.width());
        $edit_wrap.insertAfter($val_wrap);
      }
      if (isEditing) {
        $edit_wrap.find('input').val(get_editable_value(val));
        $edit_wrap.show();
        $val_wrap.hide();
        $self.addClass('isEditing');
        $edit_wrap.find('input').select();
      }
    },

    openDlg: function (title, body, width, buttonsParam, opts) {
      const buttons = Object.keys(buttonsParam).reduce((prev, k) => {
        prev[k] = buttonsParam[k];
        prev[k].class = `${prev[k].class || ''} bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded`;
        prev[k].text = prev[k].text ?? k;
        return prev;
      }, {});

      const root = opts?.ele?.length ? opts?.ele : $('<div>');

      root.attr('title', title)
        .html(body)
        .dialog({
          resizable: false,
          height: "auto",
          width: width,
          modal: true,
          classes: {
            "ui-dialog": "shadow-lg rounded-lg",
            "ui-dialog-titlebar": "bg-red-500 text-white py-2 px-4 rounded-t-lg",
            "ui-dialog-content": "py-4 px-6"
          },
          buttons: {
            ...buttons,
            "Cancel": {
              text: "Cancel",
              class: "bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded ml-2",
              click: function () {
                $(this).dialog("close");
              }
            }
          },
          close: function (event, ui) {
            $(this).remove();
          },
          ...opts,
        });
    },

    confirmDlg: function (okCb, options) {
      App.openDlg(
        options?.title || 'Delete Message'
        , '<p><span class="ui-icon ui-icon-alert" style="float:left; margin:12px 12px 20px 0;"></span>Are you sure you want to delete a selected ' + (options?.subject || 'message') + '?</p>'
        , 400
        , {
          "OK": {
            click: function () {
              okCb(this);
              $(this).dialog("close");
            }
          },
        }
      );
    },
    secondsToDhms(seconds) {
      seconds = Number(seconds);
      const d = Math.floor(seconds / (3600 * 24));
      const h = Math.floor(seconds % (3600 * 24) / 3600);
      const m = Math.floor(seconds % 3600 / 60);
      const s = Math.floor(seconds % 60);

      const dDisplay = d > 0 ? d + (d == 1 ? " d" : " d") : "";
      const hDisplay = h > 0 ? h + (h == 1 ? " h" : " h") : "";
      const mDisplay = m > 0 ? m + (m == 1 ? " m" : " m") : "";
      const sDisplay = s > 0 ? s + (s == 1 ? " s" : " s") : "";

      const arr = [];
      if (dDisplay) {
        arr.push(dDisplay);
      }
      if (hDisplay) {
        arr.push(hDisplay);
      }
      if (mDisplay) {
        arr.push(mDisplay);
      }
      if (sDisplay) {
        arr.push(sDisplay);
      }

      return arr.join(' ');
    },
  }
}();


  