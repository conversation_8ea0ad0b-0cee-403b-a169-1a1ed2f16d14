<?php

namespace Butler\Models\Driver;

use Butler\Models\BaseModel;
use Butler\Models\Gps\GpsDispatchBoard;
use Butler\Models\Gps\GpsDispatchNote;
use Butler\Models\Tenant;
use Butler\Models\User;
use Butler\Models\UserProfileHomeHistory;
use Illuminate\Database\Eloquent\Builder;

/**
 * @property integer $id
 * @property integer $tenant_id
 * @property string $display_name
 * @property string $device_sid
 * @property string $factory_id
 * @property string $make
 * @property string $model
 * @property string $active_state
 *
 * @property Tenant $tenant
 * @property GpsDispatchBoard[] $gpsDispatchBoards
 * @property GpsDispatchNote[] $gpsDispatchNotes
 * @property DriverDevicePositionAggregate $lastPosition
 *
 * @property User $user
 * @property DriverDevicePosition[] $positions
 * @property UserProfileHomeHistory[] $userProfileHomeHistories
 */
class DriverDevice extends BaseModel
{
    /**
     * @var array
     */
    protected $fillable = ['tenant_id', 'display_name', 'device_sid', 'factory_id', 'make', 'model', 'active_state'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function tenant()
    {
        return $this->belongsTo('Butler\Models\Tenant');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function gpsDispatchBoards()
    {
        return $this->hasMany('Butler\Models\Gps\GpsDispatchBoard', 'device_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function gpsDispatchNotes()
    {
        return $this->hasMany('Butler\Models\Gps\GpsDispatchNote', 'device_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOneThrough
     */
    public function user()
    {
        return $this->hasOneThrough(
            User::class
            , UserDevice::class
            , 'device_id'
            , 'id'
            , 'id'
            , 'user_id'
        );
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function positions()
    {
        return $this->hasMany('Butler\Models\Driver\DriverDevicePosition', 'device_sid', 'device_sid');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function lastPosition()
    {
        return $this->hasOne('Butler\Models\Driver\DriverDevicePositionAggregate', 'device_sid', 'device_sid');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function userProfileHomeHistories()
    {
        return $this->hasMany('Butler\Models\UserProfileHomeHistory', 'device_id');
    }

    /**
     * @param Builder $query
     * @param $tenantId
     * @return Builder
     */
    public function scopeTenantOnly(Builder $query, $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }
}
