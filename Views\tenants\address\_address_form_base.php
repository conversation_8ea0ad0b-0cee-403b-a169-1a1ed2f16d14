<script>
  let map;
  let marker;
  let geocoder;

  // Initialize Google Maps
  function initMap() {
    geocoder = new google.maps.Geocoder();

    // Default center (will be overridden if coordinates exist)
    let center = {lat: 40.7128, lng: -74.0060}; // Default to NYC

    // Create map
    map = new google.maps.Map(document.getElementById('mapAddress'), {
      center: center,
      zoom: 14,
      mapTypeControl: true,
      streetViewControl: false,
      fullscreenControl: true
    });

    // Create marker if coordinates exist
    if (!isNaN(savedLat) && !isNaN(savedLng)) {
      createMarker(center);
    }

    // Add click listener to map
    map.addListener('click', function (event) {
      const clickedLocation = event.latLng;
      updateLocationFields(clickedLocation);
      createMarker(clickedLocation);

      // Reverse geocode to get address
      geocoder.geocode({'location': clickedLocation}, function (results, status) {
        if (status === 'OK' && results[0]) {
          console.log('Address from GEO', results[0]);
          document.getElementById('user_address').value = results[0].formatted_address;
        }
      });
    });

    // Add search button functionality
    document.getElementById('searchAddressBtn').addEventListener('click', function () {
      const address = document.getElementById('user_address').value;
      if (address) {
        geocodeAddress(address);
      }
    });

    // Allow pressing Enter in the address field to search
    document.getElementById('user_address').addEventListener('keypress', function (e) {
      if (e.key === 'Enter') {
        e.preventDefault();
        const address = this.value;
        if (address) {
          geocodeAddress(address);
        }
      }
    });
  }

  // Create or update marker
  function createMarker(location) {
    // Remove existing marker if it exists
    if (marker) {
      marker.setMap(null);
    }

    // Create new marker
    marker = new google.maps.Marker({
      position: location,
      map: map,
      draggable: true,
      animation: google.maps.Animation.DROP
    });

    // Add drag end listener
    marker.addListener('dragend', function () {
      const newLocation = marker.getPosition();
      updateLocationFields(newLocation);

      // Reverse geocode to get address
      geocoder.geocode({'location': newLocation}, function (results, status) {
        if (status === 'OK' && results[0]) {
          document.getElementById('user_address').value = results[0].formatted_address;
        }
      });
    });

    // Center map on marker
    map.setCenter(location);
  }

  // Update lat/lng input fields
  function updateLocationFields(location) {
    document.getElementById('home_lat').value = location.lat().toFixed(6);
    document.getElementById('home_lng').value = location.lng().toFixed(6);
  }

  // Geocode address and update map
  function geocodeAddress(address) {
    wait_icon($('#addressWrap'));
    geocoder.geocode({'address': address}, function (results, status) {
      if (status === 'OK' && results[0]) {
        const location = results[0].geometry.location;

        // Update map and marker
        map.setCenter(location);
        createMarker(location);

        // Update form fields
        updateLocationFields(location);
        document.getElementById('user_address').value = results[0].formatted_address;
      } else {
        App.error('Geocode was not successful for the following reason: ' + status);
      }
      hide_wait_icon($('#addressWrap'));
    });
  }

  // Load the Google Maps API when the page is ready
  document.addEventListener('DOMContentLoaded', loadGoogleMaps);
</script>