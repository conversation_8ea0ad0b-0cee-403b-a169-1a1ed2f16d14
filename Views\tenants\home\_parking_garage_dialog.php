<script>
function showGaragePopup(job, card) {
  // Remove any existing dialog first
  $("#garageStatusDialog").remove();
  
  const baseApiUrl = BASE_API_URL || window.BASE_API_URL;
  
  $('<div>')
    .attr('id', 'garageStatusDialog')
    .attr('title', 'Set Garage Status')
    .html(`
      <h3 class="text-xl font-bold mb-4">Set Garage Status</h3>
      
      <div class="mb-4">
        <div class="flex items-center space-x-4 mb-3">
          <label class="inline-flex items-center">
            <input type="radio" name="garageStatus" value="driving" class="form-radio h-4 w-4 text-blue-600" checked>
            <span class="ml-2">Driving</span>
          </label>
          <label class="inline-flex items-center">
            <input type="radio" name="garageStatus" value="parked" class="form-radio h-4 w-4 text-blue-600">
            <span class="ml-2">Parked</span>
          </label>
          <label class="inline-flex items-center">
            <input type="radio" name="garageStatus" value="unparked" class="form-radio h-4 w-4 text-blue-600">
            <span class="ml-2">unParked</span>
          </label>
        </div>
      </div>
      
      <div id="parkedOptionsContainer" class="hidden mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">Select Reason(s):</label>
        <div class="grid grid-cols-1 gap-2 max-h-60 overflow-y-auto p-2 border rounded-md">            
          <label class="inline-flex items-center">
            <input type="checkbox" name="parkingReason" value="parking_tech_sr_need" class="form-checkbox h-4 w-4 text-blue-600">
            <span class="ml-2 text-sm">Senior Technician Needed</span>
          </label>
          <label class="inline-flex items-center">
            <input type="checkbox" name="parkingReason" value="parking_parts_home_warranty" class="form-checkbox h-4 w-4 text-blue-600">
            <span class="ml-2 text-sm">Parts Home Warranty</span>
          </label>
          <label class="inline-flex items-center">
            <input type="checkbox" name="parkingReason" value="parking_parts_fast_response" class="form-checkbox h-4 w-4 text-blue-600">
            <span class="ml-2 text-sm">Parts Fast Response</span>
          </label>
          <label class="inline-flex items-center">
            <input type="checkbox" name="parkingReason" value="parking_auth_required" class="form-checkbox h-4 w-4 text-blue-600">
            <span class="ml-2 text-sm">Auth Required</span>
          </label>
          <label class="inline-flex items-center">
            <input type="checkbox" name="parkingReason" value="parking_auth_denied_clam" class="form-checkbox h-4 w-4 text-blue-600">
            <span class="ml-2 text-sm">Auth Denial</span>
          </label>
          <label class="inline-flex items-center">
            <input type="checkbox" name="parkingReason" value="parking_unpaid_ncc" class="form-checkbox h-4 w-4 text-blue-600">
            <span class="ml-2 text-sm">Unpaid NCC</span>
          </label>
          <label class="inline-flex items-center">
            <input type="checkbox" name="parkingReason" value="parking_service_do_not_service" class="form-checkbox h-4 w-4 text-blue-600">
            <span class="ml-2 text-sm">Do Not Service</span>
          </label>
          <label class="inline-flex items-center">
            <input type="checkbox" name="parkingReason" value="parking_appointment_customer_missed" class="form-checkbox h-4 w-4 text-blue-600">
            <span class="ml-2 text-sm">Customer Missed Appointment</span>
          </label>
        </div>
      </div>
    `)
    .dialog({
      resizable: false,
      height: "auto",
      width: 400,
      modal: true,
      classes: {
        "ui-dialog": "shadow-lg rounded-lg",
        "ui-dialog-titlebar": "bg-red-500 text-white py-2 px-4 rounded-t-lg",
        "ui-dialog-content": "py-4 px-6"
      },
      open: function() {
        // Store job reference in dialog
        $(this).data('job', job);
        
        // Set initial state based on job data
        const parkingStatus = job.dispatch_status?.parking_status || 'driving';
        const parkingReasons = {};
        
        // Convert dispatch_meta array to key-value object
        if (job.dispatch_meta && Array.isArray(job.dispatch_meta)) {
          job.dispatch_meta.forEach(item => {
            if (item.key && item.key.startsWith('parking_')) {
              parkingReasons[item.key] = item.value;
            }
          });
        }
        
        // Set the radio button based on current status
        $(`input[name="garageStatus"][value="${parkingStatus}"]`).prop('checked', true);
        
        // Show/hide parked options container based on current status
        if (parkingStatus === 'parked') {
          $('#parkedOptionsContainer').removeClass('hidden');
          
          // Check the appropriate checkboxes based on meta data
          const metaFields = [
            'parking_tech_sr_need',
            'parking_parts_home_warranty',
            'parking_parts_fast_response',
            'parking_auth_required',
            'parking_auth_denied_clam',
            'parking_unpaid_ncc',
            'parking_service_do_not_service',
            'parking_appointment_customer_missed'
          ];
          
          // Check each meta field and set checkbox accordingly
          metaFields.forEach(field => {            
            const isChecked = parkingReasons[field] === 1 || 
                            parkingReasons[field] === '1' || 
                            parkingReasons[field] === true || 
                            parkingReasons[field] === 'true';
            $(`input[name="parkingReason"][value="${field}"]`).prop('checked', isChecked);
          });
        } else {
          $('#parkedOptionsContainer').addClass('hidden');
        }
        
        // Toggle parked options visibility based on radio selection
        $(document).on('change', 'input[name="garageStatus"]', function() {
          if ($(this).val() === 'parked') {
            $('#parkedOptionsContainer').removeClass('hidden');
          } else {
            $('#parkedOptionsContainer').addClass('hidden');
          }
        });
      },
      buttons: {
        "Save": {
          text: "Save",
          class: "bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded",
          click: async function () {
            const status = $('input[name="garageStatus"]:checked').val();
            const jobId = job.id;

            const updateData = {};
            $('input[name="parkingReason"]').each(function() {
              // Set value to 1 if checked, 0 if unchecked
              updateData[$(this).val()] = $(this).prop('checked') ? 1 : 0;
            });

            const overlayHidden = $('#garageOverlay').hasClass('hidden');
            
            if (status === 'driving' || status === 'unparked') {
              // Set driving status
              try {
                App.ajaxPostOk(`${baseApiUrl}jobs/update_job_status`, 
                  {job_id: jobId, data: {parking_status: status}}, 
                  (res) => {
                    App.success('Parking Status updated successfully!');
                    loadKanboard();
                    if (!overlayHidden) {
                      loadGarageView();
                    }          
                  },                   
                );

                // App.ajaxPostOk(`${baseApiUrl}jobs/tenants/update_job_meta`, 
                //   {job_id: jobId, data: updateData}, 
                //   (res) => {
                    
                //   },                   
                // );

                card.style.border = "2px dashed #000";                
                $(this).dialog("close");
              } catch (err) {                
                App.error("Failed to update job status.");
              }
            } else {
              if (Object.keys(updateData).length === 0) {
                return App.error("Please select at least one reason for parking.");
              }
              
              try {
                App.ajaxPostOk(`${baseApiUrl}jobs/update_job_status`, 
                  {job_id: jobId, data: {parking_status: status}}, 
                  (res) => {
                    App.success('Parking Status updated successfully!');
                    loadKanboard();
                    if (!overlayHidden) {
                      loadGarageView();
                    }          
                  },                   
                );

                App.ajaxPostOk(`${baseApiUrl}jobs/tenants/update_job_meta`, 
                  {job_id: jobId, data: updateData}, 
                  (res) => {
                    
                  },                   
                );

                card.style.border = "2px dashed #000";                
                $(this).dialog("close");
              } catch (err) {
                App.error("Failed to update job status.");
              }
            }

            
            
          }
        },
        "Cancel": {
          text: "Cancel",
          class: "bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded ml-2",
          click: function () {
            $(this).dialog("close");
          }
        }
      },
      close: function (event, ui) {
        // Clean up event handlers when dialog closes
        $(document).off('change', 'input[name="garageStatus"]');
      }
    });
}
</script>
