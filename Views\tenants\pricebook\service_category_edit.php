<div class="main-content container mx-auto relative h-full" id="main-content">
    <div class="mt-16">
        <h2 class="text-center text-4xl font-bold ">Update Service Category</h2>
        <div class="flex justify-center w-full mt-8">
            <form method="POST" class="flex flex-col gap-4 w-full max-w-sm">
                <?php if ($parents) { ?>
                    <label class="block text-xl">Parent:</label>
                    <select name="parent_id" class="w-full border p-2 rounded mb-4">
                        <option class="text-black bg-white text-black dark:text-black dark:bg-white"></option>
                        <?php foreach ($parents as &$parent) { ?>
                            <option class="text-black bg-white text-black dark:text-black dark:bg-white" <?php echo $dbRow['parent_id'] == $parent['id'] ? 'selected' : '' ?>
                                    value="<?php echo $parent['id'] ?>"><?php echo $parent['name1'] ?></option>
                        <?php } ?>
                    </select>
                <?php } ?>

                <label class="block text-xl">Name:</label>
                <input type="text" name="name" class="w-full border p-2 rounded mb-4" required value="<?php echo $dbRow['name'] ?>" />

                <div class="flex gap-3">
                    <button type="submit" class="w-full bg-[#00796b] hover:bg-[#004d40] text-white p-2 rounded cursor-pointer">Update</button>
                    <a href="<?php echo $this->controllerBaseUrl ?>"
                       class="w-full bg-gray-100 border-1 border-gray-200 hover:border-gray-300 p-2 rounded text-gray-900 text-center cursor-pointer">Cancel
                    </a>
                </div>
            </form>
        </div>

    </div>

</div>
