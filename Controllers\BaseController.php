<?php

namespace Butler\Controllers;

abstract class BaseController
{
    use BaseControllerTraits;
    use BaseControllerTraits {
        __construct as __construct_traits;
    }

    public ?string $templatePath = null;
    public string $title;
    public string $menu;

    public function __construct()
    {
        $this->__construct_traits();
    }

    public function redirect($url = null)
    {
        header("Location: " . ($url ?: $this->controllerBaseUrl));
        exit;
    }

    abstract function render($view, $data = []);
}