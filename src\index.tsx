import React from 'react'; 
import ReactDOM from 'react-dom/client'; 
import 'antd/dist/reset.css';
// Import components
import CustomCard from './components/CustomCard';
import CustomForm from './components/CustomForm';

// Component registry
class ComponentInjector {
    private components = new Map();
    constructor() {
        this.registerComponents();
    }
    private registerComponents() {
        this.components.set('CustomCard', CustomCard);
        this.components.set('CustomForm', CustomForm);
    }
    render(componentName: string, containerId: string, props: any = {}) {
        const Component = this.components.get(componentName);
        if (!Component) {
            console.error(`Component "${componentName}" not found`);
            return;
        }
        const container = document.getElementById(containerId);
        if (!container) {
            console.error(`Container "${containerId}" not found`);
            return;
        }

        const root = ReactDOM.createRoot(container);
        root.render(React.createElement(Component, props));
    }
    renderMultiple(components: Array<{ name: string; containerId: string; props?: any }>) {
        components.forEach(({ name, containerId, props = {} }) => {
            this.render(name, containerId, props);
        });
    }
    getAvailableComponents() {
        return Array.from(this.components.keys());
    }
}

// Create global instance
const injector = new ComponentInjector();
// Make available globally
(window as any).CustomComponents = {
    render: injector.render.bind(injector),
    renderMultiple: injector.renderMultiple.bind(injector),
    getAvailableComponents: injector.getAvailableComponents.bind(injector),
    React,
    ReactDOM,
};

// Auto-render components with data attributes
document.addEventListener('DOMContentLoaded', () => {
    const elements = document.querySelectorAll('[data-component]');
    elements.forEach((element) => {
        const componentName = element.getAttribute('data-component');
        const propsData = element.getAttribute('data-props');

        if (componentName && element.id) {
            try {
                const props = propsData ? JSON.parse(propsData) : {};
                injector.render(componentName, element.id, props);
            } catch (error) {
                console.error('Error parsing component props:', error);
            }
        }

    });
});
export default injector;