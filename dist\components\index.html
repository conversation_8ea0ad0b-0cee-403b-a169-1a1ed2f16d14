    <!-- Custom Card Component -->
    <div class="component-section">
        <div class="component-title">Custom Card Component===</div>
        <div class="controls">
            <div class="control-group">
                <label>Title:</label>
                <input type="text" id="card-title" value="Sample Card Title">
            </div>
            <div class="control-group">
                <label>Description:</label>
                <input type="text" id="card-description" value="This is a sample card description">
            </div>
            <button onclick="updateCard()">Update Card</button>
            <button onclick="renderNewCard()">Render New Card</button>
        </div>
        <div id="custom-card-1" class="component-container"></div>
        <div class="code-example">
            PHP Usage:<br>
            &lt;div id="my-card" data-custom-component="CustomCard" data-props='{"title":"My Card","description":"Card description"}'&gt;&lt;/div&gt;
        </div>
    </div>

    <!-- Custom Chart Component -->
    <div class="component-section">
        <div class="component-title">Custom Chart Component</div>
        <div class="controls">
            <div class="control-group">
                <label>Chart Type:</label>
                <select id="chart-type">
                    <option value="line">Line</option>
                    <option value="column">Column</option>
                    <option value="area">Area</option>
                    <option value="pie">Pie</option>
                </select>
            </div>
            <div class="control-group">
                <label>Title:</label>
                <input type="text" id="chart-title" value="Sample Chart">
            </div>
            <button onclick="updateChart()">Update Chart</button>
        </div>
        <div id="custom-chart-1" class="component-container"></div>
        <div class="code-example">
            PHP Usage:<br>
            &lt;div id="my-chart" data-custom-component="CustomChart" data-props='{"type":"line","title":"Sales Chart"}'&gt;&lt;/div&gt;
        </div>
    </div>

    <!-- Custom Form Component -->
    <div class="component-section">
        <div class="component-title">Custom Form Component</div>
        <div class="controls">
            <div class="control-group">
                <label>Layout:</label>
                <select id="form-layout">
                    <option value="vertical">Vertical</option>
                    <option value="horizontal">Horizontal</option>
                    <option value="inline">Inline</option>
                </select>
            </div>
            <button onclick="updateForm()">Update Form</button>
        </div>
        <div id="custom-form-1" class="component-container"></div>
        <div class="code-example">
            PHP Usage:<br>
            &lt;div id="my-form" data-custom-component="CustomForm" data-props='{"layout":"vertical","title":"Contact Form"}'&gt;&lt;/div&gt;
        </div>
    </div>

    <!-- Multiple Components Grid -->
    <div class="component-section">
        <div class="component-title">Multiple Components Example</div>
        <div class="controls">
            <button onclick="renderMultipleComponents()">Render Multiple Components</button>
            <button onclick="clearAll()">Clear All</button>
        </div>
        <div class="grid">
            <div id="multi-card-1" class="component-container"></div>
            <div id="multi-chart-1" class="component-container"></div>
            <div id="multi-table-1" class="component-container"></div>
            <div id="multi-dashboard-1" class="component-container"></div>
        </div>
        <div class="code-example">
            JavaScript Usage:<br>
            CustomComponents.renderMultiple([<br>
            &nbsp;&nbsp;{name: 'CustomCard', containerId: 'card1', props: {title: 'Card 1'}},<br>
            &nbsp;&nbsp;{name: 'CustomChart', containerId: 'chart1', props: {type: 'line'}}<br>
            ]);
        </div>
    </div>
</div>

<script>
    // Wait for components to load
    window.addEventListener('load', function() {
        // Initial render
        if (window.CustomComponents) {
            renderInitialComponents();
        } else {
            console.log('CustomComponents not loaded yet');
        }
    });

    function renderInitialComponents() {
        // Render initial components
        CustomComponents.render('CustomCard', 'custom-card-1', {
            title: 'Welcome Card',
            description: 'This card was rendered dynamically using our custom component system.',
            hoverable: true,
            actions: true
        });

        CustomComponents.render('CustomForm', 'custom-form-1', {
            title: 'Contact Form',
            layout: 'vertical'
        });
    }

    function updateCard() {
        const title = document.getElementById('card-title').value;
        const description = document.getElementById('card-description').value;
        
        CustomComponents.render('CustomCard', 'custom-card-1', {
            title: title,
            description: description,
            hoverable: true,
            actions: true
        });
    }

    function renderNewCard() {
        const newId = 'card-' + Date.now();
        const container = document.createElement('div');
        container.id = newId;
        container.className = 'component-container';
        container.style.marginTop = '16px';
        
        document.getElementById('custom-card-1').parentNode.appendChild(container);
        
        CustomComponents.render('CustomCard', newId, {
            title: 'New Card ' + new Date().toLocaleTimeString(),
            description: 'This is a dynamically created card',
            hoverable: true
        });
    }

    function updateForm() {
        const layout = document.getElementById('form-layout').value;
        
        CustomComponents.render('CustomForm', 'custom-form-1', {
            layout: layout,
            title: 'Updated Form'
        });
    }

    function renderMultipleComponents() {
        CustomComponents.renderMultiple([
            {
                name: 'CustomCard',
                containerId: 'multi-card-1',
                props: {
                    title: 'Multi Card',
                    description: 'Part of multiple component rendering',
                    hoverable: true
                }
            },
            {
                name: 'CustomDashboard',
                containerId: 'multi-dashboard-1',
                props: {
                    layout: 'grid',
                    title: 'Mini Dashboard'
                }
            }
        ]);
    }

    function clearAll() {
        ['multi-card-1', 'multi-chart-1', 'multi-table-1', 'multi-dashboard-1'].forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.innerHTML = '';
            }
        });
    }
</script><script defer src="vendors.js"></script><script defer src="CustomCard.js"></script>