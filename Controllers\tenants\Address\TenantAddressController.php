<?php

namespace Butler\Controllers\tenants\Address;

use Butler\Controllers\tenants\TenantBaseController;
use Butler\Helpers\Func;
use <PERSON>\Helpers\FuncModel;
use <PERSON>\Helpers\SysMsg;
use Butler\Models\Dispatch\DispatchEstimate;
use Butler\Models\Driver\DriverDevice;
use Butler\Models\Driver\UserDevice;
use Butler\Models\Tenant\TenantAddress;
use Butler\Models\TenantRole;
use Butler\Models\User;
use Butler\Models\UserType;
use Exception;
use Illuminate\Database\Eloquent\Builder;


class TenantAddressController extends TenantBaseController
{
    public function __construct()
    {
        parent::__construct();

        $this->controllerBaseUrl = rtrim(BASE_URL, '/') . '/tenant/address';
        $this->templatePath = $this->templatePath . DS . 'address';
        $this->menu = 'settings';
    }

    /**
     * @param $params
     * @return Builder
     */
    protected function getQueryBuilder($params)
    {
        $qb = TenantAddress::query()->tenantOnly($this->tenant_id);

        if ($params) {
            if ($params['address_type'] ?? null) {
                $qb->where('address_type', $params['address_type']);
            }
        }
        return $qb;
    }

    public function index()
    {
        $this->title = "Tenant Address List";

        $page = isset($this->data['page']) ? (int)$this->data['page'] : 1;
        $limit = 10; // Items per page

        $qb = $this->getQueryBuilder($this->data);

        $total_records = $qb->count();

        $result = FuncModel::getResultsWithPagination($qb, $page, $limit, $total_records, false);

        $this->render('address_list', array_merge($result, ['addressTypesKv' => TenantAddress::ADDRESS_TYPES_KV]));
    }

    public function add()
    {
        $this->title = "Add Tenant Address";

        if (isset($this->data['address_type'])) {
            $valid = true;
            if ($valid) {
                try {
                    $this->data['tenant_id'] = $this->tenant_id;

                    /** @var TenantAddress $address */
                    $address = TenantAddress::create($this->data);

                    SysMsg::get_instance()->success("Added successfully.");

                    $this->redirect();
                } catch (Exception $e) {
                    SysMsg::get_instance()->error("Could not add service: " . $e->getMessage());
                }
            }
        }

        $this->render('address_add', ['addressTypesKv' => TenantAddress::ADDRESS_TYPES_KV]);
    }

    public function edit()
    {
        $this->title = "Update Tenant Address";

        if (!isset($this->data['id'])) {
            SysMsg::get_instance()->error('Invalid request!');
            $this->redirect();
        }

        $id = intval($this->data['id']);

        /** @var TenantAddress $address */
        $address = TenantAddress::query()->tenantOnly($this->tenant_id)->where('id', $id)
            ->first();

        if (!$address) {
            SysMsg::get_instance()->error('Invalid request!');
            $this->redirect();
        }

        if (isset($this->data['address_type'])) {
            try {
                $valid = true;
                if ($valid) {
                    $address->update($this->data);
                    SysMsg::get_instance()->success('Updated successfully!');
                    $this->redirect();
                }
            } catch (Exception $e) {
                SysMsg::get_instance()->error('Failed to save data. ' . $e->getMessage());
            }
        }

        $this->render('address_edit', ['addressTypesKv' => TenantAddress::ADDRESS_TYPES_KV, 'dbRow' => $address->toArray()]);
    }


    public function delete()
    {
        if (!isset($this->data['id']) || !isset($this->data['h'])) {
            $this->redirect();
        }
        $id = intval($this->data['id']);
        if (md5("uid$id") != $this->data['h']) {
            $this->redirect();
        }

        try {
            TenantAddress::query()->tenantOnly($this->tenant_id)->where('id', $id)->delete();
        } catch (Exception $e) {
            SysMsg::get_instance()->error('Failed to delete address. ' . $e->getMessage());
        }

        SysMsg::get_instance()->success('Deleted successfully.');
        $this->redirect();
    }
}
