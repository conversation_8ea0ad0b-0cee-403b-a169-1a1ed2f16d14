<div id="outboundCallModal" class="fixed top-4 right-4 w-80 bg-white rounded-lg shadow-lg z-100 hidden">
  <div class="p-4 border-b border-gray-200">
    <div class="flex justify-between items-center">
      <h3 class="text-lg font-semibold">Outbound Call</h3>
      <button id="minimizeOutboundCall" class="text-gray-500 hover:text-gray-700">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M5 10a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1z" clip-rule="evenodd" />
        </svg>
      </button>
    </div>
    <div class="mt-2">
      <p id="outboundCallStatus" class="text-sm font-medium">Connecting...</p>
      <p id="outboundCallNumber" class="text-sm text-gray-500"></p>
      <p id="outboundCallCustomer" class="text-sm text-gray-700 font-medium"></p>
    </div>
  </div>
  <div class="p-4">
    <div class="mb-4">
      <label class="block text-sm font-medium mb-1">Call Notes:</label>
      <textarea id="outboundCallNotes" rows="3" class="w-full p-2 border rounded"></textarea>
    </div>
    <div class="flex justify-between">
      <button id="saveOutboundCallNotes" class="bg-blue-500 text-white px-3 py-1 rounded text-sm">Save Notes</button>
      <button id="hangupOutboundCall" class="bg-red-500 text-white px-3 py-1 rounded text-sm">Hang Up</button>
    </div>
  </div>
</div>

<div id="inboundModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden z-100">
  <div class="bg-white rounded-lg shadow-lg w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
    <div class="p-6">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold">Incoming Call</h2>
        <div class="flex items-center">
          <span id="inboundCallTimer" class="mr-4 text-lg font-medium">00:00</span>
          <button id="minimizeInboundCall" class="text-gray-500 hover:text-gray-700 mr-2">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 12H6" />
            </svg>
          </button>
          <button id="closeInboundCall" class="text-gray-500 hover:text-gray-700">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Customer Information -->
        <div class="bg-gray-50 p-4 rounded-lg">
          <h3 class="text-lg font-semibold mb-3">Customer Information</h3>
          <div class="mb-2">
            <p class="text-sm text-gray-500">Phone Number:</p>
            <p id="inboundCallNumber" class="font-medium"></p>
          </div>
          <div class="mb-2">
            <p class="text-sm text-gray-500">Name:</p>
            <p id="inboundCallCustomerName" class="font-medium"></p>
          </div>
          <div class="mb-2">
            <p class="text-sm text-gray-500">Address:</p>
            <p id="inboundCallCustomerAddress" class="font-medium"></p>
          </div>
          
          <!-- Recent Jobs -->
          <h4 class="text-md font-semibold mt-4 mb-2">Recent Jobs</h4>
          <div id="inboundCallRecentJobs" class="max-h-40 overflow-y-auto">
            <!-- Jobs will be populated here -->
          </div>
        </div>
        
        <!-- Call Actions -->
        <div>
          <div class="mb-4">
            <h3 class="text-lg font-semibold mb-3">Call Notes</h3>
            <textarea id="inboundCallNotes" rows="4" class="w-full p-2 border rounded"></textarea>
            <button id="saveInboundCallNotes" class="mt-2 bg-blue-500 text-white px-3 py-1 rounded">Save Notes</button>
          </div>
          
          <div class="mb-4">
            <h3 class="text-lg font-semibold mb-3">Update Appointment Status</h3>
            <div id="inboundCallAppointmentStatus" class="grid grid-cols-2 gap-2">
                <label class="flex items-center">
                    <input type="radio" name="inbound_appointment_status" value="30" class="mr-2">
                    <span class="px-2 py-1 rounded" style="background-color: #4CAF50; color: white;">Initial Appointment Scheduled</span>
                </label>
                <label class="flex items-center">
                    <input type="radio" name="inbound_appointment_status" value="80" class="mr-2">
                    <span class="px-2 py-1 rounded" style="background-color: #2196F3; color: white;">Technician May Be Delayed to Appointment</span>
                </label>
                <label class="flex items-center">
                    <input type="radio" name="inbound_appointment_status" value="120" class="mr-2">
                    <span class="px-2 py-1 rounded" style="background-color: #FF9800; color: white;">Customer Missed Appointment</span>
                </label>
                <label class="flex items-center">
                    <input type="radio" name="inbound_appointment_status" value="220" class="mr-2">
                    <span class="px-2 py-1 rounded" style="background-color: #673AB7; color: white;">Appointment Completed</span>
                </label>
                <label class="flex items-center">
                    <input type="radio" name="inbound_appointment_status" value="230" class="mr-2">
                    <span class="px-2 py-1 rounded" style="background-color: #F44336; color: white;">Appointment Cancelled</span>
                </label>
            </div>
            <button id="updateInboundAppointmentStatus" class="mt-2 bg-green-500 text-white px-3 py-1 rounded">Update Status</button>
          </div>
          
          <div>
            <h3 class="text-lg font-semibold mb-3">Create New Job</h3>
            <button id="createNewJobFromCall" class="bg-indigo-500 text-white px-4 py-2 rounded">Create New Job</button>
          </div>
        </div>
      </div>
      
      <div class="mt-6 flex justify-end">
        <div id="incomingCallControls" class="flex space-x-4">
          <button id="acceptRingBtn" class="bg-green-500 text-white px-4 py-2 rounded">Accept Call</button>
          <button id="rejectRingBtn" class="bg-red-500 text-white px-4 py-2 rounded">Reject Call</button>
        </div>
        <button id="hangupInboundCall" class="bg-red-500 text-white px-4 py-2 rounded">End Call</button>
      </div>
    </div>
  </div>
</div>
