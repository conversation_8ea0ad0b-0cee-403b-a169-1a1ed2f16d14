CREATE TABLE dispatch_meta (
	id INT UNSIGNED auto_increment NOT NULL PRIMARY KEY,
	dispatch_id INT UNSIGNED NOT NULL,
	`key` varchar(255) NOT NULL,
	value TEXT NULL,
	created_at DATETIME DEFAULT current_timestamp() NOT NULL,
	updated_at TIMESTAMP DEFAULT current_timestamp() on update current_timestamp() NOT NULL,	
	CONSTRAINT dispatch_meta_dispatches_FK FOREIGN KEY (dispatch_id) REFERENCES dispatches(id) ON DELETE CASCADE ON UPDATE CASCADE
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;
