<?php

namespace Butler\Controllers\tenants\PriceBook;

use Butler\Controllers\api\ApiBaseController;
use Butler\Helpers\Func;
use Butler\Helpers\FuncModel;
use Butler\Models\PriceBook\PbService;
use Illuminate\Database\Eloquent\Builder;

class ServiceApiController extends ApiBaseController
{
    public function __construct()
    {
        parent::__construct();
    }

    public function search()
    {
        $qb = PbService::query();
        $qb->tenant($this->tenant_id);

        $category_id = intval($_GET['category_id'] ?? null);
        if ($category_id) {
            $qb->whereHas('pbServiceCategories', function ($qb) use (&$category_id) {
                $qb->where('id', $category_id);
            });
        }

        $category_ids = Func::csvToArr($_GET['category_ids'] ?? '');
        if ($category_ids) {
            $qb->whereHas('pbServiceCategories', function ($qb) use (&$category_ids) {
                $qb->whereIn('id', $category_ids);
            });
        }

        $keyWords = trim($_GET['keyWords'] ?? '');
        if ($keyWords) {
            $qb->where(function (Builder $qb) use (&$keyWords) {
                $qb->where('task_code', 'like', $keyWords . '%')
                    ->orWhere('name', 'like', '%' . $keyWords . '%')
                    ->orWhere('description', 'like', '%' . $keyWords . '%')
                    ->orWhereHas('pbServiceCategories', function ($qb) use (&$keyWords) {
                        $qb->where('name', 'like', '%' . $keyWords . '%');
                    })
                ;
            });
        }

        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = 10; // Items per page
        $total_records = $qb->count();

        $qb->with('pbServiceCategories');

        $result = FuncModel::getResultsWithPagination($qb, $page, $limit, $total_records);

        $this->json($result);
    }
}