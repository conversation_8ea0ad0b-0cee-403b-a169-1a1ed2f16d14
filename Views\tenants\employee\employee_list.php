<?php
/** @var \Butler\Controllers\tenants\Employee\EmployeeController $this */
/** @var array $rows */
?>
<div class="main-content container mx-auto relative h-full" id="main-content">
    <div class="mt-16">
        <div class="page-header flex items-center">
            <h2 class="text-3xl font-bold flex-1"><?php echo $title ?></h2>
            <div class="actions flex-none">
                <a id="new" class="bg-[#00796b] hover:bg-[#004d40] text-white px-4 py-2 rounded cursor-pointer"
                   href="<?php echo $this->controllerBaseUrl . '/add' ?>">
                    Add Employee
                </a>
            </div>
        </div>

        <div class="flex justify-center w-full mt-4">
            <table class="w-full table-auto border-collapse mb-4">
                <thead>
                <tr class="bg-gray-200">
                    <th class="border px-2 py-1">User ID</th>
                    <th class="border px-2 py-1">Name</th>
                    <th class="border px-2 py-1">Email</th>
                    <th class="border px-2 py-1">Role</th>
                    <th class="border px-2 py-1">Device</th>
                    <th class="border px-2 py-1">Created At</th>
                    <th class="border px-2 py-1">Actions</th>
                </tr>
                </thead>
                <tbody>
                <?php
                foreach ($rows as $row): ?>
                    <tr>
                        <td class="border px-2 py-1"><?php echo htmlspecialchars($row['user_id']); ?></td>
                        <td class="border px-2 py-1"><?php echo htmlspecialchars($row['user_name']); ?></td>
                        <td class="border px-2 py-1"><?php echo htmlspecialchars($row['user_email']); ?></td>
                        <td class="border px-2 py-1"><?php echo $row['tenant_roles'][0]['role'] ?? null; ?></td>
                        <td class="border px-2 py-1"><?php echo htmlspecialchars($row['driver_device']['display_name'] ?? ''); ?></td>
                        <td class="border px-2 py-1"><?php echo htmlspecialchars($row['created_at']); ?></td>
                        <td class="border px-2 py-1">
                            <a href="<?php echo $this->controllerBaseUrl ?>/edit?id=<?php echo htmlspecialchars($row['id']); ?>&h=<?php echo md5("uid{$row['id']}") ?>"
                               class="text-blue-500 hover:underline">Edit</a> |
                            <a href="<?php echo $this->controllerBaseUrl ?>/delete?id=<?php echo htmlspecialchars($row['id']); ?>&h=<?php echo md5("uid{$row['id']}") ?>"
                               class="text-red-500 hover:underline">Delete</a>
                        </td>
                    </tr>
                <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <nav aria-label="Page navigation">
            <ul class="flex justify-center">
                <?php if ($pagination['current_page'] > 1): ?>
                    <li class="text-xl mx-2 font-bold bg-gray-200 rounded-2xl border-1 border-gray-200 p-1 ">
                        <a class="page-link" href="?page=<?php echo $pagination['current_page'] - 1 ?>">&laquo; Previous</a>
                    </li>
                <?php endif; ?>

                <?php for ($i = 1; $i <= $pagination['total_pages']; $i++): ?>
                    <li class="text-xl mx-2 font-bold bg-gray-200 rounded-2xl border-1 border-gray-200 <?php echo $i == $pagination['current_page'] ? 'bg-gray-700' : '' ?> p-1">
                        <a class="page-link" href="?page=<?php echo $i ?>"><?php echo $i ?></a>
                    </li>
                <?php endfor; ?>

                <?php if ($pagination['current_page'] < $pagination['total_pages']): ?>
                    <li class="text-xl mx-2 font-bold bg-gray-200 rounded-2xl border-1 border-gray-200 p-1">
                        <a class="page-link" href="?page=<?php echo $pagination['current_page'] + 1 ?>">Next &raquo;</a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>

        <div class="text-center mt-2">
            <small class="text-muted">
                Showing <?php echo ($pagination['current_page'] - 1) * $pagination['limit'] + 1 ?>
                to <?php echo min($pagination['current_page'] * $pagination['limit'], $pagination['total_records']) ?>
                of <?php echo $pagination['total_records'] ?> entries
            </small>
        </div>
    </div>
</div>