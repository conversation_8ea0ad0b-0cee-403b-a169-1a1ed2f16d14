name: Lint PR
on:
  pull_request_target:
    types: [ opened, edited, synchronize, reopened ]

jobs:
  validate:
    name: Validate title
    runs-on: ubuntu-latest
    steps:
      - uses: amannn/action-semantic-pull-request@v5
        with:
          types: |
            chore
            docs
            fix
            feat
            misc
            test
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
