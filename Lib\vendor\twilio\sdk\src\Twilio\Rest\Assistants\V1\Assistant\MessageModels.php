<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Assistants
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Assistants\V1\Assistant;

use Twilio\Values;
abstract class MessageModels
{
    /**
     * @property string $identity The unique identity of user for the session.
     * @property string $sessionId The unique name for the session.
     * @property string $body The query to ask the assistant.
     * @property string $webhook The webhook url to call after the assistant has generated a response or report an error.
     * @property string $mode one of the modes 'chat', 'email' or 'voice'
    */
    public static function createAssistantsV1ServiceAssistantSendMessageRequest(array $payload = []): AssistantsV1ServiceAssistantSendMessageRequest
    {
        return new AssistantsV1ServiceAssistantSendMessageRequest($payload);
    }

}

class AssistantsV1ServiceAssistantSendMessageRequest implements \JsonSerializable
{
    /**
     * @property string $identity The unique identity of user for the session.
     * @property string $sessionId The unique name for the session.
     * @property string $body The query to ask the assistant.
     * @property string $webhook The webhook url to call after the assistant has generated a response or report an error.
     * @property string $mode one of the modes 'chat', 'email' or 'voice'
    */
        protected $identity;
        protected $sessionId;
        protected $body;
        protected $webhook;
        protected $mode;
    public function __construct(array $payload = []) {
        $this->identity = Values::array_get($payload, 'identity');
        $this->sessionId = Values::array_get($payload, 'session_id');
        $this->body = Values::array_get($payload, 'body');
        $this->webhook = Values::array_get($payload, 'webhook');
        $this->mode = Values::array_get($payload, 'mode');
    }

    public function toArray(): array
    {
        return $this->jsonSerialize();
    }

    public function jsonSerialize(): array
    {
        return [
            'identity' => $this->identity,
            'session_id' => $this->sessionId,
            'body' => $this->body,
            'webhook' => $this->webhook,
            'mode' => $this->mode
        ];
    }
}

