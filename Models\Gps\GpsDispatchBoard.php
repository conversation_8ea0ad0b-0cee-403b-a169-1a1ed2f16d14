<?php

namespace Butler\Models\Gps;

use Butler\Models\BaseModel;
use Butler\Models\Dispatch\Dispatch;
use Butler\Models\Driver\DriverDevice;

/**
 * @property integer $id
 * @property integer $dispatch_id
 * @property integer $device_id
 * @property string $on_site
 * @property string $off_site
 * @property integer $time
 * @property boolean $exclude_from_board
 *
 * @property DriverDevice $driverDevice
 * @property Dispatch $dispatch
 */
class GpsDispatchBoard extends BaseModel
{
    /**
     * The table associated with the model.
     * 
     * @var string
     */
    protected $table = 'gps_dispatch_board';

    /**
     * @var array
     */
    protected $fillable = ['dispatch_id', 'device_id', 'on_site', 'off_site', 'time', 'exclude_from_board'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function driverDevice()
    {
        return $this->belongsTo('Butler\Models\Driver\DriverDevice', 'device_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function dispatch()
    {
        return $this->belongsTo('Butler\Models\Dispatch\Dispatch');
    }
}
