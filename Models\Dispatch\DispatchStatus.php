<?php

namespace Butler\Models\Dispatch;

use Butler\Models\BaseModel;


class DispatchStatus extends BaseModel {

    public const STATUS_JOB = 'job_status';
    public const STATUS_JOB_ASSIGNED = '250';
    public const STATUS_JOB_ACCEPTED = '260';
    public const STATUS_JOB_CANCELED = '40';
    public const STATUS_JOB_COMPLETED = '10';
    public const STATUS_JOB_IN_PROGRESS = '20';
    public const STATUS_JOB_INCOMPLETED = '140';
    public const STATUS_JOB_ON_HOLD = '150';


    public const STATUS_APPOINTMENT = 'appointment_status';
    public const STATUS_APPOINTMENT_INIT_SET = '30'; // Initial Set
    public const STATUS_APPOINTMENT_TECHNICIAN_MAY_DELAYED = '80'; // Technician May Delayed
    public const STATUS_APPOINTMENT_CUSTOMER_MISSED = '120'; // Customer Missed
    public const STATUS_APPOINTMENT_INSTALLATION_APPOINTMENT_SET = '430'; // Installation Appointment Set
    public const STATUS_APPOINTMENT_APPOINTMENT_COMPLETE = '220'; // Appointment Complete
    public const STATUS_APPOINTMENT_APPOINTMENT_CANCELLED = '230'; // Appointment Cancelled

    public const STATUS_PARTS = 'parts_status';
    public const STATUS_EQUIPMENT = 'equipment_status';
    public const STATUS_CONTACT = 'contact_status';
    public const STATUS_AUTHORIZATION = 'authorization_status';
    public const STATUS_CIL = 'cil_status';
    public const STATUS_INVOICE = 'invoice_status';
    public const STATUS_CASH = 'cash_status';
    public const STATUS_APPLIANCE = 'appliance_status';
    public const STATUS_NCC = 'ncc_status';
    public const STATUS_QUOTE = 'quote_status';

    public const STATUS_DRIVE = 'drive_status';
    public const STATUS_DRIVE_TECHNICIAN_IN_ROUTE = '70'; // Technician in Route
    public const STATUS_DRIVE_TECHNICIAN_ARRIVED = '90'; // Technician Arrived



    protected $table = 'dispatch_status';

    /**
     * @var array
     */
    protected $fillable = ['dispatch_id', 'job_status', 'appointment_status', 'parts_status', 'equipment_status', 'contact_status', 'authorization_status', 'cil_status', 'invoice_status', 'cash_status', 'appliance_status', 'ncc_status', 'quote_status', 'parking_status'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function dispatch() {
        return $this->belongsTo(Dispatch::class, 'dispatch_id');
    }

}