<?php

namespace Butler\Models\Gps;

use Butler\Models\BaseModel;
use Butler\Models\Dispatch\Dispatch;
use Butler\Models\Driver\DriverDevice;

/**
 * @property integer $id
 * @property integer $dispatch_id
 * @property integer $device_id
 * @property string $driver_name
 * @property string $note_date
 * @property string $issue_note
 * @property string $has_pics
 * @property string $job_status
 * @property string $created_at
 * @property string $dispatch_url
 * @property string $smart_url
 * @property string $acknowledged
 * @property boolean $submitted
 *
 * @property DriverDevice $driverDevice
 * @property Dispatch $dispatch
 */
class GpsDispatchNote extends BaseModel
{
    const UPDATED_AT = null;
    public $timestamps = [
        self::CREATED_AT
    ];

    /**
     * @var array
     */
    protected $fillable = ['dispatch_id', 'device_id', 'driver_name', 'note_date', 'issue_note', 'has_pics', 'job_status', 'created_at', 'dispatch_url', 'smart_url', 'acknowledged', 'submitted'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function driverDevice()
    {
        return $this->belongsTo('Butler\Models\Driver\DriverDevice', 'device_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function dispatch()
    {
        return $this->belongsTo('Butler\Models\Dispatch\Dispatch');
    }
}
