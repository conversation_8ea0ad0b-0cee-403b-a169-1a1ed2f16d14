const fs = require("fs");
const path = require('path');

module.exports = function getPath(dir, append_dir) {
    var results = [];
    fs.readdirSync(dir).forEach(File => {
        const abs_path = dir + '/' + File;
        const stat = fs.statSync(abs_path);
        if (stat.isDirectory()) {
        	if (File == 'config' || File == 'shared') return [];
        	const sub_list = getPath(abs_path);
        	if (sub_list.length > 0) {
        		const x = sub_list.filter((e) => e == `${abs_path}/index.js`);
        		if (x.length > 0) {
					results.push(`${abs_path}/index.js`);
				} else {
					results.push.apply(results, sub_list);
				}
			}
        } else {
            return results.push(abs_path);
        }
    });
    return results;
};



