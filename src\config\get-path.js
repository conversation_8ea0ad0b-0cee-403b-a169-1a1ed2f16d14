const fs = require("fs");
const path = require('path');

module.exports = function getPath(dir, append_dir) {
    const results = [];
    fs.readdirSync(dir).forEach(file => {
        const abs_path = dir + '/' + file;
        const stat = fs.statSync(abs_path);
        if (stat.isDirectory()) {
        	if (file == 'config' || file == 'shared') return [];
        	const sub_list = getPath(abs_path);
            
        	if (sub_list.length > 0) {
        		const x = sub_list.filter((e) => `${e}`.endsWith('index.tsx') || `${e}`.endsWith('Page.tsx'));
        		if (x.length > 0) {
					x.forEach(element => {
                        results.push(element);
                    });
				}
			}
        } else {
            results.push(abs_path);
        }
    });
    return results;
};



