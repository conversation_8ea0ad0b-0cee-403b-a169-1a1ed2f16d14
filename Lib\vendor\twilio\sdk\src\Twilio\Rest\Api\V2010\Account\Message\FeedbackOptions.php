<?php
/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Api
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Api\V2010\Account\Message;

use Twilio\Options;
use Twilio\Values;

abstract class FeedbackOptions
{
    /**
     * @param string $outcome
     * @return CreateFeedbackOptions Options builder
     */
    public static function create(
        
        string $outcome = Values::NONE

    ): CreateFeedbackOptions
    {
        return new CreateFeedbackOptions(
            $outcome
        );
    }

}

class CreateFeedbackOptions extends Options
    {
    /**
     * @param string $outcome
     */
    public function __construct(
        
        string $outcome = Values::NONE

    ) {
        $this->options['outcome'] = $outcome;
    }

    /**
     * @param string $outcome
     * @return $this Fluent Builder
     */
    public function setOutcome(string $outcome): self
    {
        $this->options['outcome'] = $outcome;
        return $this;
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        $options = \http_build_query(Values::of($this->options), '', ' ');
        return '[Twilio.Api.V2010.CreateFeedbackOptions ' . $options . ']';
    }
}

