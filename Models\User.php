<?php

namespace Butler\Models;

use <PERSON>\Models\BaseModel;
use Butler\Models\Dispatch\DispatchUser;
use Butler\Models\Driver\DriverDevice;
use Butler\Models\Driver\UserDevice;
use Butler\Models\UserProfile;
use Illuminate\Database\Query\Builder;

/**
 * @property integer $id
 * @property string $user_id
 * @property string $user_email
 * @property string $user_name
 * @property string $profile_image
 * @property string $password
 * @property string $api_token
 * @property string $token_expires
 * @property string $refresh_token
 * @property string $created_at
 * @property string $updated_at
 *
 * @property UserType[] $userTypes
 * @property UserDevice $userDevice
 * @property DriverDevice $driverDevice
 * @property UserProfile $userProfile
 * @property UserProfileHomeHistory[] $userProfileHomeHistories
 * @property TenantRole[] $tenantRoles
 * @property Dispatch\DispatchUser[] $userDispatches;
 */
class User extends BaseModel
{
    /**
     * @var array
     */
    protected $fillable = ['user_id', 'user_email', 'user_name', 'profile_image', 'password', 'created_at', 'updated_at'];
    protected $hidden = ['password'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function userTypes()
    {
        return $this->hasMany('Butler\Models\UserType');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function userProfile()
    {
        return $this->hasOne(UserProfile::class, 'user_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function userDispatches() {
        return $this->hasMany(DispatchUser::class, 'user_id', 'id');
    }

    /**
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param $tenant_id
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeTenant(\Illuminate\Database\Eloquent\Builder $query, $tenant_id=null)
    {
        return $query->whereHas('userTypes', function($builder) use (&$tenant_id) {
            if ($tenant_id) {
                $builder->where('tenant_id', $tenant_id);
            }
        });
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function userDevice()
    {
        return $this->hasOne('Butler\Models\Driver\DriverDevice');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\hasOneThrough
     */
    public function driverDevice()
    {
        return $this->hasOneThrough(
            'Butler\Models\Driver\DriverDevice'
            , UserDevice::class
            , 'user_id'
            , 'id'
            , 'id'
            , 'device_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function userProfileHomeHistories()
    {
        return $this->hasMany('Butler\Models\UserProfileHomeHistory');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function tenantRoles()
    {
        return $this->belongsToMany('Butler\Models\TenantRole', 'user_tenant_roles');
    }

    /**
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param $tenant_id
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeEmployee(\Illuminate\Database\Eloquent\Builder $query, $tenant_id=null)
    {
        return $query->whereHas('userTypes', function($builder)  use (&$tenant_id){
            $builder->where('user_type', 'employee');
            if ($tenant_id) {
                $builder->where('tenant_id', $tenant_id);
            }
        });
    }

    /**
     * Get the dispatch associated with this user.
     * 
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function dispatchUser() {
        return $this->hasOne(DispatchUser::class, 'user_id');
    }

    /**
     * Associate a dispatch with this user.
     * 
     * @param int $dispatchId
     * @return \Butler\Models\Dispatch\DispatchUser
     */
    public function associateDispatch($dispatchId) {
        // Delete any existing association first to maintain uniqueness
        DispatchUser::where('user_id', $this->id)->delete();
        
        // Create new association
        return DispatchUser::create([
            'user_id' => $this->id,
            'dispatch_id' => $dispatchId
        ]);
    }

    /**
     * Find a user by their email address
     * 
     * @param string $email
     * @param bool $includeTenant Include tenant data
     * @param bool $includeUserTypes Include user type data
     * @return User|null
     */
    public static function findByEmail($email, $includeTenant = false, $includeUserTypes = false)
    {
        $query = User::query()->where('user_email', $email);
        
        if ($includeTenant) {
            $query->with('userTypes.tenant');
        }
        
        if ($includeUserTypes) {
            $query->with('userTypes');
        }
        
        $user = $query->first();
        if ($user) {
            $user->makeVisible(['password']);
        }
        return $user;
    }
}
