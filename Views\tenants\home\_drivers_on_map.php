<script>
  var DriverOnMap = function () {
    return {
      async updateMapWithRoutes() {
        console.log('[scheduled]', scheduledJobs);
        jobMarkers.forEach(m => m.setMap(null));
        jobMarkers = [];
        directionsRenderers.forEach(dr => dr.setMap(null));
        directionsRenderers = [];
        scheduledJobs.forEach(function (job) {
          const cust = job.dispatch_customers?.[0]?.customer;
          const custGeo = getCustGeoByJob(job);

          const contentDiv = document.createElement("div");
          let markerColor = "#fff";
          if (job.assigned_techs && employeeMapById[job.assigned_techs] && employeeMapById[job.assigned_techs].user_profile?.user_color) {
            markerColor = employeeMapById[job.assigned_techs].user_profile?.user_color;
            console.log('[markColor]', markerColor);
          }

          contentDiv.style.background = markerColor;
          contentDiv.style.border = "1px solid #000";
          contentDiv.style.borderRadius = "100%";
          contentDiv.style.padding = "2px 7px";
          contentDiv.style.fontSize = "14px";
          contentDiv.style.fillOpacity = 0.7;
          contentDiv.innerText = job.dispatch_appointment.stop_sequence ? String(job.dispatch_appointment.stop_sequence) : "";
          contentDiv.title = `Job #${job.id} | ${cust?.name}`;

          const mk = new google.maps.marker.AdvancedMarkerElement({
            map: map,
            position: new google.maps.LatLng(...custGeo),
            content: contentDiv,
          });


          const infoWindow = new google.maps.InfoWindow();
          infoWindow.setContent(`
        <div style="min-width: 200px; padding: 8px;">
          <h3 style="margin: 0 0 8px 0; font-size: 16px;">Job #${job.id} | ${job.dispatch_customers?.[0]?.customer?.name}</h3>
          <p style="margin: 0 0 8px 0;">
            <strong>Address:</strong> ${Est.getFullAddress(cust?.customer_properties?.[0])}
          </p>
          <p style="margin: 0 0 8px 0;">
            <strong>Position:</strong> ${custGeo[0].toFixed(6)}, ${custGeo[1].toFixed(6)}
          </p>
        </div>
      `);

          mk.addListener('click', () => {
            infoWindow.close();
            infoWindow.open({
              anchor: mk,
              map,
            });
          });
          jobMarkers.push(mk);
        });

        const groups = groupByDateTech(scheduledJobs);
        console.log('[groups]', groups);

        const selectedDriverId = $('#driverSelect').val();
        console.log('[selected Driver]', selectedDriverId, employeeMapById[selectedDriverId]);

        let empLat = null, empLong = null;
        if (selectedDriverId) {
          const emp = employeeMapById[selectedDriverId];
          if (emp) {
            empLat = emp.user_profile.home_lat
            empLong = emp.user_profile.home_lng
          }
        }

        Object.keys(groups).forEach(function (key, idx) {
          if (techVisibility[key] !== true) return;
          const groupJobs = groups[key];
          if (groupJobs.length < 2) return;
          const renderer = new google.maps.DirectionsRenderer({
            suppressMarkers: true,
            polylineOptions: {
              strokeColor: ROUTE_COLORS[idx % ROUTE_COLORS.length],
              strokeWeight: 4
            }
          });
          renderer.setMap(map);
          directionsRenderers.push(renderer);
          console.log('[requestOptimizedRoute]', groupJobs);
          requestOptimizedRoute(groupJobs, renderer, empLat, empLong);
        });
      },
    }
  }();
</script>
