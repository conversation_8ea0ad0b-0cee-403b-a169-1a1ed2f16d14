<?php
namespace Butler\Controllers\api;

use Butler\Lib\Auth\AuthMiddleware;
use <PERSON>\Lib\Auth\EmployeeAuthMiddleware;
use <PERSON>\Lib\Auth\TenantAuthMiddleware;
use Butler\Models\ButlerDB;
use Butler\Controllers\api\ApiBaseController;
use Butler\Models\Tenant;
use Butler\Models\Dispatch\Dispatch;
use Butler\Models\Dispatch\DispatchOrg;
use Butler\Models\Dispatch\DispatchUser;
use Butler\Models\Dispatch\DispatchMessage;
use Butler\Models\Dispatch\DispatchAppointment;
use Butler\Models\Dispatch\DispatchCoverage;
use Butler\Models\Dispatch\DispatchCoverageNote;
use Butler\Models\Dispatch\DispatchComment;
use Butler\Models\Dispatch\DispatchNcc;
use Butler\Models\Dispatch\DispatchCommentAttachment;
use Butler\Models\Dispatch\DispatchStatus;
use Butler\Models\Dispatch\DispatchCustomer;
use Butler\Models\Dispatch\DispatchCustomerProperty;
use Butler\Models\Dispatch\DispatchMeta;
use <PERSON>\Models\Customer\Customer;
use Butler\Models\Customer\CustomerPhone;
use Butler\Models\Customer\CustomerProperty;
use Butler\Lib\FrontDoor\FrontDoorInBound;
use Butler\Helpers\Func;
use PDO;

class JobsApi extends ApiBaseController {
    public function searchDispatches() {
        header('Content-Type: application/json');
        
        $auth = TenantAuthMiddleware::authenticate();
        $tenantId = $auth['cur_tenant_id'];        
        $date = $_GET['date'] ?? null;
        $startDate = $_GET['startDate'] ?? null;
        $endDate = $_GET['endDate'] ?? null;
        $job_status = $_GET['jobStatus'] ?? null;
        $job_status = $job_status == null ? [
            DispatchStatus::STATUS_JOB_ASSIGNED,
            DispatchStatus::STATUS_JOB_ACCEPTED
            ] : explode(',', $job_status);
        $assigned_user = $_GET['employeeId'] ?? null;
        if ($assigned_user != "all" && $assigned_user != "unassigned" && $assigned_user != null) {            
            $assigned_user = explode(',', $assigned_user);
        }
        $appointment_status = $_GET['appointment_status'] ?? null;
        $appointment_status = $appointment_status == null ? null : explode(',', $appointment_status);
        
        try {
            $dispatches = Dispatch::searchDispatches(
                $tenantId,
                $assigned_user,
                $date,
                $startDate,
                $endDate,
                $job_status,
                $appointment_status,
                $_GET
            );

            echo json_encode($dispatches);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function getJobs() {
        header('Content-Type: application/json');
        
        $startTime = $_GET['startTime'] ?? null;
        $endTime = $_GET['endTime'] ?? null;                
        $employeeId = $_GET['employee_id'] ?? '';        
        $assigned = $_GET['assigned'] ? true : false;        
        
        try {
            $dispatches = Dispatch::getUnassignedWithRelations(
                $this->tenant_id, 
                null, 
                $employeeId, 
                $startTime, 
                $endTime,
                false,
                $assigned                
            );

            echo json_encode($dispatches);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function getEmployeeJobs() {
        header('Content-Type: application/json');
    
        $tenantId = $this->tenant_id;   
        $employeeId = $this->data['employee_id'] ?? null;
        $employeeId = explode(',', $employeeId);
        $startDate = $this->data['startTime'] ?? null;
        $endDate = $this->data['endTime'] ?? null;   
        $job_status = [
            DispatchStatus::STATUS_JOB_ACCEPTED,
            DispatchStatus::STATUS_JOB_IN_PROGRESS,
            DispatchStatus::STATUS_JOB_ON_HOLD
        ];
        
        try {
            $dispatches = Dispatch::searchDispatches(
                $tenantId,
                $employeeId,
                null,
                $startDate,
                $endDate,
                $job_status,
                null,
                $this->data
            );

            echo json_encode($dispatches);
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }        
    }

    public function assignJob() {
        header('Content-Type: application/json');
        $auth = TenantAuthMiddleware::authenticate();
        $tenantId = $auth['cur_tenant_id'];
        $data = $_POST;
        
        try {
            $dispatch = Dispatch::find($data['job_id']);
            $dispatchOrg = DispatchOrg::find($dispatch->source_org_id);
            $tenant = Tenant::find($tenantId);

            $ds = DispatchStatus::query();
            $ds->where('dispatch_id', $data['job_id']);
            $dispatchStatus = $ds->first();
            
            if ($dispatchStatus) {
                if ($dispatchStatus->job_status == DispatchStatus::STATUS_JOB_ASSIGNED) {
                    echo json_encode([
                        'success' => false, 
                        'message' => "Job is currently assigned. Please accept the job before assigning it."
                    ]);
                    return; // Stop execution here
                }
                
                $dispatchStatus->dispatch_id = $data['job_id'];
                $dispatchStatus->job_status = DispatchStatus::STATUS_JOB_IN_PROGRESS; // Job Accepted
                $dispatchStatus->appointment_status = DispatchStatus::STATUS_APPOINTMENT_INIT_SET; // Initial Appointment Set
                $dispatchStatus->save();
            } else {
                DispatchStatus::create([
                    'dispatch_id' => $data['job_id'],
                    'job_status' => DispatchStatus::STATUS_JOB_IN_PROGRESS,
                    'appointment_status' => DispatchStatus::STATUS_APPOINTMENT_INIT_SET
                ]);
            }

            $dispatchAssign = DispatchUser::addOrUpdateDispatchUser($data['job_id'], $data['assigned_techs']);
            $appointmentCreate = DispatchAppointment::addOrUpdateAppointment(
                $data['job_id'], 
                $data['assigned_techs'], 
                $data['appointment_set'], 
                null, 
                null, 
                null
            );
            
            // $frontdoor = new FrontDoorInBound($tenantId);
            // $requestBody = [
            //     "data" => [
            //         [
            //             "type" => "status",
            //             "object" => [
            //                 "source" => $tenant->source,
            //                 "tenant" => $dispatchOrg->external_id,
            //                 "dispatch_id" => $dispatch->external_id,
            //                 "vendor_id" => $tenant->external_id,
            //                 "description" => "Initial Appointment Scheduled",
            //                 "status_code" => "30",
            //                 "note" => "Butler assigned this job.",
            //                 "updated_at" => gmdate('Y-m-d\TH:i:s.000\Z'),
            //                 "start_time" => gmdate('Y-m-d\TH:i:s.000\Z'),
            //                 "end_time" => gmdate('Y-m-d\TH:i:s.000\Z')                            
            //             ]
            //         ]
            //     ]
            // ];
            // $result = $frontdoor->callApi('dispatch-connector/v1/webhook', 'POST', [], $requestBody);

            echo json_encode([
                'success' => true, 
                'data' => $dispatchAssign
            ]);
            return;
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function acceptDeclineJob() {
        header('Content-Type: application/json');
        $auth = TenantAuthMiddleware::authenticate();
        $tenantId = $auth['cur_tenant_id'];
        $data = $_POST;
        
        try {
            $dispatch = Dispatch::find($data['job_id']);
            $dispatchOrg = DispatchOrg::find($dispatch->source_org_id);
            $tenant = Tenant::find($tenantId);
            $dispatchStatus = DispatchStatus::query();
            $dispatchStatus->where('dispatch_id', $data['job_id']);
            $dispatchStatus = $dispatchStatus->first();
            
            if ($data['action'] == 'accept') {
                if ($dispatchStatus) {                
                    $dispatchStatus->dispatch_id = $data['job_id'];
                    $dispatchStatus->job_status = DispatchStatus::STATUS_JOB_ACCEPTED; // Job Accepted
                    $dispatchStatus->save();

                    // $frontdoor = new FrontDoorInBound($tenantId);
                    // $requestBody = [
                    //     "data" => [
                    //         [
                    //             "type" => "status",
                    //             "object" => [
                    //                 "source" => $tenant->source,
                    //                 "tenant" => $dispatchOrg->external_id,
                    //                 "dispatch_id" => $dispatch->external_id,
                    //                 "vendor_id" => $tenant->external_id,
                    //                 "description" => "Initial Appointment Scheduled",
                    //                 "status_code" => "30",
                    //                 "note" => "Butler assigned this job.",
                    //                 "updated_at" => gmdate('Y-m-d\TH:i:s.000\Z'),
                    //                 "start_time" => gmdate('Y-m-d\TH:i:s.000\Z'),
                    //                 "end_time" => gmdate('Y-m-d\TH:i:s.000\Z')                            
                    //             ]
                    //         ]
                    //     ]
                    // ];
                    // $result = $frontdoor->callApi('dispatch-connector/v1/webhook', 'POST', [], $requestBody);

                    echo json_encode([
                        'success' => true, 
                        'message' => "accepted",                         
                    ]);                    
                } else {
                    echo json_encode([
                        'success' => false, 
                        'message' => "Not found dispatch",                         
                    ]);                    
                }
            } elseif ($data['action'] == 'decline') {
                if ($dispatchStatus) {                
                    $dispatchStatus->dispatch_id = $data['job_id'];
                    $dispatchStatus->job_status = DispatchStatus::STATUS_JOB_CANCELED; // Job Canceled
                    $dispatchStatus->save();

                    // TODO:  FrontDoor Integration

                    echo json_encode([
                        'success' => true, 
                        'message' => "declined",                         
                    ]);

                } else {
                    echo json_encode([
                        'success' => false, 
                        'message' => "Not found Dispatch",                         
                    ]);
                }
                
            } else {
                echo json_encode([
                    'success' => false, 
                    'message' => "No given action", 
                ]);
            }            
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }        
    }

    public function getExitGarageJobs() {
        header('Content-Type: application/json');
        $tenantId = $this->tenant_id;

        try {
            $parkingDispatches = Dispatch::searchParkingDispatches(
                $tenantId,
            );
            echo json_encode($parkingDispatches);
        } catch (PDOException $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
        }
    }
    
    public function updateJob() {
        header('Content-Type: application/json');
        
        // Get JSON data from request body
        $data = json_decode(file_get_contents('php://input'), true);
        $type = $data['type'] ?? null;
        
        try {
            switch ($type) {
                case 'sequence':
                    $dispatchStatus = DispatchStatus::where('dispatch_id', $data['job_id'])->first();
                    if ($dispatchStatus) {
                        if ($dispatchStatus->job_status == DispatchStatus::STATUS_JOB_ASSIGNED) {
                            echo json_encode(['success' => false, 'message' => 'Job is not accepted.']);
                            return;
                        }                                                    
                    }


                    $qb = DispatchAppointment::query();
                    $qb->where('dispatch_id', $data['job_id']);
                    $appointment = $qb->first();
                    
                    if ($appointment) {                        
                        $appointment->stop_sequence = $data['stop_sequence'];
                        $appointment->appointment_date = $data['appointment_date'];
                        $appointment->user_id = $data['assigned_tech'];
                        $appointment->save();
                    } else {                        
                        DispatchAppointment::create([
                            'dispatch_id' => $data['job_id'],
                            'user_id' => $data['assigned_tech'] ?? 0,
                            'stop_sequence' => $data['stop_sequence'],
                            'appointment_date' => $data['appointment_set'] ?? date('Y-m-d')
                        ]);
                    }

                    $du = DispatchUser::query();
                    $du->where('dispatch_id', $data['job_id']);
                    $dispatchUser = $du->first();
                    
                    if ($dispatchUser) {
                        $dispatchUser->user_id = $data['assigned_tech'];
                        $dispatchUser->save();
                    } else {
                        DispatchUser::create([
                            'dispatch_id' => $data['job_id'],
                            'user_id' => $data['assigned_tech'] ?? 0
                        ]);
                    }

                    $ds = DispatchStatus::query();
                    $ds->where('dispatch_id', $data['job_id']);
                    $dispatchStatus = $ds->first();
                    if($dispatchStatus) {
                        if ($dispatchStatus->job_status == null) {
                            $dispatchStatus->job_status = DispatchStatus::STATUS_JOB_IN_PROGRESS;
                        }    
                        if ($dispatchStatus->appointment_status == null) {
                            $dispatchStatus->appointment_status = DispatchStatus::STATUS_APPOINTMENT_INIT_SET;
                        }                        
                        $dispatchStatus->save();
                    } else {
                        DispatchStatus::create([
                            'dispatch_id' => $data['job_id'],
                            'job_status' => DispatchStatus::STATUS_JOB_IN_PROGRESS, // Job In Progress
                            'appointment_status' => DispatchStatus::STATUS_APPOINTMENT_INIT_SET // Initial Appointment Set
                        ]);
                    }
                    // TODO: implement frontdoor integration
                    break;
                case 'remove_appointment':
                    $qb = DispatchAppointment::query();
                    $qb->where('dispatch_id', $data['job_id']);
                    $appointment = $qb->delete();

                    $du = DispatchUser::query();
                    $du->where('dispatch_id', $data['job_id']);
                    $dispatchUser = $du->delete();
                    
                    $ds = DispatchStatus::query();
                    $ds->where('dispatch_id', $data['job_id']);
                    $dispatchStatus = $ds->first();
                    if($dispatchStatus) {
                        $dispatchStatus->job_status = DispatchStatus::STATUS_JOB_ACCEPTED;
                        $dispatchStatus->appointment_status = null;                        
                        $dispatchStatus->save();
                    } else {
                        DispatchStatus::create([
                            'dispatch_id' => $data['job_id'],
                            'job_status' => DispatchStatus::STATUS_JOB_ACCEPTED // Job Accepted
                        ]);
                    }
                    // TODO: implement frontdoor integration
                    break;
            }
            echo json_encode(['success' => true]);
            
        } catch (\Exception $e) {
            http_response_code(500);
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function updateAssignedTechs()
    {
        $auth = TenantAuthMiddleware::authenticate();
        $tenantId = $auth['cur_tenant_id'];
        $data = $_POST;
        $appointment_set = $data['appointment_set'] ?? null;

        try {
            $dispatchAssign = DispatchUser::addOrUpdateDispatchUser($data['job_id'], $data['assigned_techs']);
            $appointmentCreate = DispatchAppointment::addOrUpdateAppointment(
                $data['job_id'],
                $data['assigned_techs'],
                $appointment_set,
                null,
                null,
                null
            );
            $dispatch = Dispatch::find($data['job_id']);
            $dispatchStatus = $dispatch->dispatchStatus;

            if (!$dispatchStatus) {
                $dispatchStatus = new DispatchStatus();
                $dispatchStatus->dispatch_id = $data['job_id'];
            }

            $dispatchStatus->job_status = DispatchStatus::STATUS_JOB_IN_PROGRESS; // Job Accepted
            $dispatchStatus->appointment_status = DispatchStatus::STATUS_APPOINTMENT_INIT_SET; // Initial Appointment Set
            $dispatchStatus->save();

            $this->json(['success' => true, 'data' => $dispatchAssign]);
        } catch (\Exception $e) {
            http_response_code(500);
            $this->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function updateJobStatus() {        
        $tenantId = $this->tenant_id;
        $data = $this->data;

        try {
            $status = DispatchStatus::where('dispatch_id', $data['job_id'])->first();

            if ($status) {
                foreach($data['data'] as $key => $value) {                    
                    if (in_array($key, $status->getFillable())) {
                        $status->{$key} = $value;
                        if ($key == 'parking_status') {
                            if ($value == 'driving') {
                                $status->job_status = DispatchStatus::STATUS_JOB_IN_PROGRESS;
                            } else if ($value == 'parked') {
                                $status->job_status = DispatchStatus::STATUS_JOB_ON_HOLD;
                            }
                        }
                    }
                }
                $status->save();
                $this->json(['success' => true, 'message' => "updated successfully"]);
            } else {                
                $newStatus = new DispatchStatus();
                $newStatus->dispatch_id = $data['job_id'];
                
                foreach($data['data'] as $key => $value) {
                    if (in_array($key, $newStatus->getFillable())) {
                        $newStatus->{$key} = $value;
                    }
                }
                
                $newStatus->save();
                $this->json(['success' => true, 'message' => "created successfully"]);
            }
            
        } catch (\Exception $e) {
            http_response_code(500);
            $this->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function getCustomerJobs() {
        $tenantId = $this->tenant_id;
        $data = $this->data;

        try {
            $query = Dispatch::whereHas('dispatchCustomers', function($query) use ($data) {
                $query->where('customer_id', $data['customer_id']);
            })->where('tenant_id', $tenantId)
              ->whereHas('dispatchStatus', function($query) {
                  $query->where('job_status', '!=', DispatchStatus::STATUS_JOB_ASSIGNED);                        
              })
              ->with([
                'dispatchStatus',
                'dispatchAppointment',
                'dispatchCustomers.customer',
                'dispatchItems'
              ]);
            
            // Get the full SQL with bindings replaced
            $sql = \Butler\Models\BaseModel::getFullSql($query);
            
            // Execute the query
            $dispatches = $query->get();
            
            $this->json([
                'success' => true, 
                'data' => $dispatches,
                'sql' => $sql
            ]);
        } catch (\Exception $e) {
            http_response_code(500);
            $this->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function createNewJob() {
        $tenantId = $this->tenant_id;
        $data = $this->data;

        try {
            $tenant = Tenant::find($tenantId);
            $org = DispatchOrg::where('external_id', $tenant->source)->first();

            if (!$org) {
                $org = DispatchOrg::create([
                    'external_id' => $tenant->source
                ]);
            }

            $dispatch = Dispatch::create([
                'tenant_id' => $tenantId,
                'source_org_id' => $org->id,
                'external_id' => "",
                'dispatchType' => "",
                'trade' => "",
                'priority' => "",
                'date' => $data['appointment_date'],
                'isAuthoRequired' => ""
            ]);

            if ($data['new_customer'] == 'yes' && $data['customer_name']) {
                $customer = Customer::create([
                    'external_id' => "",
                    'name' => $data['customer_name'],
                    'email' => "",
                    'preferredCommunicationType' => ""
                ]);

                CustomerPhone::create([
                    'customer_id' => $customer->id,
                    'phone' => $data['phone_number'],
                    'phone_type' => "Mobile"
                ]);

                $property = CustomerProperty::create([
                    'customer_id' => $customer->id,
                    'external_id' => "",
                    'city' => $data['city'],
                    'state' => $data['state'],
                    'streetDirection' => "",
                    'streetName' => $data['address'],
                    'streetNumber' => "",
                    'unitNumber' => "",
                    'unitType' => "",
                    'zip' => $data['zip'],
                    'zipFour' => ""
                ]);

                DispatchCustomerProperty::create([
                    'dispatch_id' => $dispatch->id,
                    'customer_id' => $customer->id,
                    'property_id' => $property->id
                ]);
            } else {
                $customer = Customer::find($data['customer_id']);
            }

            DispatchCustomer::create([
                'dispatch_id' => $dispatch->id,
                'customer_id' => $customer->id
            ]);

            if ($data['assigned_tech'] != 'unassigned') {
                DispatchUser::create([
                    'dispatch_id' => $dispatch->id,
                    'user_id' => $data['assigned_tech']
                ]);

                if ($data['appointment_date']) {
                    DispatchAppointment::create([
                        'dispatch_id' => $dispatch->id,
                        'user_id' => $data['assigned_tech'],
                        'appointment_date' => $data['appointment_date'],
                        'appointment_time' => $data['appointment_time'],
                        'stop_sequence' => 1,
                        'frontdoor_appointment' => ""
                    ]);

                    DispatchStatus::create([
                        'dispatch_id' => $dispatch->id,
                        'job_status' => DispatchStatus::STATUS_JOB_IN_PROGRESS, // Job In Progress
                        'appointment_status' => DispatchStatus::STATUS_APPOINTMENT_INIT_SET // Initial Appointment Set
                    ]);
                    
                } else {
                    DispatchStatus::create([
                        'dispatch_id' => $dispatch->id,
                        'job_status' => DispatchStatus::STATUS_JOB_ACCEPTED, // Job Accepted
                    ]);
                }
            } else {
                DispatchStatus::create([
                    'dispatch_id' => $dispatch->id,
                    'job_status' => DispatchStatus::STATUS_JOB_ACCEPTED, // Job Accepted                      
                ]);
            }

            $coverage = DispatchCoverage::create([
                'dispatch_id' => $dispatch->id,
                'header' => 'Direct Customer Call' . ($data['new_customer'] == 'yes' ? ' (New)' : ''),
                'content' => []
            ]);

            if ($data['job_notes']) {
                DispatchCoverageNote::create([
                    'dispatch_id' => $dispatch->id,                
                    'coverage_id' => $coverage->id,                
                    'note' => $data['job_notes']
                ]);
            }

            $this->json([
                'success' => true, 
                'data' => $dispatch,                
                'message' => "Job created successfully"
            ]);
        } catch (\Exception $e) {
            http_response_code(500);
            $this->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }

    public function updateJobMeta() {
        $data = $this->data;
        $auth = Func::getAuthInContainer();

        try {
            $dispatch = Dispatch::find($data['job_id']);
            $exclude = [                
                // 'parking_tech_sr_need',
                'parking_parts_home_warranty',
                // 'parking_parts_fast_response',
                // 'parking_auth_required',
                'parking_auth_denied_clam',
                'parking_unpaid_ncc',
                'parking_service_do_not_service',
                // 'parking_appointment_customer_missed',
            ];
            
            foreach ($data['data'] as $key => $value) {                
                if ($auth['user_type'] == 'employee' && in_array($key, $exclude)) {
                    continue;
                }
                DispatchMeta::updateOrCreate(
                    ['dispatch_id' => $dispatch->id, 'key' => $key],
                    ['value' => $value]
                );

                if ($key == DispatchMeta::PARKING_APPOINTMENT_CUSTOMER_MISSED && $value == '1') {
                    DispatchStatus::updateOrCreate(
                        ['dispatch_id' => $dispatch->id],
                        ['appointment_status' => DispatchStatus::STATUS_APPOINTMENT_CUSTOMER_MISSED]
                    );
                }
            }

            $this->json(['message' => "updated successfully"]);
        } catch (\Exception $e) {
            http_response_code(500);
            $this->json(['success' => false, 'message' => $e->getMessage()]);
        }

    }

    public function getJobMeta() {        
        $data = $this->data;
        $job_id = $data['job_id'];        

        try {
            $dispatchMeta = Dispatch::where('dispatch_id', $job_id)->get();

            $this->json(['data' => $dispatchMeta]);
        } catch (\Exception $e) {
            http_response_code(500);
            $this->json(['success' => false, 'message' => $e->getMessage()]);
        }

    }
}
