<?php

/**
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Accounts
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

namespace Twilio\Rest\Accounts\V1;

use Twilio\Exceptions\TwilioException;
use Twilio\ListResource;
use Twilio\Version;
use Twilio\InstanceContext;
use Twilio\Rest\Accounts\V1\Credential\AwsList;
use Twilio\Rest\Accounts\V1\Credential\PublicKeyList;


/**
 * @property AwsList $aws
 * @property PublicKeyList $publicKey
 * @method \Twilio\Rest\Accounts\V1\Credential\PublicKeyContext publicKey(string $sid)
 * @method \Twilio\Rest\Accounts\V1\Credential\AwsContext aws(string $sid)
 */
class CredentialList extends ListResource
    {
    protected $_aws = null;
    protected $_publicKey = null;

    /**
     * Construct the CredentialList
     *
     * @param Version $version Version that contains the resource
     */
    public function __construct(
        Version $version
    ) {
        parent::__construct($version);

        // Path Solution
        $this->solution = [
        ];
    }

    /**
     * Access the aws
     */
    protected function getAws(): AwsList
    {
        if (!$this->_aws) {
            $this->_aws = new AwsList(
                $this->version
            );
        }
        return $this->_aws;
    }

    /**
     * Access the publicKey
     */
    protected function getPublicKey(): PublicKeyList
    {
        if (!$this->_publicKey) {
            $this->_publicKey = new PublicKeyList(
                $this->version
            );
        }
        return $this->_publicKey;
    }

    /**
     * Magic getter to lazy load subresources
     *
     * @param string $name Subresource to return
     * @return \Twilio\ListResource The requested subresource
     * @throws TwilioException For unknown subresources
     */
    public function __get(string $name)
    {
        if (\property_exists($this, '_' . $name)) {
            $method = 'get' . \ucfirst($name);
            return $this->$method();
        }

        throw new TwilioException('Unknown subresource ' . $name);
    }

    /**
     * Magic caller to get resource contexts
     *
     * @param string $name Resource to return
     * @param array $arguments Context parameters
     * @return InstanceContext The requested resource context
     * @throws TwilioException For unknown resource
     */
    public function __call(string $name, array $arguments): InstanceContext
    {
        $property = $this->$name;
        if (\method_exists($property, 'getContext')) {
            return \call_user_func_array(array($property, 'getContext'), $arguments);
        }

        throw new TwilioException('Resource does not have a context');
    }

    /**
     * Provide a friendly representation
     *
     * @return string Machine friendly representation
     */
    public function __toString(): string
    {
        return '[Twilio.Accounts.V1.CredentialList]';
    }
}
