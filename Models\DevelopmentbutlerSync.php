<?php

namespace Butler\Models;


use Illuminate\Database\Eloquent\Builder;

/**
 * @property integer $id
 * @property integer $tenant_id
 * @property string $status
 * @property string $data
 * @property integer $page
 * @property string $created_at
 *
 * @method  \Illuminate\Database\Query\Builder $tenantOnly
 * @property Tenant $tenant
 */
class DevelopmentbutlerSync extends BaseModel
{
    protected $table = 'developmentbutler_sync';
    protected $fillable = ['tenant_id', 'status', 'data', 'page', 'created_at'];

    protected $casts = [
        'data' => 'json',
    ];

    public function tenant()
    {
        return $this->belongsTo('Butler\Models\Tenant');
    }

}