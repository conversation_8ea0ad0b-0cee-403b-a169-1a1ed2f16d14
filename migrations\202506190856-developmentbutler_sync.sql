CREATE TABLE developmentbutler_sync (
	id INT UNSIGNED auto_increment NOT NULL PRIMARY KEY,
	status varchar(100) NULL,
	`data` LONGTEXT NULL,
	created_at DATETIME DEFAULT current_timestamp() NOT NULL,
	tenant_id INT UNSIGNED NOT NULL,	
	CONSTRAINT developmentbutler_sync_tenants_FK FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE ON UPDATE CASCADE
)
ENGINE=InnoDB
DEFAULT CHARSET=utf8mb4
COLLATE=utf8mb4_general_ci;


ALTER TABLE developmentbutler_sync CHANGE tenant_id tenant_id int(10) unsigned NOT NULL AFTER id;

ALTER TABLE dispatch_butler.developmentbutler_sync ADD page INT UNSIGNED NOT NULL;
ALTER TABLE dispatch_butler.developmentbutler_sync CHANGE page page INT UNSIGNED NOT NULL AFTER `data`;