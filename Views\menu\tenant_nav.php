<nav class="bg-[#004d40]">
    <div class="w-full flex flex-wrap items-center justify-between mx-auto p-4">
        <div class="hidden w-full md:block md:w-auto" id="navbar-default">
            <ul class="font-medium flex flex-col p-4 md:p-0 mt-4 border border-gray-100 rounded-lg md:flex-row md:space-x-8 rtl:space-x-reverse md:mt-0 md:border-0">
                <li>
                    <a href="<?php echo BASE_URL . '/tenant/dashboard'; ?>"
                       class="block py-2 px-3 <?php echo $menu === 'home' ? 'text-[#26a69a] md:hover:text-[#26a69a]' : 'text-white'; ?>  md:p-0"
                       aria-current="page">Home</a>
                </li>
                <!-- <li>
                    <div class="relative group">
                        <div class="block py-2 px-3 <?php echo $menu === 'dispatch' ? 'text-[#26a69a] md:hover:text-[#26a69a]' : 'text-white'; ?> md:border-0 md:hover:text-[#26a69a] md:p-0 flex items-center">
                            Dispatch
                            <svg class="w-2.5 h-2.5 ml-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
                            </svg>
                        </div>
                        <div class="absolute hidden group-hover:block bg-white text-base z-50 list-none divide-y divide-gray-100 rounded shadow mt-0 mb-4">
                            <ul class="py-2 w-[200px]">
                                <li>
                                    <a href="<?php echo BASE_URL . '/Controllers/tenants/dispatch_console.php'; ?>"
                                       class="text-sm hover:bg-gray-100 text-gray-700 block px-4 py-2">Dispatch Console</a>
                                </li>
                                <li>
                                    <a href="<?php echo BASE_URL . '/Controllers/tenants/zone_dispatch_console.php'; ?>"
                                       class="text-sm hover:bg-gray-100 text-gray-700 block px-4 py-2">Zone Dispatch Console</a>
                                </li>
                                <li>
                                    <a href="<?php echo BASE_URL . '/Controllers/tenants/zone_list.php'; ?>"
                                       class="text-sm hover:bg-gray-100 text-gray-700 block px-4 py-2">Zones</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </li> -->
                <li>
                    <div class="relative group">
                        <div class="block py-2 px-3 <?php echo $menu === 'estimate' ? 'text-[#26a69a] md:hover:text-[#26a69a]' : 'text-white'; ?> md:border-0 md:hover:text-[#26a69a] md:p-0 flex items-center">
                            Estimates
                            <svg class="w-2.5 h-2.5 ml-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
                            </svg>
                        </div>
                        <div class="absolute hidden group-hover:block bg-white text-base z-50 list-none divide-y divide-gray-100 rounded shadow mt-0 mb-4">
                            <ul class="py-2 w-[200px]">
                                <li>
                                    <a href="<?php echo BASE_URL . 'tenant/estimates/getAll'; ?>"
                                       class="text-sm hover:bg-gray-100 text-gray-700 block px-4 py-2">Estimates</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="relative group">
                        <div class="block py-2 px-3 <?php echo $menu === 'pricebook' ? 'text-[#26a69a] md:hover:text-[#26a69a]' : 'text-white'; ?> md:border-0 md:hover:text-[#26a69a] md:p-0 flex items-center">
                            Price Book
                            <svg class="w-2.5 h-2.5 ml-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
                            </svg>
                        </div>
                        <div class="absolute hidden group-hover:block bg-white text-base z-50 list-none divide-y divide-gray-100 rounded shadow mt-0 mb-4">
                            <ul class="py-2 w-[200px]">
                                <li>
                                    <a href="<?php echo BASE_URL . 'tenant/pricebook/service'; ?>"
                                       class="text-sm hover:bg-gray-100 text-gray-700 block px-4 py-2">Services</a>
                                </li>
                                <li>
                                    <a href="<?php echo BASE_URL . 'tenant/pricebook/service-category'; ?>"
                                       class="text-sm hover:bg-gray-100 text-gray-700 block px-4 py-2">Service Category</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="relative group">
                        <div class="block py-2 px-3 <?php echo $menu === 'employee' ? 'text-[#26a69a] md:hover:text-[#26a69a]' : 'text-white'; ?> md:border-0 md:hover:text-[#26a69a] md:p-0 flex items-center">
                            Employees
                            <svg class="w-2.5 h-2.5 ml-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
                            </svg>
                        </div>
                        <div class="absolute hidden group-hover:block bg-white text-base z-50 list-none divide-y divide-gray-100 rounded shadow mt-0 mb-4">
                            <ul class="py-2 w-[200px]">
                                <li>
                                    <a href="<?php echo BASE_URL . 'tenant/employee'; ?>"
                                       class="text-sm hover:bg-gray-100 text-gray-700 block px-4 py-2">Employee List</a>
                                </li>
                                <li>
                                    <a href="<?php echo BASE_URL . 'tenant/employee/add'; ?>"
                                       class="text-sm hover:bg-gray-100 text-gray-700 block px-4 py-2">New Employee</a>
                                </li>
                                <li>
                                    <a href="<?php echo BASE_URL . 'tenant/role'; ?>"
                                       class="text-sm hover:bg-gray-100 text-gray-700 block px-4 py-2">Employee Roles List</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="relative group">
                        <div class="block py-2 px-3 <?php echo $menu === 'driver' ? 'text-[#26a69a] md:hover:text-[#26a69a]' : 'text-white'; ?> md:border-0 md:hover:text-[#26a69a] md:p-0 flex items-center">
                            Driver / Device
                            <svg class="w-2.5 h-2.5 ml-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
                            </svg>
                        </div>
                        <div class="absolute hidden group-hover:block bg-white text-base z-50 list-none divide-y divide-gray-100 rounded shadow mt-0 mb-4">
                            <ul class="py-2 w-[200px]">
                                <li>
                                    <a href="<?php echo BASE_URL . 'tenant/driver/device/list'; ?>"
                                       class="text-sm hover:bg-gray-100 text-gray-700 block px-4 py-2">Devices List</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="relative group">
                        <div class="block py-2 px-3 text-white md:border-0 md:hover:text-[#26a69a] md:p-0 flex items-center">
                            Integration
                            <svg class="w-2.5 h-2.5 ml-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
                            </svg>
                        </div>
                        <div class="absolute hidden group-hover:block bg-white text-base z-50 list-none divide-y divide-gray-100 rounded shadow mt-0 mb-4">
                            <ul class="py-2 w-[200px]">
                                <!-- <li>
                                    <a href="#" class="text-sm hover:bg-gray-100 text-gray-700 block px-4 py-2">OpenAI</a>
                                </li> -->
                                <li>
                                    <a href="<?php echo BASE_URL . 'tenant/integration/twilio'; ?>"
                                       class="text-sm hover:bg-gray-100 text-gray-700 block px-4 py-2">Twilio</a>
                                </li>
                                <!-- <li>
                                    <a href="#" class="text-sm hover:bg-gray-100 text-gray-700 block px-4 py-2">Google Map</a>
                                </li> -->
                            </ul>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="relative group">
                        <div class="block py-2 px-3 <?php echo $menu === 'settings' ? 'text-[#26a69a] md:hover:text-[#26a69a]' : 'text-white'; ?> md:border-0 md:hover:text-[#26a69a] md:p-0 flex items-center">
                            Settings
                            <svg class="w-2.5 h-2.5 ml-2.5" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 10 6">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 1 4 4 4-4"/>
                            </svg>
                        </div>
                        <div class="absolute hidden group-hover:block bg-white text-base z-50 list-none divide-y divide-gray-100 rounded shadow mt-0 mb-4">
                            <ul class="py-2 w-[200px]">
                                <li>
                                    <a href="<?php echo BASE_URL . 'tenant/address'; ?>"
                                       class="text-sm hover:bg-gray-100 text-gray-700 block px-4 py-2">Tenant Addresses</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</nav>