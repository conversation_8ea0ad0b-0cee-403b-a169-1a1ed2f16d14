body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(to right, #e0f7fa, #ffffff);
  margin: 0;
  padding: 0;
  /* display: flex; */
  flex-direction: column;
  height: 100vh;
  overflow-x: hidden;
}

header {
  background: #00796b;
  color: white;
  padding: 15px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 0.9em;
}

.logout-button {
  background: transparent;
  border: 1px solid white;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s;
}

.logout-button:hover {
  background: rgba(255,255,255,0.2);
}

/* Login Form */
.error { color: red; }

.login-form {
    max-width: 350px;
    margin: 0 auto;
}

.login-form button { 
  margin-top: 20px; 
  padding: 10px 20px; 
}


/* Kanban Board */
.header-filters{
  padding: 10px;
  background-color: #f0f0f0;
}

.header-filters button{
  margin-left: 5px;
}

.kanboard-container{
  padding: 10px;
  display: flex;
  flex-direction: row;
  overflow-x: auto;
  min-height: calc(100vh - 50px);
  box-sizing: border-box;
}

.kan-col{
  width: 350px;
  margin-right: 10px;
  background-color: #F0FFF0;
  border: 1px solid #ccc;
  display: flex;
  flex-direction: column;
}

.kan-col h3{
  margin: 0;
  padding: 10px;
  background-color: #e8e8e8;
  border-bottom: 1px solid #ccc;
}

.calls-container{
  flex: 1;
  padding: 10px;
  overflow-y: auto;
}

.zone-group{
  margin-bottom: 15px;
  border: 1px dashed #aaa;
  padding: 5px;
}

.zone-calls{
  padding-left: 10px;
}

.call-card{  
  margin-bottom: 5px;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
}

.call-card.dimmed{
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.edit-btn{
  position: absolute;
  top: 5px;
  right: 5px;
  background: #ccc;
  border: none;
  padding: 3px 6px;
  cursor: pointer;
  font-size: 12px;
  border-radius: 3px;
}

.map-popup{
  position: absolute;
  top: 120px;
  left: 20px;
  width: 500px;
  height: 400px;
  border: 1px solid #ccc;
  background: #fff;  
  box-sizing: border-box;
}

.map-popup-header{
  background: #ddd;
  padding: 5px;
  cursor: move;
  user-select: none;
}

.map-popup-body{
  width: 100%;
  height: calc(100% - 28px);
  position: relative;
  box-sizing: border-box;
  overflow: hidden;
}

#map{
  width: 100%;
  height: 100%;
}

.map-popup-resizer{
  position: absolute;
  width: 15px;
  height: 15px;
  right: 0;
  bottom: 0;
  background: #666;
  cursor: se-resize;
  user-select: none;
}

.distance-label{
  position: absolute;
  top: 0;
  left: 0;
  padding: 4px;
  background: rgba(255,255,255,0.8);
  font-weight: bold;
}

.modal-backdrop{
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.4);
  z-index: 99999;
}

.modal-backdrop.active{
  display: block;
}

.modal-content{
  width: 400px;
  background: #fff;
  margin: 100px auto;
  padding: 15px;
  border-radius: 5px;
  position: relative;
}

.modal-close{
  position: absolute;
  top: 5px;
  right: 10px;
  cursor: pointer;
  font-weight: bold;
}

.modal-content h3{
  margin-top: 0;
}

.modal-row{
  margin-bottom: 10px;
}

.modal-row label{
  display: inline-block;
  width: 80px;
  font-weight: bold;
}

.status-indicator{
  display: inline-block;
  width: 15;
  height: 15px;
  border-radius: 50%;
  background-color: #ccc;
  border: 1px solid #999;
  margin-right: 4px;
}

@keyframes flash{
  0%, 100%{opacity: 1;}
  50%{opacity: 0;}
}

.status-indicator.flash{
  animation: flash 1s infinite;
}

.card-details{
  transition: max-height 0.3s ease, opacity 0.3s ease;
}

.call-card.collapsed .card-details{
  display: none;
  opacity: 0;
}

.tech-group{
  background-color: #fafafa;
  border: 1px solid #ddd;
  margin-bottom: 5px;
  padding: 5px;
  cursor: move;
}

.tech-group.ghost{
  opacity: 0.7;
  box-shadow: 2px 2px 5px rgba(0,0,0,0.5);
}

#inboundSmsBar{
  position: fixed;
  top: 165px;
  right: 0;
  width: 3in;
  height: calc(100% - 50px);
  background: #fff;
  overflow-y: auto;
  z-index: 10000;
  box-shadow: -2px 0 5px rgba(0,0,0,0.2);
  border-left: 1px solid #ccc;
}

#inboundSmsBar h3{
  margin: 0;
  padding: 10px;
  background: #f0f0f0;
  border-bottom: 1px solid #ccc;
  font-size: 16px;
  text-align: center;
}

#inboundSmsMessages{
  padding: 10px;
}

#smsControls{
  padding: 5px;
  text-align: center;
}

#smsControls input[type="text"]{
  width: 70%;
  padding: 3px;
  font-size: 12px;
}

#smsControls button{
  padding: 3px 5px;
  font-size: 12px;
}

#smsPagination{
  text-align: center;
  padding: 5px;
  border-top: 1px solid #ccc;
}

#smsPagination button{
  padding: 3px 5px;
  font-size: 12px;
}

#inboundSmsMessages{
  padding: 10px;
}
