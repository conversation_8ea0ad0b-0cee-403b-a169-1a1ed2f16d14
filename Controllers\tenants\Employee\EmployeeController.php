<?php

namespace Butler\Controllers\tenants\Employee;

use Butler\Controllers\tenants\TenantBaseController;
use Butler\Helpers\SysMsg;
use Butler\Models\Driver\DriverDevice;
use Butler\Models\Driver\UserDevice;
use Butler\Models\TenantRole;
use Butler\Models\User;
use Butler\Models\UserType;
use Exception;

class EmployeeController extends TenantBaseController
{
    public function __construct()
    {
        parent::__construct();

        $this->controllerBaseUrl = rtrim(BASE_URL, '/') . '/tenant/employee';
        $this->templatePath = $this->templatePath . DS . 'employee';
        $this->menu = 'employee';
    }

    public function index()
    {
        $this->title = "Employee List";

        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $limit = 10; // Items per page
        $offset = ($page - 1) * $limit;


        $qb = User::query();
        $qb->offset($offset)->limit($limit);
        $qb->employee($this->tenant_id);
        $qb->with('driverDevice');
        $qb->with('tenantRoles');

        $total_records = $qb->count();
        $total_pages = ceil($total_records / $limit);
        $rows = $qb->get()->toArray();

        // Add pagination data to be used in the view
        $pagination = [
            'current_page' => $page,
            'total_pages' => $total_pages,
            'limit' => $limit,
            'total_records' => $total_records
        ];

        $this->render('employee_list', compact('pagination', 'rows'));
    }

    public function add()
    {
        $this->title = "Add Employee";

        if (isset($_POST['user_email'])) {
            if (!($_POST['user_id'] ?? null) || !($_POST['user_email'] ?? null) || !($_POST['user_type'] ?? null)) {
                SysMsg::get_instance()->error("Please fill in required fields.");
            } else {
                $valid = true;
                $password = $_POST['password'];
                $passwordConfirmed = $_POST['password_confirmed'];

                if (User::query()->where('user_email', $_POST['user_email'])->where('user_id', $_POST['user_id'])->exists()) {
                    SysMsg::get_instance()->error("User Email or User ID already exists! Take another one!");
                    $valid = false;
                }

                if ($password !== $passwordConfirmed) {
                    SysMsg::get_instance()->error("Mismatched password.");
                    $valid = false;
                }


                if ($valid) {
                    $hashedPassword = password_hash($password, PASSWORD_BCRYPT);
                    $_POST['password'] = $hashedPassword;

                    try {
                        /** @var User $user */
                        $user = User::create($_POST);
                        $user->userTypes()->insert(['user_id' => $user->id, 'user_type' => UserType::USER_TYPE_EMPLOYEE, 'tenant_id' => $this->tenant_id]);
                        if (key_exists('device_id', $_POST)) {
                            $tmpRow = UserDevice::firstOrNew(['user_id' => $user->id]);
                            $tmpRow->device_id = $_POST['device_id'] ? $_POST['device_id'] : null;

                            $tmpRow->save();
                        }

                        if (key_exists('tenant_role_id', $_POST)) {
                            $user->tenantRoles()->sync([$_POST['tenant_role_id']]);
                        }

                        if (key_exists('user_profile', $_POST)) {
                            $profile = $user->userProfile()->firstOrNew(['user_id' => $user->id]);
                            $profile->fill($_POST['user_profile']);
                            $profile->save();
                        }

                        SysMsg::get_instance()->success("Added successfully.");

                        $this->redirect();
                    } catch (Exception $e) {
                        SysMsg::get_instance()->error("Could not add service: " . $e->getMessage());
                    }
                }
            }
        }

        $devices = DriverDevice::query()->where('tenant_id', $this->tenant_id)->pluck('display_name', 'id')->toArray();
        $roles = TenantRole::query()->tenantOnly($this->tenant_id)->pluck('role', 'id')->toArray();

        $this->render('employee_add', ['devices' => $devices, 'roles' => $roles]);
    }

    public function edit()
    {
        $this->title = "Update Employee";

        if (!isset($_GET['id'])) {
            SysMsg::get_instance()->error('Invalid request!');
            $this->redirect();
        }

        $id = intval($_GET['id']);

        /** @var User $user */
        $user = User::query()->employee($this->tenant_id)->where('id', $id)
            ->with('userProfile')
            ->with('driverDevice')
            ->with('tenantRoles')
            ->first();

        if (!$user) {
            SysMsg::get_instance()->error('Invalid request!');
            $this->redirect();
        }

        if (isset($_POST['user_email'])) {
            if (!($_POST['user_email'] ?? null) || !($_POST['user_type'] ?? null)) {
                SysMsg::get_instance()->error("Please fill in required fields.");
            } else {
                try {
                    $valid = true;
                    $password = $_POST['password'];
                    if ($password) {
                        if ($password !== $_POST['password_confirmed']) {
                            SysMsg::get_instance()->error("Mismatched password.");
                            $valid = false;
                        }
                    }

                    if (User::query()->where('id', '!=', $id)->where('user_email', $_POST['user_email'])->where('user_id', $user->id)->exists()) {
                        SysMsg::get_instance()->error("User Email or User ID already exists! Take another one!");
                        $valid = false;
                    }

                    if ($valid) {
                        if ($password) {
                            $hashedPassword = password_hash($password, PASSWORD_BCRYPT);
                            $_POST['password'] = $hashedPassword;
                        } else {
                            unset($_POST['password']);
                        }

                        $user->update($_POST);

                        if (key_exists('device_id', $_POST)) {
                            $tmpRow = UserDevice::firstOrNew(['user_id' => $user->id]);
                            $tmpRow->device_id = $_POST['device_id'] ? $_POST['device_id'] : null;

                            $tmpRow->save();
                        }

                        if (key_exists('tenant_role_id', $_POST)) {
                            $user->tenantRoles()->sync([$_POST['tenant_role_id']]);
                        }

                        if (key_exists('user_profile', $_POST)) {
                            $profile = $user->userProfile()->firstOrNew(['user_id' => $user->id]);
                            $profile->fill($_POST['user_profile']);
                            $profile->save();
                        }

                        SysMsg::get_instance()->success('Updated successfully!');
                        $this->redirect();
                    }
                } catch (Exception $e) {
                    SysMsg::get_instance()->error('Failed to save data. ' . $e->getMessage());
                }
            }
        }

        $devices = DriverDevice::query()->where('tenant_id', $this->tenant_id)->pluck('display_name', 'id')->toArray();
        $roles = TenantRole::query()->tenantOnly($this->tenant_id)->pluck('role', 'id')->toArray();
        $this->render('employee_edit', ['dbRow' => $user?->toArray(), 'id' => $id, 'devices' => $devices, 'roles' => $roles]);
    }


    public function delete()
    {
        if (!isset($_GET['id']) || !isset($_GET['h'])) {
            $this->redirect();
        }
        $id = intval($_GET['id']);
        if (md5("uid$id") != $_GET['h']) {
            $this->redirect();
        }

        try {
            User::query()->employee($this->tenant_id)->where('id', $id)->delete();
        } catch (Exception $e) {
            SysMsg::get_instance()->error('Failed to save data. ' . $e->getMessage());
        }

        if ('json' === $this->data['format'] ?? null) {
            $this->jsonWithSuccess(true);
        } else {
            SysMsg::get_instance()->success('Deleted successfully.');
            $this->redirect();
        }
    }
}
