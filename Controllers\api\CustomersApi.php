<?php
namespace Butler\Controllers\api;

use <PERSON>\Lib\Auth\AuthMiddleware;
use <PERSON>\Lib\Auth\EmployeeAuthMiddleware;
use <PERSON>\Lib\Auth\TenantAuthMiddleware;
use Butler\Models\ButlerDB;
use Butler\Controllers\api\ApiBaseController;
use Butler\Models\Dispatch\Dispatch;
use Butler\Models\Dispatch\DispatchOrg;
use Butler\Models\Dispatch\DispatchUser;
use Butler\Models\Dispatch\DispatchMessage;
use Butler\Models\Dispatch\DispatchAppointment;
use Butler\Models\Dispatch\DispatchCoverageNote;
use Butler\Models\Dispatch\DispatchComment;
use Butler\Models\Dispatch\DispatchNcc;
use Butler\Models\Customer\Customer;
use Butler\Models\Dispatch\DispatchCommentAttachment;
use Butler\Models\Dispatch\DispatchStatus;
use Butler\Lib\FrontDoor\FrontDoorInBound;
use Butler\Models\Tenant;
use PDO;

class CustomersApi extends ApiBaseController {
    public function getCustomer() {
        $customer_id = $_GET['customer_id'] ?? null;
        if (!$customer_id) {
            http_response_code(400);
            $this->json(['success' => false, 'message' => 'Customer ID is required']);
            return;
        }

        try {
            $tenant_id = $this->tenant_id;
            $customer = Customer::query()
                ->where('id', $customer_id)
                ->with([                    
                    'customerPhones',
                    'customerProperties',                    
                ])
                ->first();

            if (!$customer) {
                http_response_code(400);
                $this->json(['success' => false, 'message' => 'Customer not found']);
                return;
            }

            $this->json(['success' => true, 'data' => $customer]);

        } catch (\Exception $e) {
            http_response_code(500);
            $this->json(['success' => false, 'message' => $e->getMessage()]);
        }
    }
}
