/* Existing CSS */

body {
    margin: 0;
    font-family: Arial, sans-serif;
}

button {
    cursor: pointer
}

.header-filters {
    padding: 10px;
    background-color: #f0f0f0;
}

.header-filters button {
    margin-left: 5px;
}

.kanboard-container {

    padding: 10px;
    display: flex;
    flex-direction: row;

    overflow-x: auto;
    min-height: calc(100vh - 50px);
    box-sizing: border-box;

}

.kan-col {

    width: 350px;
    margin-right: 10px;
    background-color: #F0FFF0;

    border: 1px solid #ccc;
    display: flex;
    flex-direction: column;

}

.kan-col h3 {

    margin: 0;
    padding: 10px;
    background-color: #e8e8e8;

    border-bottom: 1px solid #ccc;

}

.calls-container {
    flex: 1;
    padding: 10px;
    overflow-y: auto;
}

.zone-group {
    margin-bottom: 15px;
    border: 1px dashed #aaa;
    padding: 5px;
}

.zone-calls {
    padding-left: 10px;
}

.call-card {
    margin-bottom: 5px;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    position: relative;
    transition: all 0.3s ease;
    overflow: hidden;
}

.call-card.dimmed {
    opacity: 0.5;
    transition: opacity 0.3s ease;
}

.edit-btn {

    position: absolute;
    top: 5px;
    right: 5px;
    background: #ccc;

    border: none;
    padding: 3px 6px;
    cursor: pointer;
    font-size: 12px;
    border-radius: 3px;

}

.map-popup {

    position: absolute;
    top: 120px;
    left: 20px;
    width: 500px;
    height: 400px;

    border: 1px solid #ccc;
    background: #fff;
    z-index: 5;
    box-sizing: border-box;

}

.map-popup-header {

    background: #ddd;
    padding: 5px;
    cursor: move;
    user-select: none;

}

.map-popup-body {

    width: 100%;
    height: calc(100% - 28px);
    position: relative;

    box-sizing: border-box;
    overflow: hidden;

}

#map {
    width: 100%;
    height: 100%;
}

.map-popup-resizer {

    position: absolute;
    width: 15px;
    height: 15px;
    right: 0;
    bottom: 0;

    background: #666;
    cursor: se-resize;
    user-select: none;

}

.distance-label {

    position: absolute;
    top: 0;
    left: 0;
    padding: 4px;

    background: rgba(255, 255, 255, 0.8);
    font-weight: bold;

}

/** Modal CSS
-----------------------------------------------------  */
.modal-backdrop {

    display: none;
    position: fixed;
    top: 0;
    left: 0;

    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: 45;

}

.modal-backdrop.active {
    display: block;
}

.modal-content {
    width: 400px;
    background: #fff;
    margin: 100px auto;
    padding: 15px;
    border-radius: 5px;
    position: relative;
    max-height: calc(100% - 200px);
    overflow-y: auto;
    overflow-x: hidden;
}

.modal-close {
    position: absolute;
    top: 5px;
    right: 10px;
    cursor: pointer;
    font-weight: bold;
}

.modal-content h3 {
    margin-top: 0;
}

.modal-row {
    margin-bottom: 10px;
}

.modal-row label {
    display: inline-block;
    width: 80px;
    font-weight: bold;
}

/** Modal CSS version 2
-----------------------------------------------------  */
.modal-content2 {
    width: 400px;
    background: #fff;
    margin: 100px auto;
    border-radius: 5px;
    position: relative;
    max-height: calc(100% - 200px);
    overflow-y: auto;
    overflow-x: hidden;
}

.modal-content2 .modal-close {
    top: 10px;
    right: 8px;
    font-size: 24px;
    width: 24px;
}

.modal-content2 .modal-header {
    height: 48px;
    padding: 10px 20px;
    line-height: 28px;
    border-bottom: 1px solid #eee;
}

.modal-content2 .modal-body {
    height: 100%;
    max-height: calc(100vh - 298px);
    overflow-y: auto;
    overflow-x: hidden;
    padding: 20px 20px 20px 20px;
    /*margin-bottom: 20px;*/
}

.modal-content2 .modal-footer {
    height: 48px;
    padding: 8px 20px;
    border-top: 1px solid #eee;
}


/* Status Lights Styles */

.status-lights {
    margin-top: 5px;
}

.status-indicator {

    display: inline-block;

    width: 20px;

    height: 20px;

    border-radius: 50%;

    background-color: #ccc;

    border: 1px solid #999;

    margin-right: 4px;

}

@keyframes flash {

    0%, 100% {
        opacity: 1;
    }

    50% {
        opacity: 0;
    }

}

.status-indicator.flash {

    animation: flash 1s infinite;

}

/* New CSS for collapsible job cards */

.card-details {

    transition: max-height 0.3s ease, opacity 0.3s ease;

}

.call-card.collapsed .card-details {

    display: none;

    opacity: 0;

}

.tech-group {

    background-color: #fafafa; /* light background for groups */

    border: 1px solid #ddd;

    margin-bottom: 5px;

    padding: 5px;

    cursor: move; /* indicate draggable */

}

.tech-group.ghost {

    opacity: 0.7;

    box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.5);

}

/* Adjusted SMS bar - moved down from the top */

#inboundSmsBar {

    position: fixed;

    top: 165px; /* Moved down by 50px */

    right: 0;

    width: 3in; /* 3 inches wide */

    height: calc(100% - 50px); /* Adjusted height */

    background: #fff;

    overflow-y: auto;

    z-index: 10000;

    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.2);

    border-left: 1px solid #ccc;

}

#inboundSmsBar h3 {

    margin: 0;

    padding: 10px;

    background: #f0f0f0;

    border-bottom: 1px solid #ccc;

    font-size: 16px;

    text-align: center;

}

#inboundSmsMessages {

    padding: 10px;

}

#smsControls {

    padding: 5px;

    text-align: center;

}

#smsControls input[type="text"] {

    width: 70%;

    padding: 3px;

    font-size: 12px;

}

#smsControls button {

    padding: 3px 5px;

    font-size: 12px;

}

#smsPagination {

    text-align: center;

    padding: 5px;

    border-top: 1px solid #ccc;

}

#smsPagination button {

    padding: 3px 5px;

    font-size: 12px;

}

#inboundSmsMessages {

    padding: 10px;

}


/* Container for toast notifications */

#toastContainer {

    position: fixed;

    bottom: 20px;

    right: 20px;

    z-index: 10000;

}

/* Style for individual toast messages */

.toast {

    background-color: #333;

    color: #796f6f;

    padding: 12px 20px;

    margin-top: 10px;

    border-radius: 4px;

    min-width: 250px;

    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

    position: relative;

    font-family: sans-serif;

    white-space: pre-wrap; /* Preserve line breaks */

}

/* Close button inside the toast */

.toast .close-btn {

    position: absolute;

    top: 4px;

    right: 8px;

    background: none;

    border: none;

    color: #fff;

    font-size: 16px;

    cursor: pointer;

}

/** Toast update
---------------------------------------------- */
.jq-toast-wrap {
    z-index: 102000 !important;
}