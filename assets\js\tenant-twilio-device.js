
let device;
let currentConnection = null;
let callTimer = null;
let callStartTime = null;
let activeJobId = null;

// Create a TwilioCallModule similar to EditAppointmentStatusModule
var TwilioCallModule = function () {
  const self = {
    device: null,
    currentConnection: null,
    callTimer: null,
    callStartTime: null,
    activeJobId: null,
    assignedNumber: null,
    customerId: null,
  };

  self.init = function() {
    // Initialize Twilio device
    this.connectTwilio();
    this.assignedNumber = assignedNumber;
    
    // Set up event handlers for outbound call modal
    $('#saveOutboundCallNotes').off('click').on('click', function() {
      const notes = $("#outboundCallNotes").val().trim();
      if (!notes) {
        self.showDialog("Please enter notes", "warning");
        return;
      }
      
      // Get the job ID from the appointment modal if it's open
      let jobId = null;
      if ($("#editAppointmentStatusModal2").hasClass("active")) {
        jobId = EditAppointmentStatusModule.job?.id;
      }
      
      if (!jobId) {
        self.showDialog("No job selected", "warning");
        return;
      }
      
      self.saveCallNotes(jobId, notes);
    });
    
    $('#hangupOutboundCall').off('click').on('click', function() {
      if (self.currentConnection) {      
        self.currentConnection.disconnect();
      }
    });
    
    // Set up event handlers for inbound call modal
    $("#saveInboundCallNotes").off('click').on('click', function() {
      if (self.activeJobId) {
        const notes = $("#inboundCallNotes").val();
        self.saveCallNotes(self.activeJobId, notes);
      } else {
        self.showDialog("Please select a job first", "warning");
      }
    });
    
    $("#updateInboundAppointmentStatus").off('click').on('click', function() {
      if (self.activeJobId) {
        const status = $("input[name='inbound_appointment_status']:checked").val();
        if (status) {
          self.updateJobStatus(self.activeJobId, status);
        } else {
          self.showDialog("Please select a status", "warning");
        }
      } else {
        self.showDialog("Please select a job first", "warning");
      }
    });
    
    $("#hangupInboundCall").off('click').on('click', function() {
      if (self.currentConnection) {
        self.currentConnection.disconnect();
        $("#inboundModal").addClass("hidden");
      }
    });
    
    $("#minimizeInboundCall").off('click').on('click', function() {
      // Implement minimize functionality
    });
    
    $("#closeInboundCall").off('click').on('click', function() {
      if (self.currentConnection) {
        self.currentConnection.disconnect();
      }
      $("#inboundModal").addClass("hidden");
    });
    
    $("#minimizeOutboundCall").off('click').on('click', function() {
      // Implement minimize functionality
    });

    // Initialize new job dialog
    self.initNewJobDialog();

  };

  self.connectTwilio = async function() {
    try {
      const res = await fetch(
        BASE_API_URL + '/twilio/twilio_access_token',
        { 
          credentials: 'same-origin',
          headers: {
            'Content-Type': 'application/json'
          }
        }
      );

      const { token } = await res.json();  
      self.device = new Twilio.Device(token, {
        codecPreferences: ['opus', 'pcmu'],
        debug: true
      });

      self.device.on('ready', () => {    
        console.log("Twilio Connected");
      });

      self.device.on('error', e => {
        console.error("Twilio Error:", e);
        self.showDialog("Twilio Error: " + e.message, "error");
      });

      self.device.on('offline', () => {
        console.log("Twilio Offline");
      });

      self.device.on('incoming', self.handleIncoming);

      self.device.on('connect', (connection) => {
        self.currentConnection = connection;
        const callSid = connection.parameters.CallSid;      
        console.log('Live Call SID:', callSid);
        
        // Start call timer
        self.startCallTimer();
        
        // Update UI for connected call
        if ($("#outboundCallModal").is(":visible")) {
          $("#outboundCallStatus").text("Connected");
        }
      });
      
      self.device.on('disconnect', () => {
        self.stopCallTimer();
        self.currentConnection = null;
        
        // Hide modals or update UI as needed
        $("#outboundCallModal").addClass("hidden");
        
        // Re-enable buttons in appointment modal
        $("div#editAppointmentStatusModal2 .modal-footer button").prop("disabled", false);
      });
      
      // Store the global device reference for backward compatibility
      device = self.device;
    } catch (error) {
      console.error("Error connecting to Twilio:", error);
      self.showDialog("Failed to connect to Twilio", "error");
    }
  };

  self.startCallTimer = function() {
    self.callStartTime = new Date();
    self.callTimer = setInterval(self.updateCallTimer, 1000);
    
    // Also update global variables for backward compatibility
    callStartTime = self.callStartTime;
    callTimer = self.callTimer;
  };

  self.stopCallTimer = function() {
    if (self.callTimer) {
      clearInterval(self.callTimer);
      self.callTimer = null;
      
      // Update global variable
      callTimer = null;
    }
  };

  self.updateCallTimer = function() {
    if (!self.callStartTime) return;
    
    const now = new Date();
    const diff = now - self.callStartTime;
    const minutes = Math.floor(diff / 60000);
    const seconds = Math.floor((diff % 60000) / 1000);
    
    const timeString = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    
    // Update timer display in both modals
    $("#inboundCallTimer").text(timeString);
    $("#outboundCallTimer").text(timeString);
  };

  self.handleIncoming = async function(call) {
    self.currentConnection = call;
    currentConnection = call; // For backward compatibility
    self.customerId = null;
    self.phoneNumber = call.parameters.From || 'Unknown';
    self.customerName = null;
    
    // Show the incoming call modal
    $("#inboundModal").removeClass("hidden");
    
    // Show incoming call controls, hide hangup button
    $("#incomingCallControls").show();
    $("#hangupInboundCall").hide();
    
    // Display the caller's phone number
    const phoneNumber = call.parameters.From || 'Unknown';    
    // Try to find customer information based on the phone number
    try {
      // Try to find customer information based on the phone number
      let data = null;
      
      App.ajax(
        BASE_API_URL + '/twilio/tenants/search_by_phone',
        { phone: phoneNumber },
        function(response) {
          data = response;

          $("#inboundCallNumber").text('');
          $("#inboundCallCustomerName").text('Unknown Customer');
          $("#inboundCallCustomerAddress").text('No address');
          $("#inboundCallRecentJobs").html('<p class="text-gray-500">No recent jobs found</p>');

          if (data && data.type == 'user') {
            const user = data.data;
            self.customerName = user.user_name || 'Unknown';
            $("#inboundCallCustomerName").text(user.user_name || 'Unknown');
            $("#inboundCallNumber").text(user.phone_number || phoneNumber);
            
            $("#inboundCallCustomerAddress").text('');
            
          } else if (data && data.type == 'customer') {
            const customer = data.data;
            self.customerId = customer.id;
            self.customerName = customer.name || 'Unknown';
            $("#inboundCallCustomerName").text(customer.name || 'Unknown');
            $("#inboundCallNumber").text(customer.phone || phoneNumber);
            // Format address if available
            
            let address = '';
            if (customer.properties && customer.properties.length > 0) {
              const prop = customer.properties[0];
              address = [prop.streetName, prop.city, prop.state, prop.zip]
                .filter(Boolean)
                .join(', ');
            }
            $("#inboundCallCustomerAddress").text(address || 'No address');
                        
            self.loadCustomerJobs(customer.id);
          }
          
        },
        function(error) {
          console.error("API Error:", error);
          $("#inboundCallCustomerName").text('Error loading customer data');
        }
      );
    } catch (error) {
      console.error("Error fetching customer data:", error);
      $("#inboundCallCustomerName").text('Error loading customer data');
    }
    
    // Set up accept/reject buttons
    $("#acceptRingBtn").off('click').on('click', (e) => {
      e.preventDefault();
      call.accept();
      self.startCallTimer();
      $("#inboundCallStatus").text("Connected");
      
      // Hide incoming call controls, show hangup button
      $("#incomingCallControls").hide();
      $("#hangupInboundCall").show();
    });

    $("#rejectRingBtn").off('click').on('click', (e) => {
      e.preventDefault();
      call.reject();
      $("#inboundModal").addClass("hidden");
    });
  };

  self.loadCustomerJobs = async function(customerId) {
    try {
      App.ajax(
        BASE_API_URL + '/jobs/get_customer_jobs',
        { customer_id: customerId },
        function(data) {
          if (data.data && data.data.length > 0) {
            const $jobsContainer = $("#inboundCallRecentJobs");
            $jobsContainer.empty();
            
            data.data.forEach(job => {
              const date = new Date(job.created_at).toLocaleDateString();
              const $jobItem = $(`
                <div class="job-item p-2 border-b cursor-pointer hover:bg-gray-100" data-job-id="${job.id}">
                  <div class="flex justify-between">
                    <span class="font-medium">#${job.id}</span>
                    <span class="text-sm text-gray-500">${date}</span>
                  </div>                  
                </div>
              `);
              
              $jobItem.on('click', function() {
                // Highlight selected job
                $(".job-item").removeClass("bg-blue-100");
                $(this).addClass("bg-blue-100");
                
                // Set active job
                self.activeJobId = job.id;
                activeJobId = job.id; // For backward compatibility
                
                // Pre-fill appointment status if available
                if (job.dispatch_status && job.dispatch_status.appointment_status) {
                  $(`input[name='inbound_appointment_status'][value='${job.dispatch_status.appointment_status}']`).prop('checked', true);
                } else {
                  $("input[name='inbound_appointment_status']").prop('checked', false);
                }
              });
              
              $jobsContainer.append($jobItem);
            });
          } else {
            $("#inboundCallRecentJobs").html('<p class="text-gray-500">No recent jobs found</p>');
          }
        },
        function(error) {
          console.error("Error fetching customer jobs:", error);
          $("#inboundCallRecentJobs").html('<p class="text-gray-500">Error loading jobs</p>');
        }
      );
    } catch (error) {
      console.error("Error fetching customer jobs:", error);
      $("#inboundCallRecentJobs").html('<p class="text-gray-500">Error loading jobs</p>');
    }
  };

  self.saveCallNotes = async function(jobId, notes) {
    if (!notes.trim()) {
      self.showDialog("Please enter notes", "warning");
      return;
    }

    // Show loading indicator
    let $saveBtn;
    let originalBtnText;
    
    if ($("#inboundModal").is(":visible")) {
      $saveBtn = $("#saveInboundCallNotes");
    } else if ($("#outboundCallModal").is(":visible")) {
      $saveBtn = $("#saveOutboundCallNotes");
    }
    
    if ($saveBtn) {
      originalBtnText = $saveBtn.text();
      $saveBtn.prop('disabled', true).text('Saving...').addClass('opacity-75');
    }
    
    // Prepare the message data
    const message = {
      job_id: jobId,
      job_comment: notes,
      message_type: 'admin_type'
    };
    
    // Use the same endpoint as the EditAppointmentStatusModule
    const endpoint = `${BASE_API_URL}/jobs/add_employee_comment`;
    
    // Use App.ajaxPostOk instead of fetch
    App.ajaxPostOk(endpoint, message, (data) => {
      if (data.success) {
        self.showDialog("Notes saved successfully", "success");
        
        // Clear the appropriate textarea based on which modal is active
        if ($("#inboundModal").is(":visible")) {
          $("#inboundCallNotes").val('');
        } else if ($("#outboundCallModal").is(":visible")) {
          $("#outboundCallNotes").val('');
        }
        
        // If the appointment modal is open, refresh the notes
        if ($("#editAppointmentStatusModal2").hasClass("active") && typeof EditAppointmentStatusModule !== 'undefined') {
          EditAppointmentStatusModule.loadJobMessages();
        }
      } else {
        self.showDialog("Failed to save notes: " + (data.message || "Unknown error"), "error");
      }
    }, {
      blockEle: $saveBtn ? $saveBtn.closest('.modal') : null,
      complete: () => {
        // Restore button state
        if ($saveBtn) {
          $saveBtn.prop('disabled', false).text(originalBtnText).removeClass('opacity-75');
        }
      }
    });
  };

  self.updateJobStatus = function(jobId, status) {
    const data = {
      job_id: jobId,
      data: { appointment_status: status }
    };
    
    App.ajaxPostOk(
      BASE_API_URL + '/jobs/update_job_status', 
      data,
      (response) => {
        if (response.success) {
          self.showDialog("Appointment status updated successfully", "success");
          // Refresh job list if needed
          if (self.customerId) {
            self.loadCustomerJobs(self.customerId);
          }
        } else {
          self.showDialog("Failed to update status: " + (response.message || "Unknown error"), "error");
        }
      },
      {
        blockEle: $("#inboundModal"),
        complete: () => {
          // Any additional cleanup if needed
        }
      },
      (error) => {
        console.error("Error updating job status:", error);
        self.showDialog("Error updating job status", "error");
      }
    );
  };

  self.makeCallTo = function(phoneNumber) {  
    if (!self.device) {
      alert("Not connected to Twilio. Please connect first.");
      return;
    }

    // Display the phone number in the outbound call modal
    $('#outboundCallNumber').text(phoneNumber);
    
    // If the appointment modal is open, disable its buttons
    if ($("#editAppointmentStatusModal2").hasClass("active")) {
      $("div#editAppointmentStatusModal2 .modal-footer button").prop("disabled", true);
      
      // Display customer name if available
      const customerName = EditAppointmentStatusModule.job?.dispatch_customers?.[0]?.customer?.name;
      if (customerName) {
        $('#outboundCallCustomer').text(customerName);
      }
    }
    
    // Show the outbound call modal
    $("#outboundCallModal").removeClass("hidden");
    
    // Connect to Twilio
    const connection = self.device.connect({ 
      To: phoneNumber,
      CallerId: self.assignedNumber
    });
    
    // Store the current connection
    self.currentConnection = connection;
    currentConnection = connection; // For backward compatibility

    // Handle disconnect event
    connection.on('disconnect', () => {    
      $("#outboundCallModal").addClass("hidden");
      
      // Re-enable buttons in appointment modal
      $("div#editAppointmentStatusModal2 .modal-footer button").prop("disabled", false);
      
      // Clear the notes field
      $("#outboundCallNotes").val('');
      
      // Reset the current connection
      self.currentConnection = null;
      currentConnection = null; // For backward compatibility
    });

    // Handle error event
    connection.on('error', (error) => {    
      self.showDialog("Connection failed: " + error.message, "error");
      $("#outboundCallModal").addClass("hidden");
      
      // Re-enable buttons in appointment modal
      $("div#editAppointmentStatusModal2 .modal-footer button").prop("disabled", false);
      
      // Reset the current connection
      self.currentConnection = null;
      currentConnection = null; // For backward compatibility
    });
  };

  self.showDialog = function(message, style = "success") {
    let bgColor = "bg-green-100 border-green-500 text-green-700";
    
    if (style === "error") {
      bgColor = "bg-red-100 border-red-500 text-red-700";
    } else if (style === "warning") {
      bgColor = "bg-yellow-100 border-yellow-500 text-yellow-700";
    } else if (style === "info") {
      bgColor = "bg-blue-100 border-blue-500 text-blue-700";
    }
    
    const toast = $(`
      <div class="fixed bottom-4 right-4 px-4 py-3 rounded border ${bgColor} z-50">
        <span class="block sm:inline">${message}</span>
        <button class="absolute top-0 bottom-0 right-0 px-4 py-3">
          <svg class="fill-current h-6 w-6" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
            <title>Close</title>
            <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
          </svg>
        </button>
      </div>
    `);
    
    $("body").append(toast);
    
    toast.find("button").on("click", function() {
      toast.remove();
    });
    
    setTimeout(() => {
      toast.remove();
    }, 5000);
  };

  self.showNewJobDialog = function() {
    // Reset form fields
    $("#newJobCustomerName").val("");
    $("#newJobPhoneNumber").val("");
    $("#newJobAddress").val("");
    $("#newJobCity").val("");
    $("#newJobState").val("");
    $("#newJobZip").val("");
    $("#newJobNotes").val("");
    $("#newJobAppointmentDate").val("");
    // $("#newJobAppointmentTime").val("");
    
    // If we have a customer ID, fetch and fill customer information
    if (self.customerId) {
      // Show loading indicator
      const $form = $("#newJobFromCallModal .modal-body");
      $form.addClass("opacity-50");
      $form.append('<div id="loadingOverlay" class="absolute inset-0 flex items-center justify-center"><div class="spinner-border text-primary" role="status"></div></div>');
      
      // Fetch customer data
      App.ajax(
        BASE_API_URL + '/customers/tenants/get_customer',
        { customer_id: self.customerId },
        function(response) {
          if (response.success && response.data) {
            // Remove loading indicator
            $form.removeClass("opacity-50");
            $("#loadingOverlay").remove();
            
            const customer = response.data;
            
            // Fill customer name and phone
            $("#newJobCustomerName").val(customer.name || "");
            
            // Fill phone if available
            if (customer.customer_phones && customer.customer_phones.length > 0) {
              $("#newJobPhoneNumber").val(customer.customer_phones[0].phone || "");
            } else if (self.phoneNumber) {
              // Use phone number from call if available
              $("#newJobPhoneNumber").val(self.phoneNumber);
            }
            
            // Fill address if available
            if (customer.customer_properties && customer.customer_properties.length > 0) {
              const property = customer.customer_properties[0];
              const streetNumber = property.streetNumber || "";
              const streetName = property.streetName || "";
              $("#newJobAddress").val(`${streetNumber} ${streetName}`.trim());
              $("#newJobCity").val(property.city || "");
              $("#newJobState").val(property.state || "");
              $("#newJobZip").val(property.zip || "");
            }
          }
          
          // Remove loading indicator
          $form.removeClass("opacity-50");
          $("#loadingOverlay").remove();
        },
        function(error) {
          console.error("Error fetching customer data:", error);
          self.showDialog("Error loading customer data", "error");
          
          // Remove loading indicator
          $form.removeClass("opacity-50");
          $("#loadingOverlay").remove();
        }
      );
    } else if (self.phoneNumber) {
      // If we don't have a customer ID but have a phone number from the call
      $("#newJobPhoneNumber").val(self.phoneNumber);
    }
    
    // Load technicians into dropdown
    const $techSelect = $("#newJobTech");
    $techSelect.html('<option value="unassigned">(Unassigned)</option>');
    
    // Populate technicians dropdown
    $.each(employeeMapById, function(id, employee) {
      $techSelect.append($("<option>").val(employee.id).text(employee.user_name));
    });
    
    // Show the modal
    $("#newJobFromCallModal").addClass("active");
  };

  self.createNewJob = async function() {
    // Validate required fields
    const customerName = $("#newJobCustomerName").val().trim();
    const phoneNumber = $("#newJobPhoneNumber").val().trim();
    
    if (!customerName) {
      self.showDialog("Customer name is required", "warning");
      return;
    }
    
    if (!phoneNumber) {
      self.showDialog("Phone number is required", "warning");
      return;
    }
    
    // Gather form data
    const jobData = {
      customer_name: customerName,
      phone_number: phoneNumber,
      address: $("#newJobAddress").val().trim(),
      city: $("#newJobCity").val().trim(),
      state: $("#newJobState").val().trim(),
      zip: $("#newJobZip").val().trim(),
      job_notes: $("#newJobNotes").val().trim(),
      assigned_tech: $("#newJobTech").val() || null,
      appointment_date: $("#newJobAppointmentDate").val() || null,
      appointment_time: $("#newJobAppointmentTime").val() || null,
      customer_id: self.customerId,
      new_customer: self.customerId ? 'no' : 'yes'
    };
    
    // Show loading state
    $("#saveNewJob").prop('disabled', true).text('Creating...').addClass('opacity-75');
    
    try {
      // Call API to create new job using App.ajaxPostOk
      App.ajaxPostOk(
        BASE_API_URL + 'jobs/tenants/create_job',
        jobData,
        (response) => {
          if (response.success) {
            self.showDialog("Job created successfully!", "success");
            $("#newJobFromCallModal").removeClass("active");
            
            // If we're in a call, add a note about job creation
            if (self.currentConnection && self.activeJobId) {
              const noteText = `Created new job #${response.job_id} during this call.`;
              self.saveCallNotes(self.activeJobId, noteText);
            }
            
            // Refresh kanboard if it exists
            if (typeof loadKanboard === 'function') {
              loadKanboard();
            }
          } else {
            self.showDialog("Failed to create job: " + (response.message || "Unknown error"), "error");
          }
        },
        {
          blockEle: $("#newJobFromCallModal"),
          blockEleTitle: "Creating job...",
          complete: () => {
            // Reset button state
            $("#saveNewJob").prop('disabled', false).text('Create Job').removeClass('opacity-75');
          }
        },
        (error) => {
          console.error("Error creating job:", error);
          self.showDialog("Error creating job. Please try again.", "error");
          // Reset button state
          $("#saveNewJob").prop('disabled', false).text('Create Job').removeClass('opacity-75');
        }
      );
    } catch (error) {
      console.error("Error in job creation process:", error);
      self.showDialog("Error creating job. Please try again.", "error");
      // Reset button state
      $("#saveNewJob").prop('disabled', false).text('Create Job').removeClass('opacity-75');
    }
  };

  self.initNewJobDialog = function() {
    // Open new job dialog when button is clicked
    $("#createNewJobFromCall").off('click').on('click', function() {
      self.showNewJobDialog();
    });
    
    // Close dialog when cancel button is clicked
    $("#cancelNewJob, #newJobFromCallModal .modal-close").off('click').on('click', function() {
      $("#newJobFromCallModal").removeClass("active");
    });
    
    // Save new job when save button is clicked
    $("#saveNewJob").off('click').on('click', function() {
      self.createNewJob();
    });
  };

  return self;
}();

// For backward compatibility, create global functions that call the module methods
function makeCallTo(phoneNumber) {
  TwilioCallModule.makeCallTo(phoneNumber);
}

function showDialog(message, style = "success") {
  TwilioCallModule.showDialog(message, style);
}

// Initialize the module when the document is ready
$(document).ready(function() {
  TwilioCallModule.init();
});













