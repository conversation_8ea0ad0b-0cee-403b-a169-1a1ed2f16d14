<?php

namespace Butler\Models\Gps;

use Butler\Models\BaseModel;
use Butler\Models\Dispatch\Dispatch;

/**
 * @property integer $dispatch_id
 * @property integer $sms_sent
 * @property string $complete_address
 * @property float $cust_lat
 * @property float $cust_long
 * @property string $details
 * @property string $geofence_polygon
 * @property boolean $completed
 *
 * @property Dispatch $dispatch
 */
class GpsDispatch extends BaseModel
{
    /**
     * The primary key for the model.
     * 
     * @var string
     */
    protected $primaryKey = 'dispatch_id';

    /**
     * Indicates if the IDs are auto-incrementing.
     * 
     * @var bool
     */
    public $incrementing = false;

    /**
     * @var array
     */
    protected $fillable = ['sms_sent', 'complete_address', 'cust_lat', 'cust_long', 'details', 'geofence_polygon', 'completed'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function dispatch()
    {
        return $this->belongsTo('Butler\Models\Gps\Dispatch');
    }
}
